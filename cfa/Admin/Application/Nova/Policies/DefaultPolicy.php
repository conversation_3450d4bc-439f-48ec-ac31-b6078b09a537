<?php

namespace Cfa\Admin\Application\Nova\Policies;

use Cfa\Admin\Application\Nova\Resources\Resource;
use Cfa\Admin\Domain\CmsUser\CmsUser;
use Illuminate\Auth\Access\HandlesAuthorization;
use Laravel\Nova\Actions\Action;

class DefaultPolicy
{
    use HandlesAuthorization;

    public function viewAny(CmsUser $cmsUser): bool
    {
        return $cmsUser->checkRole('admin');
    }

    public function view(CmsUser $cmsUser, Resource $resource): bool
    {
        return false;
    }

    public function runAction(CmsUser $cmsUser, Resource $resource, Action $action): bool
    {
        if ($action->seeCallback) {
            return $this->viewAny($cmsUser);
        }

        return false;
    }
}
