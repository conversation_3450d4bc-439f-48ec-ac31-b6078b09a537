<?php

namespace Cfa\Admin\Application\Nova\Traits;

use Cfa\Common\Domain\User\Career\Career;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

trait NovaRelations
{
    public function relationsForRoles(string $userClass, array $roles, string $table): HasManyThrough
    {
        return $this->hasManyThrough($userClass, Career::class, $table . '_id', 'id', 'id', 'user_id')
            ->withoutGlobalScopes()
            ->select(['careers.user_id', 'careers.' . $table . '_id'])
            ->join('roles', 'roles.id', 'role_id')
            ->whereNull('roles.deleted_at')
            ->whereNull('careers.deleted_at')
            ->whereIn('roles.role_name_enum', $roles)
            ->groupBy(['careers.user_id', 'careers.' . $table . '_id']);
    }
}
