<?php

namespace Cfa\Admin\Application\Nova\Metrics;

use Laravel\Nova\Http\Requests\NovaRequest;
use Override;

class PlannerSchoolsMetric extends StatisticsMetric
{
    /** {@inheritdoc} */
    public $width = '1/3';

    /** {@inheritdoc} */
    public function calculate(NovaRequest $request)
    {
        $cachedStatistics = $this->getCachedStatistics();

        if (isset($cachedStatistics['plannerSchools'])) {
            return $this->result($cachedStatistics['plannerSchools'])
                ->allowZeroResult()
                ->previous($cachedStatistics['plannerSchoolsPrevious']);
        }

        return $this->result(0);
    }

    /** {@inheritdoc} */
    #[Override]
    public function uriKey(): string
    {
        return 'active-planner-schools';
    }

    /** {@inheritdoc} */
    #[Override]
    public function name()
    {
        return trans('nova.metrics.active-planner-schools');
    }
}
