<?php

namespace Cfa\Admin\Application\Nova\Resources;

use Cfa\Admin\Application\Nova\Policies\GroupPolicy;
use Cfa\Common\Domain\School\Group\Group as GroupModel;
use Illuminate\Contracts\Database\Eloquent\Builder;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Override;

use function trans_choice;

class Group extends SmdResource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = GroupModel::class;

    /**
     * The underlying model resource instance.
     *
     * @var GroupModel
     */
    public $resource;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'uid',
        'name',
        'code',
    ];

    /**
     * The number of resources to show per page via relationships.
     *
     * @var int
     */
    public static $perPageViaRelationship = 25;

    public static string $policy = GroupPolicy::class;

    /**
     * {@inheritdoc}
     */
    #[Override]
    protected static function applyOrderings(Builder $query, array $orderings): Builder
    {
        if (empty($orderings)) {
            $orderings = [
                'is_classgroup' => 'desc',
                'target_audience_type' => 'asc',
                'natural_study_year' => 'asc',
            ];
        }

        return parent::applyOrderings($query, $orderings);
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function fields(NovaRequest $request): array
    {
        return array_merge(
            parent::fields($request),
            [
                Text::make(trans('labels.name'), 'name')->sortable(),

                Text::make(trans('labels.code'), 'code')->sortable(),

                Boolean::make(trans('nova.labels.is_classgroup'), 'is_classgroup')->sortable(),

                BelongsTo::make(trans_choice('nova.resources.school', 1), 'school', School::class),

                HasMany::make(trans_choice('nova.resources.staff', 2), 'novaStaff', Staff::class),

                HasMany::make(trans_choice('nova.resources.pupil', 2), 'novaPupils', Pupil::class),
            ],
        );
    }
}
