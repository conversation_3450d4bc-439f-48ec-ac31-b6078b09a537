<?php

namespace Cfa\Admin\Application\Cms\Controllers\Collections\PublisherCollection\Legacy;

use App\Http\Requests\FormRequest;
use Cfa\Planner\Domain\Record\RecordPurpose;
use Override;

class RecordSaveRequest extends FormRequest
{
    private const MAX_URL_SIZE = 'max:500';

    /**
     * Get the validation rules that apply to the request.
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'name' => 'required',
                'duration_minutes' => 'required|integer|min:0',
                'purpose' => 'required|enum:' . RecordPurpose::class,
                'instruction_movie_url_1' => [
                    'nullable',
                    'string',
                    '' . self::MAX_URL_SIZE . '',
                ],
                'instruction_movie_url_2' => [
                    'nullable',
                    'string',
                    self::MAX_URL_SIZE,
                ],
                'instruction_movie_url_3' => [
                    'nullable',
                    'string',
                    self::MAX_URL_SIZE,
                ],
                'boardbook_url' => [
                    'nullable',
                    'string',
                    self::MAX_URL_SIZE,
                ],
            ],
        );
    }
}
