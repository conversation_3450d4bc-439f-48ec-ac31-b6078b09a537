<?php

namespace Cfa\Admin\Application\Cms\Controllers\Collections\PublisherCollection;

use App\Controllers\Controller;
use App\Http\Requests\FormRequest;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Carbon;

use function redirect;
use function route;
use function session;

class PlannerCollectionTogglePublishController extends Controller
{
    public function __construct(
        private readonly PlannerCollectionRepositoryInterface $plannerCollectionRepository,
    ) {}

    public function __invoke(FormRequest $request, PlannerCollection $plannerCollection): RedirectResponse|JsonResponse
    {
        $plannerCollection->published_at = $plannerCollection->published_at ? null : Carbon::now();
        $this->plannerCollectionRepository->save($plannerCollection);

        $isPublished = $plannerCollection->published_at !== null;

        if ($request->expectsJson()) {
            return response()->json(['published' => $isPublished]);
        }

        session()->flash('message', 'Collection ' . ($isPublished ? '' : 'un') . 'published.');

        return redirect(route('web.cms.collections.edit', $plannerCollection->uid));
    }
}
