<?php

namespace Cfa\Admin\Application\Cms\Controllers\Collections\PublisherCollection;

use App\Controllers\Controller;
use Cfa\Planner\Domain\CollectionV2\Chapter\Chapter;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Record;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordData;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\Record\Material\MaterialType;
use Illuminate\Http\RedirectResponse;

use function redirect;
use function request;
use function route;
use function session;

class MaterialDeleteController extends Controller
{
    public function __invoke(
        RecordRepositoryInterface $recordRepository,
        PlannerCollection $plannerCollection,
        Chapter $chapter,
        Record $record,
        int $materialNumber,
    ): RedirectResponse {
        $recordData = RecordData::fromRecord($record);
        $type = MaterialType::fromName(request()->get('type'));

        $existingMaterials = match ($type) {
            MaterialType::File => $recordData->getMaterialFiles(),
            MaterialType::Physical => $recordData->getMaterialPhysicals(),
            MaterialType::Link => $recordData->getMaterialLinks(),
        };

        match ($type) {
            MaterialType::File => $recordData->setMaterialFiles(
                $existingMaterials->forget($materialNumber)->all(),
            ),
            MaterialType::Physical => $recordData->setMaterialPhysicals(
                $existingMaterials->forget($materialNumber)->all(),
            ),
            MaterialType::Link => $recordData->setMaterialLinks(
                $existingMaterials->forget($materialNumber)->all(),
            ),
        };

        $recordRepository->save($record->forceFill($recordData->toArray()));

        session()->flash('message', 'Material deleted.');

        return redirect(
            route(
                'web.cms.records.edit',
                [
                    'collectionv2' => $plannerCollection->uid,
                    'chapterv2' => $chapter->uid,
                    'recordv2' => $record->uid,
                ],
            ),
        );
    }
}
