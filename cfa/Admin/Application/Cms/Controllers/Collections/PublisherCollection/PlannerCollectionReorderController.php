<?php

namespace Cfa\Admin\Application\Cms\Controllers\Collections\PublisherCollection;

use App\Controllers\Controller;
use Cfa\Admin\Application\Cms\Controllers\Collections\PublisherCollection\Legacy\ReorderRequest;
use Cfa\Planner\Domain\CollectionV2\Chapter\ChapterRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Illuminate\Http\JsonResponse;

use function response;

class PlannerCollectionReorderController extends Controller
{
    public function __invoke(
        PlannerCollectionRepositoryInterface $plannerCollectionRepository,
        ChapterRepositoryInterface $chapterRepository,
        ReorderRequest $request,
        PlannerCollection $plannerCollection,
    ): JsonResponse {
        $chapters = $chapterRepository->getAllByPlannerCollection($plannerCollection)->keyBy('uid');

        foreach ($request->get('order') as $order => $chapterId) {
            if ($chapter = $chapters->get($chapterId)) {
                $chapterRepository->setOrder($chapter, $order);
            }
        }

        return response()->json(['success' => true]);
    }
}
