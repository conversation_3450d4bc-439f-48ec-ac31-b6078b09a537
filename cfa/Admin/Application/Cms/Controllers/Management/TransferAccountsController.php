<?php

namespace Cfa\Admin\Application\Cms\Controllers\Management;

use App\Http\Requests\FormRequest;
use Cfa\Admin\Application\Cms\Controllers\CmsController;
use Cfa\Admin\Application\Cms\Services\SyncModels\TransferAccountsService;
use Cfa\Admin\Domain\MergeLog\MergeLogType;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\CalendarItem\CalendarItem;
use Cfa\Planner\Domain\Roster\Roster;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\View\View;

use function redirect;

class TransferAccountsController extends CmsController
{
    /**
     * Switch user data index page.
     *
     * @param Request $request The incoming request.
     */
    public function index(Request $request): View
    {
        $searchparameter = $request->get('search');

        $schools = School::query()
            ->when($searchparameter, function (Builder $query) use ($searchparameter): void {
                $searchparameter = '%' . $searchparameter . '%';

                $query->where('uid', 'like', $searchparameter)
                    ->orWhere('partner_number', 'like', $searchparameter)
                    ->orWhere('name', 'like', $searchparameter);
            })
            ->orderBy('name')
            ->paginate(20);

        return view('cms.transfer-accounts.index', compact('schools', 'searchparameter'));
    }

    /**
     * Page to choose the users within the same school.
     *
     * @param School $school The selected school.
     */
    public function users(School $school): View
    {
        $users = $this->getUsersForSchool($school);

        return view('cms.transfer-accounts.users', compact('school', 'users'));
    }

    /**
     * Page to confirm the users to transfer the data between.
     *
     * @param School $school The selected school.
     * @param Request $request The incoming request.
     */
    public function confirm(School $school, Request $request): View
    {
        $sourceUser = $this->getUserByUid($request->get('sourceUser'), $school);
        $destinationUser = $this->getUserByUid($request->get('destinationUser'), $school);

        return view('cms.transfer-accounts.confirm', compact('school', 'sourceUser', 'destinationUser'));
    }

    /**
     * Transfer the account data from the source user to the destination user.
     *
     * @param School $school The selected school.
     * @param FormRequest $request The incoming request.
     */
    public function transfer(School $school, FormRequest $request): RedirectResponse
    {
        $sourceUser = $this->getUserByUid($request->get('sourceUser'), $school);
        $destinationUser = $this->getUserByUid($request->get('destinationUser'), $school);

        app(TransferAccountsService::class)->transfer(
            $sourceUser,
            $destinationUser,
            MergeLogType::UserTransfer,
        );

        return redirect(route('web.cms.transfer-accounts.completed', [
            'school' => $school->uid,
            'user' => $destinationUser->uid,
        ]));
    }

    /**
     * Page to see the new data of the destination user.
     *
     * @param School $school The selected school.
     * @param User $user The destination user.
     */
    public function completed(School $school, User $user): View
    {
        $user->loadCount([
            'records',
            'usercollections',
            'calendaritems as calendaritems_count' => function (Builder $query) use ($school) {
                return $query->where('school_id', $school->id);
            },
            'rosters as rosters_count' => function (Builder $query) use ($school) {
                return $query->where('school_id', $school->id);
            },
        ]);

        return view('cms.transfer-accounts.completed', compact('school', 'user'));
    }

    /**
     * Searches for the users within a specific school.
     *
     * @param School $school The selected school.
     */
    private function getUsersForSchool(School $school): Collection
    {
        return User::withTrashed()
            ->select('users.*')
            ->selectRaw(
                'CASE WHEN `calendaritems_count` IS NULL THEN 0 ELSE `calendaritems_count` END AS calendaritems_count',
            )
            ->selectRaw(
                'CASE WHEN `rosters_count` IS NULL THEN 0 ELSE `rosters_count` END AS rosters_count',
            )
            ->with([
                'careersWithTrashed' => fn(HasMany $query): HasMany => $query
                    ->where('school_id', $school->id)
                    ->whereNotNull('role_id'),
                'careersWithTrashed.role',
            ])
            ->distinct()
            ->join('careers', 'users.id', '=', 'careers.user_id')
            ->leftJoin('roles', 'roles.id', '=', 'careers.role_id')
            ->leftJoinSub(
                CalendarItem::query()
                    ->select(['owner_id', 'school_id', DB::raw('COUNT(*) AS calendaritems_count')])
                    ->where('school_id', $school->id)
                    ->groupBy('owner_id', 'school_id'),
                'calendaritems_count',
                function (JoinClause $join): void {
                    $join->on('calendaritems_count.owner_id', '=', 'users.id');
                },
            )
            ->leftJoinSub(
                Roster::query()
                    ->select(['owner_id', 'school_id', DB::raw('COUNT(*) AS rosters_count')])
                    ->where('school_id', $school->id)
                    ->groupBy('owner_id', 'school_id'),
                'rosters_count',
                function (JoinClause $join): void {
                    $join->on('rosters_count.owner_id', '=', 'users.id');
                },
            )
            ->where(function (Builder $query): void {
                $query->whereNull('roles.role_name_enum')
                    ->orWhereNotIn('roles.role_name_enum', [RoleName::Pupil->value]);
            })
            ->where('careers.school_id', $school->id)
            ->orderBy('users.firstname')
            ->orderBy('users.lastname')
            ->get()
            ->transform(function (User $user): User {
                $user->allRoles = $user->careersWithTrashed
                    ->map(fn(Career $career) => $career->role->role_name)
                    ->unique();

                return $user;
            });
    }

    /**
     * Returns the user of the given uid.
     *
     * @param string $uid The user uid.
     */
    protected function getUserByUid(string $uid, School $school): ?User
    {
        return User::withTrashed()
            ->with(['careers.school.educationalnetwork', 'careers.role'])
            ->withCount([
                'records',
                'usercollections',
                'calendaritems as calendaritems_count' => function (Builder $query) use ($school) {
                    return $query->where('school_id', $school->id);
                },
                'rosters as rosters_count' => function (Builder $query) use ($school) {
                    return $query->where('school_id', $school->id);
                },
            ])
            ->where('uid', $uid)
            ->first();
    }
}
