<?php

namespace Cfa\Admin\Domain\CmsUser;

use App\Models\DatabaseNotification\DatabaseNotification;
use App\Models\Model;
use Carbon\Carbon;
use Cfa\Planner\Domain\Collection\PublisherCollection\Publisher\Publisher;
use Illuminate\Auth\Authenticatable;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Contracts\Auth\CanResetPassword as CanResetPasswordContract;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Foundation\Auth\Access\Authorizable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;
use Laravel\Fortify\TwoFactorAuthenticatable;
use Override;

/**
 * Cfa\Admin\Domain\CmsUser\CmsUser
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property int|null $publisher_id Limit user to a specific publisher
 * @property string|null $first_name
 * @property string|null $last_name
 * @property string $email
 * @property string $password
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property string|null $two_factor_confirmed_at
 * @property CmsUserRole $role The cms user role, see CmsUserRole enum.
 * @property string|null $remember_token
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $google2fa_secret
 * @property bool $google2fa_enabled
 * @property string $locale
 * @property-read string $name
 * @property-read array $validation_rules
 * @property-read DatabaseNotificationCollection<int,DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read Publisher|null $publisher
 * @method static CmsUserFactory factory($count = null, $state = [])
 * @method static Builder|CmsUser newModelQuery()
 * @method static Builder|CmsUser newQuery()
 * @method static Builder|CmsUser query()
 * @method static Carbon|null randomCreatedAt()
 * @method static string randomEmail()
 * @method static string|null randomFirstName()
 * @method static bool randomGoogle2faEnabled()
 * @method static string|null randomGoogle2faSecret()
 * @method static int randomId()
 * @method static string|null randomLastName()
 * @method static string randomLocale()
 * @method static int|null randomPublisherId()
 * @method static CmsUserRole randomRole()
 * @method static string|null randomTwoFactorConfirmedAt()
 * @method static string|null randomTwoFactorRecoveryCodes()
 * @method static string|null randomTwoFactorSecret()
 * @method static Carbon|null randomUpdatedAt()
 * @method static Builder|CmsUser whereCreatedAt($value)
 * @method static Builder|CmsUser whereEmail($value)
 * @method static Builder|CmsUser whereFirstName($value)
 * @method static Builder|CmsUser whereGoogle2faEnabled($value)
 * @method static Builder|CmsUser whereGoogle2faSecret($value)
 * @method static Builder|CmsUser whereId($value)
 * @method static Builder|CmsUser whereLastName($value)
 * @method static Builder|CmsUser whereLocale($value)
 * @method static Builder|CmsUser wherePassword($value)
 * @method static Builder|CmsUser wherePublisherId($value)
 * @method static Builder|CmsUser whereRememberToken($value)
 * @method static Builder|CmsUser whereRole($value)
 * @method static Builder|CmsUser whereTwoFactorConfirmedAt($value)
 * @method static Builder|CmsUser whereTwoFactorRecoveryCodes($value)
 * @method static Builder|CmsUser whereTwoFactorSecret($value)
 * @method static Builder|CmsUser whereUpdatedAt($value)
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class CmsUser extends Model implements
    AuthenticatableContract,
    AuthorizableContract,
    CanResetPasswordContract
{
    use Notifiable;
    use Authenticatable;
    use Authorizable;
    use CanResetPassword;
    use TwoFactorAuthenticatable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'email',
        'first_name',
        'last_name',
    ];

    /** @var string[] */
    protected $dispatchesEvents = [
        'creating' => CmsUserCreatingEvent::class,
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    /**
     * {@inheritdoc}
     *
     * @var array
     */
    protected $casts = [
        'publisher_id' => 'integer',
        'google2fa_enabled' => 'bool',
        'role' => CmsUserRole::class,
    ];

    /**
     * The publisher the user belongs to.
     */
    public function publisher(): BelongsTo
    {
        return $this->belongsTo(Publisher::class);
    }

    /**
     * Check if user has required role or is admin.
     *
     * @param string $role Name of the role to check for.
     */
    public function checkRole(string $role): bool
    {
        // Admin is always allowed or matching role.
        return $this->role == CmsUserRole::Admin || $this->role == CmsUserRole::fromName($role);
    }

    /**
     * Check if user has permission for an action or is admin.
     *
     * @param CmsUserPermission $permission Permission to check for.
     */
    public function checkPermission(CmsUserPermission $permission): bool
    {
        // Admin is always allowed or matching role.
        return $this->role == CmsUserRole::Admin || $this->role->hasPermission($permission);
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'email' => [
                    'required',
                    'email',
                    Rule::unique('cms_users')->ignore($this->id),
                ],
                'role' => 'required',
                'first_name' => 'required',
                'last_name' => 'required',
                'password' => [
                    'required',
                    'string',
                    Password::min(8),
                ],
                'publisher_id' => [
                    'nullable',
                    Rule::exists('publishers', 'id'),
                    'required_if:role,' . CmsUserRole::Author->value,
                ],
                'google2fa_enabled' => 'boolean',
                'locale' => [
                    'required',
                    'string',
                    'in:nl,en',
                ],
            ],
        );
    }

    /**
     * Make the name attribute available for nova.
     */
    public function getNameAttribute(): string
    {
        return $this->first_name;
    }
}
