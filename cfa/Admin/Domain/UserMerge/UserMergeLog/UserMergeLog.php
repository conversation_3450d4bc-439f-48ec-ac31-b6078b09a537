<?php

namespace Cfa\Admin\Domain\UserMerge\UserMergeLog;

use App\Models\Model;
use Cfa\Admin\Application\Cms\Traits\TouchedByCmsUser;
use Cfa\Admin\Domain\CmsUser\CmsUser;
use Cfa\Admin\Domain\UserMerge\UserMerge;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * Cfa\Admin\Domain\UserMerge\UserMergeLog\UserMergeLog
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $model The class path to the Model where changes have been made.
 * @property string $field_name The name of the field where changes have been made.
 * @property string $ids List of ids where changes have been made.
 * @property int $user_merge_id The user merge.
 * @property int|null $creator_id
 * @property int|null $updater_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read CmsUser|null $creator
 * @property-read array $validation_rules
 * @property-read CmsUser|null $updater
 * @property-read UserMerge $userMerge
 * @method static Builder|UserMergeLog newModelQuery()
 * @method static Builder|UserMergeLog newQuery()
 * @method static Builder|UserMergeLog query()
 * @method static Carbon|null randomCreatedAt()
 * @method static int|null randomCreatorId()
 * @method static string randomFieldName()
 * @method static int randomId()
 * @method static string randomIds()
 * @method static string randomModel()
 * @method static Carbon|null randomUpdatedAt()
 * @method static int|null randomUpdaterId()
 * @method static int randomUserMergeId()
 * @method static Builder|UserMergeLog whereCreatedAt($value)
 * @method static Builder|UserMergeLog whereCreatorId($value)
 * @method static Builder|UserMergeLog whereFieldName($value)
 * @method static Builder|UserMergeLog whereId($value)
 * @method static Builder|UserMergeLog whereIds($value)
 * @method static Builder|UserMergeLog whereModel($value)
 * @method static Builder|UserMergeLog whereUpdatedAt($value)
 * @method static Builder|UserMergeLog whereUpdaterId($value)
 * @method static Builder|UserMergeLog whereUserMergeId($value)
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class UserMergeLog extends Model
{
    use TouchedByCmsUser;

    /**
     * Get the Usermerge linked to the log.
     */
    public function userMerge(): BelongsTo
    {
        return $this->belongsTo(UserMerge::class);
    }
}
