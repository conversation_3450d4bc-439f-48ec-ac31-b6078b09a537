<?php

namespace Cfa\Admin\Domain\UserMerge;

use App\Models\Model;
use Cfa\Admin\Application\Cms\Traits\TouchedByCmsUser;
use Cfa\Admin\Domain\CmsUser\CmsUser;
use Cfa\Admin\Domain\UserMerge\UserMergeLog\UserMergeLog;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;

/**
 * Cfa\Admin\Domain\UserMerge\UserMerge
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property int $main_user_id The user we will merge the other user into.
 * @property int $other_user_id The user we merge into the main user.
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property int|null $creator_id
 * @property int|null $updater_id
 * @property-read CmsUser|null $creator
 * @property-read array $validation_rules
 * @property-read CmsUser|null $updater
 * @property-read Collection<int, UserMergeLog> $userMergeLogs
 * @property-read int|null $user_merge_logs_count
 * @method static Builder|UserMerge newModelQuery()
 * @method static Builder|UserMerge newQuery()
 * @method static Builder|UserMerge query()
 * @method static Carbon|null randomCreatedAt()
 * @method static int|null randomCreatorId()
 * @method static int randomId()
 * @method static int randomMainUserId()
 * @method static int randomOtherUserId()
 * @method static Carbon|null randomUpdatedAt()
 * @method static int|null randomUpdaterId()
 * @method static Builder|UserMerge whereCreatedAt($value)
 * @method static Builder|UserMerge whereCreatorId($value)
 * @method static Builder|UserMerge whereId($value)
 * @method static Builder|UserMerge whereMainUserId($value)
 * @method static Builder|UserMerge whereOtherUserId($value)
 * @method static Builder|UserMerge whereUpdatedAt($value)
 * @method static Builder|UserMerge whereUpdaterId($value)
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class UserMerge extends Model
{
    use TouchedByCmsUser;

    /**
     * Get logs linked to this userMerge.
     */
    public function userMergeLogs(): HasMany
    {
        return $this->hasMany(UserMergeLog::class);
    }
}
