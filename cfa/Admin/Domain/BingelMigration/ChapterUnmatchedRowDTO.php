<?php

namespace Cfa\Admin\Domain\BingelMigration;

class ChapterUnmatchedRowDTO
{
    public function __construct(
        public string $uid,
        public string $name,
        public string $collection,
    ) {}

    public function toArray(): array
    {
        return [
            'id' => $this->uid,
            'name' => $this->name,
            'collection' => $this->collection,
        ];
    }
}
