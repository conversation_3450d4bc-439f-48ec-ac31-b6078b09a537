<?php

namespace Cfa\Admin\Domain\BingelMigration;

use Cfa\Admin\Application\BingelMigration\Services\BingelMigrationLookupService;

class CollectionTmsModel
{
    private array $collection;
    private BingelMigrationLookupService $lookupService;
    private ?string $parsedName = null;

    public function __construct(array $collection, BingelMigrationLookupService $lookupService)
    {
        $this->collection = $collection;
        $this->lookupService = $lookupService;
        $this->parsedName = $this->lookupService->parseName($this->collection['collection_name']);
    }

    public function getId(): string
    {
        return $this->collection['planner_collection_id'];
    }

    public function getName(): string
    {
        return $this->parsedName;
    }

    public function getChapters(): ?string
    {
        return $this->collection['chapters'] ?? null;
    }
}
