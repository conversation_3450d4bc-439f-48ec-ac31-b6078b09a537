<?php

namespace Cfa\Evaluation\Application\Controllers\Settings\Report\QuotationSettings\Bulk;

use App\Http\Response\NoContentResponse;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Illuminate\Http\Response;

class SubjectQuotationSettingCommentSaveController extends SubjectQuotationSettingsBulkController
{
    public function __invoke(
        SubjectQuotationSettingCommentSaveRequest $request,
        ReportSettings $reportSettings,
        Subject $subject,
    ): Response {
        $attributes = [
            'has_comment' => $request->get('hasComment'),
        ];

        $this->updateOrCreateSubjectQuotationSettings($reportSettings, $subject, $attributes);

        return new NoContentResponse();
    }
}
