<?php

namespace Cfa\Evaluation\Application\Controllers\Settings\Report\EvaluationSubjectPermission;

use App\Controllers\Controller;
use Cfa\Common\Application\Repositories\SettingsGroupRepository;
use Cfa\Evaluation\Domain\Settings\EvaluationSubjectPermission\EvaluationSubjectPermission;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;

class EvaluationSubjectPermissionIndexController extends Controller
{
    public function __invoke(
        ReportSettings $reportSettings,
        SettingsGroupRepository $groupRepository,
    ): View {
        $school = school();
        $user = Auth::user();
        $groups = $groupRepository->getGroupsByTargetAudiences(
            $reportSettings->targetAudiences,
            $user,
            $school,
            null,
        );

        $groupStaff = $groupRepository->getUsersWithAccessToGroups($school, $groups);

        $subjectPermissions = EvaluationSubjectPermission::getRepository()
            ->getAllForGroupsInSchool($groups, $school);

        $saveUrl = route('web.settings.evaluation.reports.report-setting.subjects-permissions.save', [
            'subject' => '#subjectUid#',
            'group' => '#groupUid#',
            'reportSettings' => $reportSettings->uid,
        ]);

        $fetchUrl = route(
            'web.settings.evaluation.reports.report-setting.subject-permissions.fetch',
            ['reportSettings' => $reportSettings->uid],
        );
        $bulkUrl = route(
            'web.settings.evaluation.reports.report-setting.subject-permissions.bulk',
            ['reportSettings' => $reportSettings->uid],
        );

        return view('settings.evaluation.report.step-subject-permissions', [
            'groups' => $groups,
            'staff' => $groupStaff,
            'evaluationSubjectPermissions' => $subjectPermissions,
            'saveUrl' => $saveUrl,
            'fetchUrl' => $fetchUrl,
            'bulkUrl' => $bulkUrl,
            'currentStep' => 4,
            'pageTitle' => $this->getPageTitle($reportSettings),
        ]);
    }

    private function getPageTitle(ReportSettings $reportSettings): string
    {
        return trans('titles.report.report') . ': ' .
            $reportSettings->report_name . ' - ' .
            trans('labels.report.breadcrumbs.step-subjects-permissions');
    }
}
