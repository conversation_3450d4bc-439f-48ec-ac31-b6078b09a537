<?php

namespace Cfa\Evaluation\Application\Controllers\Settings\Report\EvaluationSubjectPermission\Bulk;

use App\Models\CheckboxState\CheckboxState;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\Settings\EvaluationSubjectPermission\EvaluationSubjectPermission;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class EvaluationSubjectPermissionBulkController extends EvaluationSubjectPermissionController
{
    private const FLATTEN_DEPTH = 1;

    public function __invoke(
        EvaluationSubjectPermissionBulkRequest $request,
        ReportSettings $reportSettings,
    ): JsonResponse {
        $school = school();

        $groups = $this->getGroups($reportSettings, Auth::user(), $school, $request->getGroup());
        $subjects = $this->getSubjects($request->get(EvaluationSubjectPermissionBulkRequest::SUBJECT), $school);
        $userIds = $this->getUsers($request->get(EvaluationSubjectPermissionBulkRequest::USERS));
        $usersToIgnoreIds = $this->getUsers($request->get(EvaluationSubjectPermissionBulkRequest::USERS_TO_IGNORE));
        $classTeacherState = $request->get(EvaluationSubjectPermissionBulkRequest::CLASS_TEACHER);

        $subjectPermissions = EvaluationSubjectPermission::query()
            ->whereIn('group_id', $groups->pluck('id'))
            ->whereIn('subject_id', $subjects->pluck('id'))
            ->get()
            ->keyBy(fn($permission): string => $permission->group_id . ':' . $permission->subject_id);

        if ($classTeacherState !== CheckboxState::Partial->value) {
            EvaluationSubjectPermission::whereIn('id', $subjectPermissions->pluck('id'))->update([
                'class_teacher' => $classTeacherState === CheckboxState::On->value,
            ]);
        }

        $subjectPermissionsToAdd = $groups
            ->map(function (Group $group) use ($subjects, $classTeacherState, $subjectPermissions) {
                return $subjects
                    ->reject(fn(Subject $subject): bool => $subjectPermissions->has($group->id . ':' . $subject->id))
                    ->map(fn(Subject $subject): array => [
                        'uid' => uuid(),
                        'group_id' => $group->id,
                        'subject_id' => $subject->id,
                        'class_teacher' => $classTeacherState === CheckboxState::On->value,
                    ]);
            })
            ->flatten(self::FLATTEN_DEPTH);

        EvaluationSubjectPermission::insert($subjectPermissionsToAdd->toArray());

        DB::table('evaluation_subject_permission_user')
            ->whereIn('evaluation_subject_permission_id', $subjectPermissions->pluck('id'))
            ->when(
                $usersToIgnoreIds !== null,
                fn(Builder $query): Builder => $query->whereNotIn('user_id', $usersToIgnoreIds),
            )
            ->delete();

        if ($userIds !== null) {
            $usersToInsert = EvaluationSubjectPermission::query()
                ->whereIn('group_id', $groups->pluck('id'))
                ->whereIn('subject_id', $subjects->pluck('id'))
                ->pluck('id')
                ->map(function (int $subjectPermissionId) use ($userIds) {
                    return $userIds->map(fn(int $userId): array => [
                        'evaluation_subject_permission_id' => $subjectPermissionId,
                        'user_id' => $userId,
                    ]);
                })
                ->flatten(self::FLATTEN_DEPTH);

            DB::table('evaluation_subject_permission_user')->insert($usersToInsert->toArray());
        }

        Subject::getRepository()->flushCacheForSchool($school);

        return $this->respondNoContent();
    }

    /** @return User[]|Collection|null */
    private function getUsers(?array $userUids): ?Collection
    {
        if (empty($userUids)) {
            return null;
        }

        return User::whereIn('uid', $userUids)->pluck('id');
    }
}
