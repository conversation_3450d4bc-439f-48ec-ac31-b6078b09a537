<?php

namespace Cfa\Evaluation\Application\Controllers\ReportPrint;

use App\Controllers\Controller;
use Cfa\Common\Application\Exports\ExportJobStatus;
use Cfa\Common\Domain\School\Group\GroupRepositoryInterface;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\Report\EvaluationPrintJob\EvaluationPrintJob;
use Cfa\Evaluation\Domain\Report\EvaluationPrintJob\EvaluationPrintJobType;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Illuminate\Http\JsonResponse;

use function cloudfront_url;
use function group;
use function school;

class GroupPrintStatusController extends Controller
{
    public function __invoke(
        ReportPeriod $reportPeriod,
        GroupRepositoryInterface $groupRepository,
    ): JsonResponse {
        $school = school();
        $group = group();

        $jobs = EvaluationPrintJob::where('school_id', $school->id)
            ->where('group_id', $group->id)
            ->where('report_period_id', $reportPeriod->id)
            ->get()
            ->keyBy(fn(EvaluationPrintJob $job): string => $job->type->value . $job->pupil_id);
        $groupJob = $jobs->get(EvaluationPrintJobType::default->value);
        $groupOverviewJob = $jobs->get(EvaluationPrintJobType::groupOverview->value);

        // We want to map over all existing pupils because for some the report might not have a job yet.
        $pupils = $groupRepository->getPupilsOfGroup($group, $reportPeriod->schoolyear);

        return $this->respond(
            [
                'pupils' => $pupils->map(function (Pupil $pupil) use ($jobs) {
                    $job = $jobs->get(EvaluationPrintJobType::default->value . $pupil->id);

                    return [
                        'pupilName' => $pupil->fullname,
                        'pupilUid' => $pupil->uid,
                        'status' => $job?->status ?? ExportJobStatus::NotExisting,
                        'file' => $this->getFile($job?->file),
                        'disabled' => $pupil->careInfo->pupil_status->isDisabled(),
                    ];
                })->values(),
                'group' => [
                    'groupName' => $group->name,
                    'groupUid' => $group->uid,
                    'status' => $groupJob?->status ?? ExportJobStatus::NotExisting,
                    'file' => $this->getFile($groupJob?->file),
                ],
                'groupOverview' => [
                    'groupName' => $group->name,
                    'groupUid' => $group->uid,
                    'status' => $groupOverviewJob?->status ?? ExportJobStatus::NotExisting,
                    'file' => $this->getFile($groupOverviewJob?->file),
                ],
            ],
        );
    }

    private function getFile(?string $filePath): ?string
    {
        return $filePath ? cloudfront_url($filePath) : null;
    }
}
