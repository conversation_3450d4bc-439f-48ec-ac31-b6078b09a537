<?php

namespace Cfa\Evaluation\Application\Controllers\Report;

use App\Constants\Exports;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Application\Services\Report\EvaluationReportJobStatusService;
use Cfa\Evaluation\Domain\Report\Calculation\ReportTotal;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;

use function group;

class ReportSynthesisAveragesController extends AbstractReportController
{
    public function __invoke(
        Pupil $pupil,
        EvaluationReportJobStatusService $evaluationReportJobStatusService,
    ): JsonResponse {
        $group = group();

        $jobsFinished = $evaluationReportJobStatusService->isCalculationReadyForGroup($group);

        $state = $jobsFinished ? Exports::STATE_FINISHED : Exports::STATE_PROCESSING;
        $data = $jobsFinished ? $this->getReportData($group, $pupil) : [];

        return $this->respond(['state' => $state, 'data' => $data]);
    }

    private function getReportData(Group $group, Pupil $pupil): Collection
    {
        return ReportTotal::query()
            ->with('subject:id,uid', 'reportPeriod:id,uid,parent_id')
            ->where('school_id', $group->school_id)
            ->where('group_id', $group->id)
            ->where('pupil_id', $pupil->id)
            ->whereNotNull(['average', 'median'])
            ->get()
            ->reject(fn(ReportTotal $reportTotal): bool => $reportTotal->reportPeriod === null)
            ->groupBy(
                fn(ReportTotal $reportTotal): string => $reportTotal->reportPeriod->parent_id === null ?
                    'total' : $reportTotal->reportPeriod->uid,
            )
            ->map
            ->mapWithKeys(
                fn(ReportTotal $reportTotal) => [
                    ($reportTotal->subject_id === null ? 'total' : $reportTotal->subject->uid) => [
                        'average' => $reportTotal->average,
                        'median' => $reportTotal->median,
                    ],
                ],
            );
    }
}
