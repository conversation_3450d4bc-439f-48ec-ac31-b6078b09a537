<?php

namespace Cfa\Evaluation\Application\Controllers\Report;

use Cfa\Common\Domain\School\Group\GroupRepositoryInterface;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Application\Services\Report\ShowRedicodisOnReport;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\Report\FollowUpSystem\Report;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriodRepositoryInterface;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\Group\ReportGroupSettings;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\Group\ReportGroupSettingsResource;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;
use stdClass;

use function route;

class ReportFollowUpSystemController extends AbstractReportController
{
    public function __construct(
        GroupRepositoryInterface $groupRepository,
        ReportPeriodRepositoryInterface $reportPeriodRepository,
        SchoolyearRepositoryInterface $schoolyearRepository,
    ) {
        parent::__construct($groupRepository, $schoolyearRepository, $reportPeriodRepository);
    }

    public function __invoke(
        ReportPeriod $reportPeriod,
        Pupil $pupil,
    ): Response {
        $group = group();
        $school = school();
        $user = Auth::user();
        $routeName = 'web.evaluation.report.follow-up-system';
        $routeParameters = ['reportPeriod' => $reportPeriod, 'pupil' => $pupil];

        $this->setTabs($pupil, $reportPeriod);
        $this->setGroupSwitcherData($user, $school, ['reportPeriod' => $reportPeriod, 'tab' => 'follow-up-system']);

        $reportGroupSettings = ReportGroupSettings::where('group_id', $group->id)->first();
        $reportSettings = ReportSettings::getRepository()->getForGroup($group, $reportPeriod->schoolyear);
        $report = new Report($pupil, $group, $reportPeriod, $reportGroupSettings)
            ->forTeacher($user)
            ->showAllFollowUpSystems()
            ->toArray();

        return Inertia::render('Evaluation/Report/FollowUpSystems', [
            'followUpSystems' => $report['followUpSystems']->map->makeVisible('icon'),
            'comments' => $report['comments']->isEmpty() ? new stdClass() : $report['comments'],
            'pupils' => $this->getPupils($group, $routeName, $routeParameters),
            'periodTabs' => $this->getReportPeriods($group, $routeName, $routeParameters),
            'pupil' => $pupil->only(['fullname', 'uid']),
            'reportPeriod' => $reportPeriod,
            'quotationsPerFollowUpSystem' => $report['quotations'],
            'reportGroupSettings' => new ReportGroupSettingsResource($reportGroupSettings)->toArray(request()),
            'pupilSearchAutocompleteUrl' => route('web.common.pupils.search'),
            'printToggleAllFollowUpSystemsUrl' => route('web.evaluation.report.follow-up-systems.print-toggle-all', [
                'reportPeriod' => $reportPeriod,
            ]),
            'printToggleFollowUpSystemUrl' => route('web.evaluation.report.follow-up-systems.print-toggle', [
                'reportPeriod' => $reportPeriod,
                'followUpSystem' => '#followUpSystemUid#',
            ]),
            'filterToggleFollowUpSystemUrl' => route('web.evaluation.report.follow-up-systems.filter-toggle', [
                'reportPeriod' => $reportPeriod,
                'followUpSystem' => '#followUpSystemUid#',
                'pupil' => $pupil,
            ]),
            'commentBaseUrl' => route('web.evaluation.report.follow-up-system.comment', [
                'reportPeriod' => $reportPeriod->uid,
                'pupil' => $pupil->uid,
                'followUpSystem' => '#followUpSystemUid#',
            ]),
            'canEditComments' => $reportPeriod->isExpired() === false && $reportPeriod->schoolyear->isCurrent(),
            'inputMomentCommentBaseUrl' => route('web.common.follow-up-systems.goals.comment.store', [
                'followUpSystem' => '#followUpSystemUid#',
                'pupil' => $pupil->uid,
                'followUpSystemGoal' => '#followUpSystemGoalUid#',
                'followUpSystemInputMoment' => '#followUpSystemInputMoment#',
            ]),
            'printToggleRedicodisUrl' => route('web.evaluation.report.print-toggle-redicodis', [
                'reportPeriod' => $reportPeriod,
            ]),
            'redicodisFeatureEnabled' => app(ShowRedicodisOnReport::class)($reportSettings),
            'toggleRedicodiShowOnReportUrl' => route('web.evaluation.report.redicodis.redicodi.print-toggle', [
                'pupil' => $pupil,
                'reportPeriod' => $reportPeriod,
                'redicodi' => '#redicodi#',
            ]),
            'scoreCommentBaseUrl' => route('web.common.follow-up-systems.predefined.comment', [
                'followUpSystem' => '#followUpSystemUid#',
                'followUpSystemSubType' => FollowUpSystemSubType::Default->value,
                'pupil' => '#pupilUid#',
            ]),
        ]);
    }
}
