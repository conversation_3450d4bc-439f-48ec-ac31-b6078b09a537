<?php

namespace Cfa\Evaluation\Application\Controllers\Evaluation\EvaluationTest;

use App\Controllers\Controller;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Evaluation\Application\Services\Report\EvaluationReportJobsService;
use Cfa\Evaluation\Domain\EvaluationTest\EvaluationTest;
use Cfa\Evaluation\Domain\EvaluationTest\Score\EvaluationTestScore;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Illuminate\Http\JsonResponse;

use function app;

class EvaluationTestDeleteController extends Controller
{
    public function __invoke(
        ReportPeriod $reportPeriod,
        Subject $subject,
        EvaluationTest $evaluationTest,
    ): JsonResponse {
        EvaluationTestScore::whereEvaluationTestId($evaluationTest->id)->delete();
        $evaluationTest->delete();
        app(EvaluationReportJobsService::class)->createForReportPeriod(
            $evaluationTest->school,
            $evaluationTest->group,
            $reportPeriod,
        );

        return $this->respondNoContent();
    }
}
