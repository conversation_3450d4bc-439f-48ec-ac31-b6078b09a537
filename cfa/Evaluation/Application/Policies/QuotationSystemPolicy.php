<?php

namespace Cfa\Evaluation\Application\Policies;

use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\QuotationSystem\QuotationSystem;

class QuotationSystemPolicy
{
    /**
     * The user may edit the quotation system when it was not used for evaluations.
     */
    public function edit(User $user, QuotationSystem $quotationSystem): bool
    {
        return !$quotationSystem->has_evaluations;
    }
}
