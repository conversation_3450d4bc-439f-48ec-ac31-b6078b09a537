<?php

namespace Cfa\Evaluation\Application\Repositories\Report\Subject;

use Cfa\Common\Domain\Subject\Subject;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\Subject\SubjectReportSettings;

class SubjectReportSettingsSaveOrderRepository
{
    private readonly SubjectReportRepository $subjectReportRepository;

    public function __construct(
        private readonly ReportSettings $reportSettings,
    ) {
        $this->subjectReportRepository =
            app(SubjectReportRepository::class, ['reportSettings' => $this->reportSettings]);
    }

    public function saveOrder(Subject $subject, ?int $order): void
    {
        SubjectReportSettings::forceUpdateOrCreate([
            'report_setting_id' => $this->reportSettings->id,
            'subject_id' => $subject->id,
        ], ['order' => $order]);
        $this->subjectReportRepository->forgetCacheKey($this->reportSettings);
    }
}
