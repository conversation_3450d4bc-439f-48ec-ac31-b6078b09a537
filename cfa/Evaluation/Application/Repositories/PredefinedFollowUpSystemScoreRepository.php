<?php

namespace Cfa\Evaluation\Application\Repositories;

use App\Models\Model;
use App\Repositories\Repository;
use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Application\Services\PredefinedFollowUpSystem\DeterminePredefinedFollowupSystemTestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Comment\PredefinedFollowUpSystemComment;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score\PredefinedFollowUpSystemScore;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score\PredefinedFollowUpSystemScoreRepositoryInterface;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;
use Cfa\Wisa\Domain\CareData\WisaCareData;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Override;

use function array_keys;
use function collect;
use function config;
use function max;
use function min;

// phpcs:ignore
class PredefinedFollowUpSystemScoreRepository extends Repository implements PredefinedFollowUpSystemScoreRepositoryInterface
{
    private ?Collection $wisaCareDataByPupilId;

    #[Override]
    public function getModel(): Model
    {
        return new PredefinedFollowUpSystemScore();
    }

    #[Override]
    public function getPupilsWithScores(
        Collection $pupils,
        Group $group,
        PredefinedFollowUpSystem $followUpSystem,
        FollowUpSystemSubType $followUpSystemSubType,
        ?Schoolyear $schoolyear = null,
    ): Collection {
        $this->wisaCareDataByPupilId = null;
        $schoolyear ??= Schoolyear::getRepository()->getCurrent();

        $allScores = $this->getAllData(
            PredefinedFollowUpSystemScore::class,
            $pupils,
            $group,
            $followUpSystem,
            $followUpSystemSubType,
            $schoolyear,
        );
        $allComments = $this->getAllData(
            PredefinedFollowUpSystemComment::class,
            $pupils,
            $group,
            $followUpSystem,
            $followUpSystemSubType,
            $schoolyear,
            ['creator', 'updater'],
        );

        $defaultTestMoments = collect(
            config('follow-up-systems.' . $followUpSystem->type->value . '.general.testMoments'),
        );

        $isVclb = $followUpSystem->type->isLvsVclb();

        return $pupils->map(
            fn(Pupil $pupil): array => $this->mapPupil(
                $pupil,
                $followUpSystem,
                $followUpSystemSubType,
                $allScores,
                $defaultTestMoments,
                $group,
                $isVclb,
                $pupils,
                $allComments,
            ),
        )->values();
    }

    private function getAllData(
        string $model,
        Collection $pupils,
        Group $group,
        PredefinedFollowUpSystem $followUpSystem,
        FollowUpSystemSubType $followUpSystemSubType,
        ?Schoolyear $schoolyear = null,
        array $with = [],
    ): Collection {
        $testAudience = config('follow-up-systems.' . $followUpSystem->type->value . '.general.testAudiences')[0];
        $hasSingleScore = config(
            'follow-up-systems.' . $followUpSystem->type->value . '.general.hasSingleScore',
            false,
        );
        $isTargetAudienceBased = TestAudience::from($testAudience)->isTargetAudienceBased();

        return $model::with($with)
            ->where('school_id', $group->school->id)
            ->when(
                !$hasSingleScore,
                fn($query) => $query->where('schoolyear_id', $schoolyear->id),
            )
            ->where('predefined_follow_up_system_id', $followUpSystem->id)
            ->whereIn('pupil_id', $pupils->pluck('id'))
            ->where('subtype', $followUpSystemSubType)
            ->when(
                $isTargetAudienceBased && !$hasSingleScore,
                fn(Builder $builder): Builder =>
                   $builder->where('test_audience', TestAudience::fromGroup($group)),
            )
            ->orderByDesc('schoolyear_id')
            ->orderByDesc('date')
            ->get()
            ->groupBy('pupil_id');
    }

    private function mapPupil(
        Pupil $pupil,
        FollowUpSystem $followUpSystem,
        FollowUpSystemSubType $followUpSystemSubType,
        Collection $allScores,
        Collection $defaultTestMoments,
        Group $group,
        bool $isVclb,
        Collection $pupils,
        Collection $allComments,
    ): array {
        $pupilScores = $allScores->get($pupil->id);
        $pupilComments = $allComments->get($pupil->id);
        $wisaCareData = $this->getWisaCareDataForPupil($pupils, $group, $pupil);
        $testAudience = $pupilScores ? $pupilScores->first()->test_audience :
            (new DeterminePredefinedFollowupSystemTestAudience())(
                $followUpSystem,
                $pupil,
                $group,
                $wisaCareData,
            );

        $testMoments = collect();
        if ($testAudience instanceof TestAudience) {
            $testAudience = $testAudience->value;
            $testMoments = collect(
                config(
                    'follow-up-systems.' . $followUpSystem->type->value .
                    '.percentile.' . $followUpSystemSubType->value . '.' . $testAudience,
                    [],
                ),
            )->keys();
        }

        if ($testMoments->isEmpty()) {
            $testMoments = $defaultTestMoments;
        }

        /** @var CareInfo $careInfo */
        $careInfo = $pupil->careInfos->where('school_id', $group->school_id)->first();

        $mappedTestMoments = $this->mapTestMoments(
            $testMoments,
            $isVclb && $careInfo->has_dutch_as_home_language === false,
            $followUpSystem,
            $followUpSystemSubType,
            $testAudience,
            $pupilScores,
            $pupilComments,
        );

        return [
            'uid' => $pupil->uid,
            'name' => $pupil->fullname,
            'date_of_birth' => $wisaCareData->date_of_birth ??
                    $careInfo->date_of_birth ?? $pupil->date_of_birth,
            'disabled' => $careInfo->pupil_status->isDisabled(),
            'has_dutch_as_home_language' => $careInfo->has_dutch_as_home_language,
            'test_audience' => $testAudience,
            'test_moments' => $mappedTestMoments,
        ];
    }

    private function mapTestMoments(
        Collection $testMoments,
        bool $showNnScore,
        FollowUpSystem $followUpSystem,
        FollowUpSystemSubType $followUpSystemSubType,
        ?string $testAudience,
        ?Collection $pupilScores,
        ?Collection $pupilComments,
    ): Collection {
        return $testMoments->map(
            function (string $testMoment) use (
                $pupilScores,
                $showNnScore,
                $followUpSystem,
                $followUpSystemSubType,
                $testAudience,
                $pupilComments,
            ) {
                $testMomentEnum = TestMoment::from($testMoment);
                $score = $pupilScores?->where('test_moment', $testMomentEnum)->first();
                $comment = $pupilComments?->where('test_moment', $testMomentEnum)->first();
                [$minScore, $maxScore] = $this->getMinAndMaxScore(
                    $followUpSystem->type,
                    $followUpSystemSubType,
                    $testAudience,
                    $testMoment,
                );

                return [
                    'name' => $testMoment,
                    'score' => $showNnScore ? $score?->score_nn : $score?->score,
                    'minScore' => $minScore,
                    'maxScore' => $maxScore,
                    'is_editable' => $score?->source === null && $followUpSystem->source->isEditable(),
                    'percentile' => $showNnScore ? $score?->percentile_nn : $score?->percentile,
                    'zone' => $showNnScore ? $score?->zone_nn : $score?->zone,
                    'comment' => $comment ? [
                        'uid' => $comment['uid'],
                        'comment' => $comment['comment'],
                        'creator' => $comment->creator?->firstname_with_salutation,
                        'updater' => $comment->updater?->firstname_with_salutation,
                    ] : null,
                ];
            },
        );
    }

    private function getMinAndMaxScore(
        FollowUpSystemType $followUpSystemType,
        FollowUpSystemSubType $followUpSystemSubType,
        ?string $testAudience,
        string $testMoment,
    ): array {
        $scoreMapping = config(
            'follow-up-systems.' . $followUpSystemType->value .
            '.percentile.' .
            $followUpSystemSubType->value .
            '.' .
            $testAudience .
            '.' .
            $testMoment,
        );
        if ($scoreMapping === null) {
            return [null, null];
        }
        $scores = array_keys($scoreMapping);

        return [min($scores), max($scores)];
    }

    private function getWisaCareDataForPupil(Collection $pupils, Group $group, Pupil $pupil): ?WisaCareData
    {
        if (isset($this->wisaCareDataByPupilId) === false) {
            $this->wisaCareDataByPupilId = WisaCareData::query()
                ->join('care_infos', 'care_info_id', 'care_infos.id')
                ->where('care_infos.school_id', $group->school_id)
                ->whereIn('care_infos.pupil_id', $pupils->pluck('id'))
                ->get()
                ->keyBy('pupil_id');
        }

        return $this->wisaCareDataByPupilId->get($pupil->id);
    }
}
