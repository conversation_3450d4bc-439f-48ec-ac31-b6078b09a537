<?php

namespace Cfa\Evaluation\Application\Services\Report;

use App\Models\Feature\Feature;
use App\Models\Feature\FeatureToggle;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;

class ShowRedicodisOnReport
{
    public function __invoke(ReportSettings $reportSettings): bool
    {
        return FeatureToggle::isActive(Feature::ShowRedicodisOnReport)
            && $reportSettings->show_redicodis
            && $reportSettings->school
                ->permissions()
                ->where('name', PermissionName::HasAccessToCare)
                ->exists();
    }
}
