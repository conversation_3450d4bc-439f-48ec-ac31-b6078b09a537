<?php

namespace Cfa\Evaluation\Application\Services\Report;

use Cfa\Evaluation\Domain\Settings\Report\SubjectQuotation\SubjectQuotationSettings;
use Illuminate\Support\Collection;

use function gmp_lcm;

class ReportLeastCommonMultipleService
{
    private const DENOMINATOR_PERCENTAGE = 100;

    /** @param Collection|SubjectQuotationSettings[] $subjectQuotationSettings */
    public function find(Collection $subjectQuotationSettings): ?int
    {
        $denominators = $subjectQuotationSettings
            ->filter(fn(SubjectQuotationSettings $setting): bool => $setting->hasWeight())
            ->map(function (SubjectQuotationSettings $setting) {
                return $setting->weight_percentage ?
                    self::DENOMINATOR_PERCENTAGE :
                    $setting->weight_fraction_denominator;
            })
            ->values()
            ->toArray();

        return $this->calculateLeastCommonMultiple($denominators);
    }

    private function calculateLeastCommonMultiple(array $numbers): ?int
    {
        $totalNumbers = count($numbers);

        if ($totalNumbers === 0) {
            return null;
        }

        $number = $numbers[0];

        if ($totalNumbers === 1) {
            return $number;
        }

        for ($i = 1; $i < $totalNumbers; $i++) {
            $number = (int) gmp_lcm($numbers[$i], $number);
        }

        return $number;
    }
}
