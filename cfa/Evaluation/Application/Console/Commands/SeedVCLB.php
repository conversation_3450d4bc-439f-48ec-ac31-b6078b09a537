<?php

namespace Cfa\Evaluation\Application\Console\Commands;

use App\Console\Commands\Command;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSource;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score\PredefinedFollowUpSystemScore;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;
use Cfa\Evaluation\Domain\FollowUpSystem\StructureType;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

use function app;
use function array_merge;
use function collect;
use function config;
use function random_int;
use function trans;
use function uuid;

/** @codeCoverageIgnore */
class SeedVCLB extends Command
{
    /** @var string */
    protected $signature = 'tms:db-seeding:vclb {--clean} {group}';

    /** @var string */
    protected $description = 'Seeds VCLB tests with scores for a given group.';

    protected ?Group $group;

    private Schoolyear $schoolyear;

    /** @SuppressWarnings(PHPMD.CyclomaticComplexity) */
    public function handle(): void
    {
        $this->group = Group::where('uid', $this->argument('group'))
            ->firstOrFail();
        $this->schoolyear = app(SchoolyearRepositoryInterface::class)->getCurrent();
        $schoolId = $this->group->school_id;

        $followUpSystems =
            collect(FollowUpSystemType::getPredefinedLvsVclb())
                ->map(function (string $followUpSystemTypeValue) use ($schoolId): FollowUpSystem {
                    $followUpSystemType = FollowUpSystemType::from($followUpSystemTypeValue);
                    $name = trans($followUpSystemType->getTranslationKey());

                    $existingFollowUpSystem = FollowUpSystem::where('school_id', $schoolId)
                        ->where('name', $name)
                        ->where('type', $followUpSystemType)
                        ->first();

                    if ($existingFollowUpSystem !== null) {
                        return $existingFollowUpSystem;
                    }

                    $followUpSystem = new FollowUpSystem(['name' => $name]);
                    $followUpSystem->school_id = $this->group->school_id;
                    $followUpSystem->source = FollowUpSystemSource::Predefined;
                    $followUpSystem->type = $followUpSystemType;
                    $followUpSystem->structure_type = StructureType::Table;
                    $followUpSystem->save();

                    return $followUpSystem;
                });

        $pupils = Group::getRepository()->getPupilsOfGroup($this->group);

        if ($this->option('clean')) {
            PredefinedFollowUpSystemScore::query()
                ->where('school_id', $schoolId)
                ->whereIn('predefined_follow_up_system_id', $followUpSystems->pluck('id'))
                ->whereIn('pupil_id', $pupils->pluck('id'))
                ->forceDelete();

            $this->info('All existing scores for ' . $this->group->name . ' are deleted.');
        }

        $classIdiot = $pupils->random();
        $badYear = random_int(1, $this->group->natural_study_year);
        $this->info('The class idiot is the pupil with id ' . $classIdiot->id . ', bad year is ' . $badYear . '.');

        $pupils->each(function (Pupil $pupil) use ($followUpSystems, $classIdiot, $badYear): void {
            $isClassIdiot = $pupil->id === $classIdiot->id;
            $followUpSystems->each(
                function (FollowUpSystem $followUpSystem) use ($pupil, $isClassIdiot, $badYear): void {
                    $maxStudyYear = $this->group->natural_study_year + ($isClassIdiot ? 1 : 0);
                    for ($studyYear = 1; $studyYear <= $maxStudyYear; $studyYear++) {
                        $schoolYear = $this->getSchoolYearByGroupAndStudyYear(
                            $this->group,
                            $isClassIdiot ? $studyYear - 1 : $studyYear,
                        );
                        $scoreStudyYear = $studyYear > $badYear && $isClassIdiot ? $studyYear - 1 : $studyYear;
                        $repeatingYear = $studyYear === ($badYear + 1) && $isClassIdiot ? 1 : 0;

                        collect([TestMoment::Begin, TestMoment::Middle, TestMoment::End])
                            ->each(
                                fn(TestMoment $testMoment): bool =>
                                    $this->createScore(
                                        $pupil,
                                        $followUpSystem,
                                        $testMoment,
                                        $scoreStudyYear,
                                        $repeatingYear,
                                        $schoolYear,
                                    ),
                            );
                    }//end for
                },
            );
        });
    }

    private function createScore(
        Pupil $pupil,
        FollowUpSystem $followUpSystem,
        TestMoment $testMoment,
        int $studyYear,
        int $repeatingYear,
        Schoolyear $schoolYear,
    ): bool {
        $attributes = [
            'pupil_id' => $pupil->id,
            'school_id' => $this->group->school_id,
            'predefined_follow_up_system_id' => $followUpSystem->id,
            'test_moment' => $testMoment,
            'test_audience' => TestAudience::fromName('LO_J' . $studyYear),
            'repeating_year' => $repeatingYear,
            'date' => $this->getRandomDateForScore($testMoment, $schoolYear),
            'schoolyear_id' => $schoolYear->id,
        ];
        $attributes['created_at'] = $attributes['date'];

        $subTypes = array_keys(config('follow-up-systems.' . $followUpSystem->type->value . '.zoneScoreRanges'));

        foreach ($subTypes as $subTypeValue) {
            $subType = FollowUpSystemSubType::from($subTypeValue);
            $scoreValues = $this->getRandomScoreArray($attributes, $followUpSystem->type, $subType);
            if (!empty($scoreValues)) {
                $score = new PredefinedFollowUpSystemScore();
                $score->uid = uuid();
                $score->subtype = $subType;
                $score->forceFill(array_merge($attributes, $scoreValues));
                $score->saveQuietly();
            }
        }

        return true;
    }

    private function getRandomDateForScore(TestMoment $testMoment, Schoolyear $schoolyear): Carbon
    {
        $numberOfPeriods = 3;
        $daysPerPeriod = (int) floor(
            $schoolyear->start->diffInDays($schoolyear->end, absolute: true) / $numberOfPeriods,
        );
        $baseDays = 0;
        if ($testMoment === TestMoment::Middle) {
            $baseDays = $daysPerPeriod;
        }
        if ($testMoment === TestMoment::End) {
            $baseDays = $daysPerPeriod * 2;
        }

        return $schoolyear->start->copy()->addDays(random_int($baseDays, $baseDays + $daysPerPeriod));
    }

    private function getRandomScoreArray(
        array $score,
        FollowUpSystemType $type,
        FollowUpSystemSubType $subType,
    ): array {
        $scoreRanges = config(
            'follow-up-systems.' .
            $type->value .
            '.nn.zoneScoreRanges.'
            . $subType->value . '.' .
            $score['test_audience']->value . '.' .
            $score['test_moment']->value,
        );

        if ($scoreRanges === null) {
            return [];
        }

        $scoreRange = Arr::random($scoreRanges);
        $scoreValue = random_int($scoreRange[0], $scoreRange[1]);
        $scoreObject = new Score(
            $scoreValue,
            $type,
            $subType,
            $score['test_audience'],
            $score['test_moment'],
        );
        $percentileObject = $scoreObject->getPercentile();
        $zoneObject = $percentileObject->getZone();

        return [
            'score' => $scoreObject->getValue(),
            'score_nn' => $scoreObject->getValue(),
            'percentile' => $percentileObject->getValue(),
            'percentile_nn' => $percentileObject->getValueNN(),
            'zone' => $zoneObject->getValue(),
            'zone_nn' => $zoneObject->getValueNN(),
        ];
    }

    private function getSchoolYearByGroupAndStudyYear(Group $group, int $studyYear): Schoolyear
    {
        $difference = $group->natural_study_year - $studyYear;
        if ($difference === 0) {
            return $this->schoolyear;
        }

        return Schoolyear::getRepository()->findSchoolyearByYear($this->schoolyear->start->year - $difference);
    }
}
