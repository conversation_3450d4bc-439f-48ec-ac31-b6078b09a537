<?php

namespace Cfa\Evaluation\Application\Console\Commands;

use App\Console\Commands\Command;
use Cfa\Common\Application\Services\BingelResults\Domain\VclbCmsReference;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Evaluation\Domain\BingelTestResult\BingelTestResult;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSource;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemType;
use Illuminate\Support\Facades\DB;
use stdClass;

use function collect;
use function strtotime;

class ReportVclbUserCounts extends Command
{
    /** @var string */
    protected $signature = 'tms:analytics:vclb-user-count';

    /** {@inheritdoc} */
    protected $description = 'Logs the number of pupils that have at least two scores on a VCLB test.';

    public function handle(): void
    {
        ini_set('memory_limit', '2G');

        $schoolyear = Schoolyear::getRepository()->getCurrent();
        $counters = $this->getCountsFromBingelTestResultsTable($schoolyear);

        foreach (FollowUpSystemType::getPredefinedLvsVclb() as $type) {
            $this->info($type . ': ' . $this->getTotalPupilCountForType($schoolyear, $type, $counters));
            unset($counters[$type]);
        }
    }

    private function getTotalPupilCountForType(Schoolyear $schoolyear, string $followUpSystemType, array $counters): int
    {
        DB::table('predefined_follow_up_system_scores')
            ->select(['smd_external_uid AS pupilId', DB::raw('count(predefined_follow_up_system_scores.id) AS count')])
            ->join('follow_up_systems', 'predefined_follow_up_system_id', 'follow_up_systems.id')
            ->join('users', 'pupil_id', 'users.id')
            ->where('follow_up_systems.source', FollowUpSystemSource::Predefined)
            ->where('type', $followUpSystemType)
            ->where('schoolyear_id', $schoolyear->id)
            ->whereNull('follow_up_systems.deleted_at')
            ->whereNull('predefined_follow_up_system_scores.deleted_at')
            ->groupBy('users.smd_external_uid')
            ->cursor()
            ->each(function (stdClass $result) use (&$counters, $followUpSystemType): void {
                $pupilId = $result->pupilId;
                $resultsWithScores = $result->count;

                $counters[$followUpSystemType][$pupilId] ??= 0;
                $counters[$followUpSystemType][$pupilId] += $resultsWithScores;
            });

        return collect($counters[$followUpSystemType] ?? [])->sum(fn(int $value): int => $value > 1 ? 1 : 0) ?? 0;
    }

    private function getCountsFromBingelTestResultsTable(Schoolyear $schoolyear): array
    {
        $counters = [];

        BingelTestResult::query()
            ->whereNull('deleted_at')
            ->cursor()
            ->each(function (BingelTestResult $testResult) use (&$counters, $schoolyear) {
                $message = $testResult->message;

                if (str_contains((string) $message['cmsReference'], '_LVS') === false) {
                    return true;
                }

                $cmsReference = new VclbCmsReference($message['cmsReference']);
                $resultType = $cmsReference->getFollowUpSystemType()->value;
                $dateTaken = strtotime((string) $message['dateTaken']);

                if (
                    $dateTaken < $schoolyear->start->timestamp
                    || $dateTaken > $schoolyear->end->timestamp
                ) {
                    return true;
                }

                $results = $message['pupilResults'];
                foreach ($results as $result) {
                    $pupilId = $result['pupilId'];
                    $resultsWithScores = collect($result['results'])
                        ->where(fn(array $resultForPupil): bool => $resultForPupil['score'] !== null)
                        ->count();

                    if ($resultsWithScores > 0) {
                        $counters[$resultType][$pupilId] ??= 0;
                        $counters[$resultType][$pupilId] += $resultsWithScores;
                    }
                }
            });

        return $counters;
    }
}
