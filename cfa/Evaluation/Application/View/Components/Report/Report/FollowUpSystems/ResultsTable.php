<?php

namespace Cfa\Evaluation\Application\View\Components\Report\Report\FollowUpSystems;

use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Illuminate\View\Component;

use function collect;

abstract class ResultsTable extends Component
{
    public readonly array $comments;

    public readonly string $primaryBorderColor;
    public readonly string $primaryBackgroundColor;
    public readonly string $secondaryBackgroundColor;
    public readonly string $followUpSystemName;
    public readonly ?array $followUpSystemIcon;
    public readonly array $redicodis;
    public readonly bool $hasRedicodis;

    protected readonly array $quotations;

    public function __construct(
        public readonly array $reportSettings,
        public readonly FollowUpSystem $followUpSystem,
        array $report,
        public readonly array $reportGroupSettings,
    ) {
        $this->quotations = $report['quotations'][$this->followUpSystem->uid] ?? [];
        $this->comments = ($report['comments_by_follow_up_system'][$followUpSystem->uid] ?? collect())->all();
        $this->primaryBorderColor = $reportSettings['primary_border_color'];
        $this->primaryBackgroundColor = $reportSettings['primary_background_color'];
        $this->secondaryBackgroundColor = $reportSettings['secondary_background_color'];
        $this->followUpSystemName = $followUpSystem->name;
        $this->followUpSystemIcon = $followUpSystem->icon?->toArray();

        $this->redicodis = $this->quotations['redicodis'] ?? [];
        $this->hasRedicodis = $reportSettings['show_redicodis'] === true &&
            $reportGroupSettings['redicodis_on_report'] === true &&
            count($this->redicodis) > 0;
    }
}
