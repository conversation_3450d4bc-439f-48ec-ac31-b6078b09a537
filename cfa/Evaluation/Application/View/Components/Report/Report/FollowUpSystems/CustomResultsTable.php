<?php

namespace Cfa\Evaluation\Application\View\Components\Report\Report\FollowUpSystems;

use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Illuminate\Support\Collection;
use Override;

use function collect;
use function view;

class CustomResultsTable extends ResultsTable
{
    public readonly int $numberOfColumns;
    public readonly Collection $inputMoments;
    public readonly array $inputMomentNames;
    public readonly array $goal;
    public readonly string $indentPadding;
    public readonly string $goalDescription;

    public function __construct(
        FollowUpSystem $scoreTable,
        public readonly array $scoreTableSection,
        public readonly bool $showTableHeader,
        public readonly bool $showComments,
        array $reportSettings,
        public readonly array $report,
        public readonly CommentContainer $commentContainer,
        array $reportGroupSettings,
    ) {
        parent::__construct($reportSettings, $scoreTable, $report, $reportGroupSettings);

        $this->inputMoments = ($this->quotations['followUpSystemInputMoments'] ?? collect())->slice(-4);
        $this->inputMomentNames = $this->inputMoments->pluck('followUpSystemInputMoment.name')->all();
        $this->numberOfColumns = $this->inputMoments->count() + 1;
        $this->goal = $scoreTableSection;

        $this->indentPadding = 'padding-left:' . (16 * ($scoreTableSection['level'] + 1)) . 'px';
        $this->goalDescription = $scoreTableSection['description'] ?? '';
    }

    /** {@inheritdoc} */
    #[Override]
    public function render()
    {
        return view('components.report.report.follow-up-systems.custom-results-table');
    }
}
