<?php

namespace Cfa\Evaluation\Domain\EvaluationTest;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

/**
 * @mixin EvaluationTest
 * @extends JsonResource<EvaluationTest>
 */
class EvaluationTestResource extends JsonResource
{
    /**
     * {@inheritdoc}
     *
     * @param Request $request The request.
     */
    #[Override]
    public function toArray($request): array
    {
        $data = [
            'uid' => $this->uid,
            'name' => $this->name,
            'date' => $this->date->toDateString(),
            'min' => $this->min,
            'max' => $this->max,
            'quotation_system' => $this->quotation_system_id !== null && $this->relationLoaded('quotationSystem') ?
                $this->quotationSystem->loadMissing('quotations') :
                null,
            'on_report' => $this->on_report,
            'mayEvaluateUsingScores' => $this->has_score,
            'mayEvaluateUsingQuotations' => $this->quotation_system_id !== null,
            'mayEvaluateUsingComments' => $this->has_comment,
            'imported_at' => $this->imported_at,
            'description' => $this->description,
            'group' => ['group_uid' => $this->group->uid, 'name' => $this->group->name],
        ];

        if ($this->relationLoaded('evaluationTestScores')) {
            $data['evaluation_test_scores'] = $this->evaluationTestScores->toArray();
            $data['average'] = $this->average;
            $data['median'] = $this->median;
        }

        return $data;
    }
}
