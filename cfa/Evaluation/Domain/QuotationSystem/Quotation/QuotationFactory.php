<?php

namespace Cfa\Evaluation\Domain\QuotationSystem\Quotation;

use App\Factories\Factory;
use App\Models\Model;
use Illuminate\Support\Collection;
use Override;

/**
 * QuotationFactory
 *
 * @codingStandardsIgnoreStart
 * @method Collection|Quotation[]|Quotation create($attributes = [], Model|null $parent = null)
 * @method Collection|Quotation[]|Quotation createWithEvents(array $attributes = [], Model|null $parent = null)
 * @codingStandardsIgnoreEnd
 */
class QuotationFactory extends Factory
{
    /** @var string */
    protected $model = Quotation::class;

    public function setLabel(string $label): self
    {
        return $this->state(fn(): array => ['label' => $label]);
    }

    public function setDescription(?string $description): ?self
    {
        return $this->state(fn(): array => ['description' => $description]);
    }

    public function setColor(string $color): self
    {
        return $this->state(fn(): array => ['color' => $color]);
    }

    public function setOrder(string $order): self
    {
        return $this->state(fn(): array => ['order' => $order]);
    }

    public function setQuotationSystemId(int $quotationSystemId): self
    {
        return $this->state(fn(): array => ['quotation_system_id' => $quotationSystemId]);
    }

    #[Override]
    public function definition(): array
    {
        return [
            'uid' => uuid(),
            'label' => $this->faker->text(15),
            'description' => $this->faker->text(255),
            'color' => $this->faker->hexColor,
            'order' => $this->faker->numberBetween(0, 5),
            'quotation_system_id' => $this->faker->numberBetween(0, 5),
        ];
    }
}
