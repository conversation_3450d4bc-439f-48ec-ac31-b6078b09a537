<?php

namespace Cfa\Evaluation\Domain\Settings\EvaluationSubjectPermission;

use App\Factories\Factory;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\Subject\Subject;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Override;

/**
 * EvaluationSubjectPermissionFactory
 *
 * @codingStandardsIgnoreStart
 * @method Collection|EvaluationSubjectPermission[]|EvaluationSubjectPermission create($attributes = [], Model|null $parent = null)
 * @method Collection|EvaluationSubjectPermission[]|EvaluationSubjectPermission createWithEvents(array $attributes = [], Model|null $parent = null)
 * @codingStandardsIgnoreEnd
 */
class EvaluationSubjectPermissionFactory extends Factory
{
    /** @var string */
    protected $model = EvaluationSubjectPermission::class;

    public function setUid(string $uid): self
    {
        return $this->state(fn(): array => ['uid' => $uid]);
    }

    public function setIsClassTeacher(bool $classTeacher): self
    {
        return $this->state(fn(): array => ['class_teacher' => $classTeacher]);
    }

    public function forGroup(Group $group): self
    {
        return $this->state(fn(): array => ['group_id' => $group->id]);
    }

    public function forSubject(Subject $subject): self
    {
        return $this->state(fn(): array => ['subject_id' => $subject->id]);
    }

    #[Override]
    public function definition(): array
    {
        return [
            'uid' => uuid(),
            'class_teacher' => $this->faker->boolean,
            'group_id' => Group::randomId(),
            'subject_id' => Subject::randomId(),
        ];
    }
}
