<?php

namespace Cfa\Evaluation\Domain\Settings\Report\ReportSettings\Group;

use App\Casts\Json;
use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\PruneSoftDeletes;
use Cfa\Common\Application\Traits\TouchedByUser;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\User\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Validation\Rule;
use Override;

/**
 * Cfa\Evaluation\Domain\Settings\Report\ReportSettings\Group\ReportGroupSettings
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property int $group_id
 * @property bool $follow_up_systems_on_report
 * @property mixed|null $disabled_follow_up_systems
 * @property mixed|null $unfiltered_follow_up_systems
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property int|null $creator_id
 * @property int|null $updater_id
 * @property Carbon|null $deleted_at
 * @property bool $redicodis_on_report
 * @property-read User|null $creator
 * @property-read array $validation_rules
 * @property-read Group $group
 * @property-read User|null $updater
 * @method static ReportGroupSettingsFactory factory($count = null, $state = [])
 * @method static Builder|ReportGroupSettings newModelQuery()
 * @method static Builder|ReportGroupSettings newQuery()
 * @method static Builder|ReportGroupSettings onlyTrashed()
 * @method static Builder|ReportGroupSettings query()
 * @method static Carbon|null randomCreatedAt()
 * @method static int|null randomCreatorId()
 * @method static Carbon|null randomDeletedAt()
 * @method static mixed|null randomDisabledFollowUpSystems()
 * @method static bool randomFollowUpSystemsOnReport()
 * @method static int randomGroupId()
 * @method static int randomId()
 * @method static bool randomRedicodisOnReport()
 * @method static mixed|null randomUnfilteredFollowUpSystems()
 * @method static Carbon|null randomUpdatedAt()
 * @method static int|null randomUpdaterId()
 * @method static Builder|ReportGroupSettings whereCreatedAt($value)
 * @method static Builder|ReportGroupSettings whereCreatorId($value)
 * @method static Builder|ReportGroupSettings whereDeletedAt($value)
 * @method static Builder|ReportGroupSettings whereDisabledFollowUpSystems($value)
 * @method static Builder|ReportGroupSettings whereFollowUpSystemsOnReport($value)
 * @method static Builder|ReportGroupSettings whereGroupId($value)
 * @method static Builder|ReportGroupSettings whereId($value)
 * @method static Builder|ReportGroupSettings whereRedicodisOnReport($value)
 * @method static Builder|ReportGroupSettings whereUnfilteredFollowUpSystems($value)
 * @method static Builder|ReportGroupSettings whereUpdatedAt($value)
 * @method static Builder|ReportGroupSettings whereUpdaterId($value)
 * @method static Builder|ReportGroupSettings withTrashed()
 * @method static Builder|ReportGroupSettings withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class ReportGroupSettings extends Model
{
    use PruneSoftDeletes;
    use SoftDeletes;
    use TouchedByUser;

    /** @var string */
    protected $table = 'report_group_settings';

    /** @var array */
    protected $casts = [
        'group_id' => 'integer',
        'follow_up_systems_on_report' => 'boolean',
        'redicodis_on_report' => 'boolean',
        'disabled_follow_up_systems' => Json::class,
        'unfiltered_follow_up_systems' => Json::class,
    ];

    #[Override]
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'group_id' => [
                'required',
                'integer',
                Rule::exists('groups', 'id')->whereNull('deleted_at'),
            ],
        ]);
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }
}
