<?php

namespace Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Comment;

use App\Casts\EncodedString;
use Carbon\Carbon;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\PredefinedFollowUpSystemElement;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;
use Illuminate\Database\Eloquent\Builder;
use Override;

/**
 * Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Comment\PredefinedFollowUpSystemComment
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property int $pupil_id
 * @property int $school_id
 * @property int $schoolyear_id
 * @property int $predefined_follow_up_system_id
 * @property TestMoment $test_moment
 * @property TestAudience $test_audience
 * @property FollowUpSystemSubType $subtype
 * @property Carbon $date
 * @property string|null $comment The comment on a pupil for a given goal.
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property int|null $creator_id
 * @property int|null $updater_id
 * @property Carbon|null $deleted_at
 * @property-read User|null $creator
 * @property-read array $validation_rules
 * @property-read PredefinedFollowUpSystem $predefinedFollowUpSystem
 * @property int $repeating_year
 * @property-read Pupil $pupil
 * @property-read School $school
 * @property-read Schoolyear $schoolyear
 * @property-read User|null $updater
 * @method static PredefinedFollowUpSystemCommentFactory factory($count = null, $state = [])
 * @method static Builder|PredefinedFollowUpSystemComment newModelQuery()
 * @method static Builder|PredefinedFollowUpSystemComment newQuery()
 * @method static Builder|PredefinedFollowUpSystemComment onlyTrashed()
 * @method static Builder|PredefinedFollowUpSystemComment query()
 * @method static string|null randomComment()
 * @method static Carbon|null randomCreatedAt()
 * @method static int|null randomCreatorId()
 * @method static Carbon randomDate()
 * @method static Carbon|null randomDeletedAt()
 * @method static int randomId()
 * @method static int randomPredefinedFollowUpSystemId()
 * @method static int randomRepeatingYear()
 * @method static int randomPupilId()
 * @method static int randomSchoolId()
 * @method static int randomSchoolyearId()
 * @method static FollowUpSystemSubType randomSubtype()
 * @method static TestAudience randomTestAudience()
 * @method static TestMoment randomTestMoment()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static int|null randomUpdaterId()
 * @method static Builder|PredefinedFollowUpSystemComment whereComment($value)
 * @method static Builder|PredefinedFollowUpSystemComment whereCreatedAt($value)
 * @method static Builder|PredefinedFollowUpSystemComment whereCreatorId($value)
 * @method static Builder|PredefinedFollowUpSystemComment whereDate($value)
 * @method static Builder|PredefinedFollowUpSystemComment whereDeletedAt($value)
 * @method static Builder|PredefinedFollowUpSystemComment whereId($value)
 * @method static Builder|PredefinedFollowUpSystemComment wherePredefinedFollowUpSystemId($value)
 * @method static Builder|PredefinedFollowUpSystemComment whereRepeatingYear($value)
 * @method static Builder|PredefinedFollowUpSystemComment wherePupilId($value)
 * @method static Builder|PredefinedFollowUpSystemComment whereSchoolId($value)
 * @method static Builder|PredefinedFollowUpSystemComment whereSchoolyearId($value)
 * @method static Builder|PredefinedFollowUpSystemComment whereSubtype($value)
 * @method static Builder|PredefinedFollowUpSystemComment whereTestAudience($value)
 * @method static Builder|PredefinedFollowUpSystemComment whereTestMoment($value)
 * @method static Builder|PredefinedFollowUpSystemComment whereUid($value)
 * @method static Builder|PredefinedFollowUpSystemComment whereUpdatedAt($value)
 * @method static Builder|PredefinedFollowUpSystemComment whereUpdaterId($value)
 * @method static Builder|PredefinedFollowUpSystemComment withTrashed()
 * @method static Builder|PredefinedFollowUpSystemComment withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class PredefinedFollowUpSystemComment extends PredefinedFollowUpSystemElement
{
    /** @var array */
    protected $fillable = [
        'comment',
        'date',
    ];

    /** @var array */
    protected $casts = [
        'pupil_id' => 'integer',
        'school_id' => 'integer',
        'schoolyear_id' => 'integer',
        'predefined_follow_up_system_id' => 'integer',
        'date' => 'datetime',
        'comment' => EncodedString::class,
        'repeating_year' => 'integer',
    ];

    /** @var string[] */
    protected $dispatchesEvents = [
        'creating' => PredefinedFollowUpSystemCommentCreatingEvent::class,
    ];

    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'comment' => [
                    'nullable',
                    'string',
                ],
            ],
        );
    }
}
