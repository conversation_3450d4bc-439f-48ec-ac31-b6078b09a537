<?php

namespace Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score;

use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\PredefinedFollowUpSystemElementFactory;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Override;

/**
 * PredefinedFollowUpSystemScoreFactory
 *
 * @codingStandardsIgnoreStart
 * @method Collection|PredefinedFollowUpSystemScore[]|PredefinedFollowUpSystemScore create($attributes = [], Model|null $parent = null)
 * @method Collection|PredefinedFollowUpSystemScore[]|PredefinedFollowUpSystemScore createWithEvents(array $attributes = [], Model|null $parent = null)
 * @codingStandardsIgnoreEnd
 */
class PredefinedFollowUpSystemScoreFactory extends PredefinedFollowUpSystemElementFactory
{
    /** @var string */
    protected $model = PredefinedFollowUpSystemScore::class;

    public function setScore(int $score, int $percentile, int|string $zone): self
    {
        return $this->state(fn(): array => [
            'score' => $score,
            'percentile' => $percentile,
            'zone' => $zone,
        ]);
    }

    public function setScoreNn(int $score, int $percentile, int|string $zone): self
    {
        return $this->state(fn(): array => [
            'score_nn' => $score,
            'percentile_nn' => $percentile,
            'zone_nn' => $zone,
        ]);
    }

    public function setRepeatingYear(int $repeatingYear): self
    {
        return $this->state(fn(): array => [
            'repeating_year' => $repeatingYear,
        ]);
    }

    #[Override]
    public function definition(): array
    {
        return array_merge(parent::definition(), [
            'score' => $this->faker->numberBetween(0, 20),
            'percentile' => $this->faker->numberBetween(0, 100),
            'zone' => $this->faker->randomElement(['A', 'B', 'C', 'D', 'E']),
            'score_nn' => $this->faker->numberBetween(0, 20),
            'percentile_nn' => $this->faker->numberBetween(0, 100),
            'zone_nn' => $this->faker->randomElement(['A', 'B', 'C', 'D', 'E']),
            'repeating_year' => 0,
        ]);
    }
}
