<?php

namespace Cfa\Evaluation\Domain\FollowUpSystem;

use App\Repositories\RepositoryInterface;
use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

interface FollowUpSystemRepositoryInterface extends RepositoryInterface
{
    /** @return Collection<int, FollowUpSystem> */
    public function getAllForGroupsInSchool(School $school, Collection $groups, array $with = []): Collection;

    /** @return Collection<int, FollowUpSystem> */
    public function getAllForSchool(School $school): Collection;

    /** @return Collection<int, FollowUpSystem> */
    public function getAllForGroup(Group $group): Collection;

    /** @return Collection<int, FollowUpSystem> */
    public function getAllForGroupFilteredWithData(Group $group): Collection;

    /** @return Collection<int, FollowUpSystem> */
    public function getUsedForCareInfo(CareInfo $careInfo): Collection;

    public function getPredefinedUsedForCareInfo(CareInfo $careInfo): Collection;

    public function getBaseQuery(
        School $school,
        ?Collection $groups = null,
        array $columns = ['follow_up_systems.*'],
    ): Builder;
}
