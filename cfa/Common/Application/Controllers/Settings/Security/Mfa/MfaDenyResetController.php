<?php

namespace Cfa\Common\Application\Controllers\Settings\Security\Mfa;

use App\Controllers\Controller;
use Cfa\Common\Application\Mail\Mfa\User\MfaResetDeniedEmail;
use Cfa\Common\Domain\User\Mfa\Status\MfaStatus;
use Cfa\Common\Domain\User\StaffSecurityResource;
use Cfa\Common\Domain\User\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Mail;

class MfaDenyResetController extends Controller
{
    public function __invoke(User $userToResetMfa): JsonResponse
    {
        $userMfa = $userToResetMfa->userMfa;

        $userMfa->status = MfaStatus::Active;
        $userMfa->save();

        if (!empty($userToResetMfa->contact_email)) {
            Mail::to($userToResetMfa->contact_email)
                ->locale($userToResetMfa->preferredLocale())
                ->send(new MfaResetDeniedEmail($userToResetMfa));
        }

        return $this->respond(new StaffSecurityResource($userToResetMfa));
    }
}
