<?php

namespace Cfa\Common\Application\Controllers\Settings\PupilManagement\ManagePupils;

use App\Controllers\Controller;
use Cfa\Common\Domain\School\Group\GroupRepositoryInterface;
use Cfa\Common\Domain\User\Pupil;
use Illuminate\Http\JsonResponse;

use function app;
use function route;

class PupilClearNationalRegisterNumberController extends Controller
{
    public function __invoke(Pupil $pupil): JsonResponse
    {
        $careInfo = $pupil->care_info;
        $careInfo->clearNationalRegisterNumber();
        $careInfo->save();

        app(GroupRepositoryInterface::class)->flushGroupCacheForSchool($careInfo->school);

        return $this->respondJsonRedirectWriteConnection(
            route('web.settings.common.pupil-management.manage-pupils-overview'),
        );
    }
}
