<?php

namespace Cfa\Common\Application\Controllers\Settings\FollowUpSystem\Goal;

use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoal;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoalRepositoryInterface;
use Illuminate\Http\JsonResponse;
use Override;

class FollowUpSystemGoalDearchiveController extends AbstractFollowUpSystemGoalArchiveDearchiveController
{
    protected string $method = 'dearchive';

    #[Override]
    public function __invoke(
        FollowUpSystem $followUpSystem,
        FollowUpSystemGoal $followUpSystemGoal,
        FollowUpSystemGoalDearchiveRequest $request,
        FollowUpSystemGoalRepositoryInterface $followUpSystemGoalRepository,
    ): JsonResponse {
        $this->withChildren = $request->withChildren();

        return parent::__invoke(
            $followUpSystem,
            $followUpSystemGoal,
            $request,
            $followUpSystemGoalRepository,
        );
    }

    #[Override]
    protected function archiveDearchiveGoal(
        FollowUpSystem $followUpSystem,
        FollowUpSystemGoal $followUpSystemGoal,
    ): void {
        parent::archiveDearchiveGoal($followUpSystem, $followUpSystemGoal);
        $this->deachchiveParents($followUpSystem, $followUpSystemGoal);
    }

    protected function deachchiveParents(
        FollowUpSystem $followUpSystem,
        FollowUpSystemGoal $followUpSystemGoal,
    ): void {
        if ($followUpSystemGoal->parent_id === null) {
            return;
        }
        $query = FollowUpSystemGoal::where('id', $followUpSystemGoal->parent_id)
            ->where('follow_up_system_id', $followUpSystem->id);
        $query->update(['archived_at' => null]);
        $this->deachchiveParents($followUpSystem, $query->first(['id', 'parent_id']));
    }
}
