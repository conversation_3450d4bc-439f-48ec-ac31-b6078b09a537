<?php

namespace Cfa\Common\Application\Controllers\Settings\FollowUpSystem\FollowUpSystem;

use App\Http\Requests\FormRequest;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemType;
use Override;

class PredefinedFollowUpSystemStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'followUpSystem' => [
                    'required',
                    'enumValue:' . FollowUpSystemType::class,
                ],
            ],
        );
    }
}
