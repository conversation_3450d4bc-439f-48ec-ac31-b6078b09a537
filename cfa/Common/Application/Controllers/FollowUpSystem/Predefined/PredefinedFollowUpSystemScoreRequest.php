<?php

namespace Cfa\Common\Application\Controllers\FollowUpSystem\Predefined;

use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score\PredefinedFollowUpSystemScore;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;
use Illuminate\Support\Facades\Route;
use Illuminate\Validation\ValidationException;
use Override;

use function array_keys;
use function config;
use function max;
use function min;
use function trans;

class PredefinedFollowUpSystemScoreRequest extends AbstractPredefinedFollowUpSystemSaveRequest
{
    /**
     * The minimum score.
     *
     * @var int
     */
    private $minScore = 0;

    /**
     * The maximum score.
     *
     * @var int
     */
    private $maxScore = 0;

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function rules(): array
    {
        $this->setMinScoreAndMaxScore();

        return array_merge(
            parent::rules(),
            [
                'score' => [
                    'integer',
                    'nullable',
                    'max:' . $this->maxScore,
                    'min:' . $this->minScore,
                ],
            ],
        );
    }

    private function setMinScoreAndMaxScore(): void
    {
        $data = $this->validationData();

        if (!isset($data['testAudience'], $data['testMoment'])) {
            return;
        }

        $scoreMapping = config(
            'follow-up-systems.' .
            $this->followUpSystem->type->value .
            '.percentile.' .
            FollowUpSystemSubType::Default->value .
            '.' .
            $data['testAudience'] .
            '.' .
            $data['testMoment'],
        );

        if ($this->followUpSystem->type->value === FollowUpSystemType::Koala->value) {
            $this->checkForInputOnDifferentTestMomentForKoala();
        }

        if (empty($scoreMapping)) {
            return;
        }

        $scores = array_keys($scoreMapping);

        $this->minScore = min($scores);
        $this->maxScore = max($scores);
    }

    public function checkForInputOnDifferentTestMomentForKoala(): void
    {
        if ($this->get('score') === null) {
            return;
        }
        $testMoment = $this->getTestMoment() === TestMoment::APlusB ? TestMoment::APlusBStar : TestMoment::APlusB;
        $schoolyear = $this->getSchoolyear();
        /* @var Pupil $pupil */
        $pupil = Route::current()->parameter('pupil');
        $attributes = [
            'school_id' => school()->id,
            'pupil_id' => $pupil->id,
            'schoolyear_id' => $schoolyear->id,
            'test_moment' => $testMoment,
            'subtype' => FollowUpSystemSubType::Default,
            'predefined_follow_up_system_id' => $this->followUpSystem->id,
        ];

        if (PredefinedFollowUpSystemScore::where($attributes)->exists()) {
            $message = trans('errors.koala.aplusb_or_aplusbstar');
            throw ValidationException::withMessages(['score' => $message]);
        }
    }
}
