<?php

namespace Cfa\Common\Application\Controllers\FollowUpSystem\Standard\Goal\Quotation\Comment;

use App\Http\Requests\FormRequest;
use Illuminate\Validation\Validator;
use Override;

class FollowUpSystemGoalCommentSyncRequest extends FormRequest
{
    public const COMMENT = 'comment';
    public const PUPILS = 'pupils';
    public const FOLLOW_UP_SYSTEM_GOALS = 'follow_up_system_goals';
    public const COMMENT_ON_REPORT = 'comment_on_report';

    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                self::COMMENT => [
                    'present',
                ],
                self::PUPILS => [
                    'required',
                    'array',
                ],
                self::FOLLOW_UP_SYSTEM_GOALS => [
                    'required',
                    'array',
                ],
            ],
        );
    }

    public function after(): array
    {
        return [
            $this->validateReportPeriod(),
        ];
    }

    private function validateReportPeriod(): callable
    {
        return function (Validator $validator): void {
            if ($this->followUpSystemInputMoment->reportPeriod->isExpired()) {
                $validator->errors()->add('report_period', trans('validation.fus.report_period.expired'));
            }
        };
    }
}
