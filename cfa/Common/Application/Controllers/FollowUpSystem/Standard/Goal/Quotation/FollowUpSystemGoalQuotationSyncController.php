<?php

namespace Cfa\Common\Application\Controllers\FollowUpSystem\Standard\Goal\Quotation;

use Cfa\Common\Domain\School\Group\Group;
use Cfa\Evaluation\Application\Exceptions\EvaluationValidationException;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoal;
use Cfa\Evaluation\Domain\FollowUpSystem\InputMoment\FollowUpSystemInputMoment;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class FollowUpSystemGoalQuotationSyncController extends AbstractFollowUpSystemGoalQuotationController
{
    public function __invoke(
        FollowUpSystem $followUpSystem,
        FollowUpSystemInputMoment $followUpSystemInputMoment,
        FollowUpSystemGoalQuotationSyncRequest $request,
    ): JsonResponse {
        $followUpSystemGoals = FollowUpSystemGoal::withoutArchived()
            ->whereIn('uid', $request->get(FollowUpSystemGoalQuotationSyncRequest::FOLLOW_UP_SYSTEM_GOALS))
            ->get();

        $pupils = Group::getRepository()
            ->getPupilsOfGroup(group())
            ->whereIn('uid', $request->get(FollowUpSystemGoalQuotationSyncRequest::PUPILS));

        if ($pupils->isEmpty()) {
            return $this->respondNoContent();
        }

        try {
            foreach ($followUpSystemGoals as $followUpSystemGoal) {
                foreach ($pupils as $pupil) {
                    $this->syncFollowUpSystemGoalQuotation(
                        $pupil,
                        $followUpSystemGoal,
                        $followUpSystem,
                        $request->getQuotation(),
                        $followUpSystemInputMoment,
                    );
                }
            }
        } catch (EvaluationValidationException $exception) {
            throw ValidationException::withMessages(['follow_up_system_goal' => $exception->getMessage()]);
        }

        return $this->respondNoContent();
    }
}
