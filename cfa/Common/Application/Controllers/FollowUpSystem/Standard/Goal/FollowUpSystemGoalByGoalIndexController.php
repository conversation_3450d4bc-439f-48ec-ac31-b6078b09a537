<?php

namespace Cfa\Common\Application\Controllers\FollowUpSystem\Standard\Goal;

use Cfa\Common\Application\Controllers\FollowUpSystem\FollowUpSystemControllerHelper;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceResource;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\Comment\FollowUpSystemGoalComment;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoalTreeResource;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\Quotation\FollowUpSystemGoalQuotation;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Inertia\Inertia;
use Inertia\Response;

use function group;
use function route;

class FollowUpSystemGoalByGoalIndexController extends AbstractFollowUpSystemGoalIndexController
{
    public function __invoke(
        FollowUpSystem $followUpSystem,
        ReportPeriod $reportPeriod,
        FollowUpSystemControllerHelper $followUpSystemControllerHelper,
        SchoolyearRepositoryInterface $schoolyearRepository,
        Request $request,
    ): Response {
        $group = group();
        $schoolyear = $schoolyearRepository->getCurrent();

        $pupils = $this->groupRepository->getPupilsOfGroup(group());

        $pupilData = $pupils->map(function (Pupil $pupil): array {
            $status = $pupil->care_info->pupil_status->isDisabled();
            $pupilData = $pupil->toArray();
            $pupilData['disabled'] = $status;

            return $pupilData;
        });

        $followUpSystem->load(['quotationSystem.quotations']);

        [
            $followUpSystemGoals,
            $targetAudiences,
            $targetAudience,
        ] = $this->getFollowUpSystemGoalsWithRelatedData($request, $followUpSystem);

        $reportPeriods = $this->reportPeriodRepository->getReportPeriodsForGroup($group, $schoolyear)
            ->map(function (ReportPeriod $reportPeriod) use ($followUpSystem, $targetAudience) {
                return [
                    'name' => $reportPeriod->name,
                    'link' => $this->getByGoalRouteForReportPeriod($followUpSystem, $reportPeriod, $targetAudience),
                ];
            });

        $commentBaseUrl = route('web.common.follow-up-systems.goals.comment.store', [
            'followUpSystem' => $followUpSystem->uid,
            'pupil' => '#pupilUid#',
            'followUpSystemGoal' => '#followUpSystemGoalUid#',
            'followUpSystemInputMoment' => '#followUpSystemInputMoment#',
        ]);
        $quotationBaseUrl = route('web.common.follow-up-systems.goals.quotations.store', [
            'followUpSystem' => $followUpSystem->uid,
            'pupil' => '#pupilUid#',
            'followUpSystemGoal' => '#followUpSystemGoalUid#',
            'followUpSystemInputMoment' => '#followUpSystemInputMoment#',
        ]);
        $fetchByGoalBaseUrl = route('web.common.follow-up-systems.goals.by-goal.fetch', [
            'followUpSystem' => $followUpSystem->uid,
            'followUpSystemGoal' => '#followUpSystemGoalUid#',
            'followUpSystemInputMoment' => '#followUpSystemInputMoment#',
        ]);

        $commentValidationRules = new FollowUpSystemGoalComment()
            ->withoutRule('comment', 'required')
            ->getValidationRulesAttribute();

        $this->setTabs($followUpSystem, $reportPeriod, $targetAudience);
        $followUpSystemControllerHelper->setGroupSwitcherGroups();
        $followUpSystems = $followUpSystemControllerHelper->getFollowUpSystems($group, $followUpSystem);

        $inputMoments = $this->followUpSystemInputMomentRepository->getAllForGroupAndFollowUpSystemInReportPeriod(
            $group,
            $followUpSystem,
            $reportPeriod,
        );

        return Inertia::render(
            'Evaluation/FollowUpSystemGoals/ByGoal',
            [
                'quotations' => optional($followUpSystem->quotationSystem)->quotations,
                'quotationSystem' => $followUpSystem->quotationSystem,
                'reportPeriods' => $reportPeriods,
                'inputMoments' => $inputMoments,
                'reportPeriod' => $reportPeriod,
                'commentBaseUrl' => $commentBaseUrl,
                'quotationBaseUrl' => $quotationBaseUrl,
                'quotationSyncUrl' => route('web.evaluation.follow-up-system.goals.quotations.sync', [
                    'followUpSystem' => $followUpSystem->uid,
                    'followUpSystemInputMoment' => '#followUpSystemInputMoment#',
                ]),
                'commentSyncUrl' => route('web.evaluation.follow-up-system.goals.comments.sync', [
                    'followUpSystem' => $followUpSystem->uid,
                    'followUpSystemInputMoment' => '#followUpSystemInputMoment#',
                ]),
                'fetchByGoalBaseUrl' => $fetchByGoalBaseUrl,
                'followUpSystem' => $followUpSystem,
                'goalsWithQuotations' => $this->getGoalsWithQuotations($inputMoments, $pupils),
                'goalsWithComments' => $this->getGoalsWithComments($inputMoments, $pupils),
                'commentValidationRules' => $commentValidationRules,
                'pupils' => $pupilData,
                'followUpSystems' => $followUpSystems,
                'periodIsExpired' => $reportPeriod->isExpired(),
                'schoolyear' => $reportPeriod->schoolyear,
                'inputMomentSaveBaseUrl' => route('web.evaluation.follow-up-system.input-moment.save', [
                    'reportPeriod' => $reportPeriod,
                    'followUpSystem' => $followUpSystem,
                    'followUpSystemInputMoment' => '#followUpSystemInputMoment#',
                ]),
                'followUpSystemGoals' => FollowUpSystemGoalTreeResource::collection($followUpSystemGoals),
                'targetAudiences' => TargetAudienceResource::collection($targetAudiences),
                'targetAudience' => $targetAudience ? TargetAudienceResource::make($targetAudience) : null,
            ],
        );
    }

    private function getGoalsWithComments(Collection $inputMoments, Collection $pupils): Collection
    {
        $inputMomentsById = $inputMoments->keyBy('id');

        return FollowUpSystemGoalComment::distinct()
            ->join('follow_up_system_goals', 'follow_up_system_goals.id', 'follow_up_system_goal_id')
            ->whereIn('follow_up_system_input_moment_id', $inputMomentsById->keys())
            ->whereIn('pupil_id', $pupils->pluck('id'))
            ->get(['follow_up_system_goals.uid AS follow_up_system_goal_uid', 'follow_up_system_input_moment_id'])
            ->groupBy(fn(FollowUpSystemGoalComment $goalComment) => $goalComment->follow_up_system_goal_uid)
            ->map(fn(Collection $goalComments): Collection => $goalComments->mapWithKeys(
                fn(FollowUpSystemGoalComment $goalComment) => [
                    $inputMomentsById->get($goalComment->follow_up_system_input_moment_id)->uid => true,
                ],
            ));
    }

    private function getGoalsWithQuotations(Collection $inputMoments, Collection $pupils): Collection
    {
        $inputMomentsById = $inputMoments->keyBy('id');

        return FollowUpSystemGoalQuotation::distinct()
            ->join('follow_up_system_goals', 'follow_up_system_goals.id', 'follow_up_system_goal_id')
            ->whereIn('follow_up_system_input_moment_id', $inputMomentsById->keys())
            ->whereIn('pupil_id', $pupils->pluck('id'))
            ->get(['follow_up_system_goals.uid AS follow_up_system_goal_uid', 'follow_up_system_input_moment_id'])
            ->groupBy(fn(FollowUpSystemGoalQuotation $goalQuotation) => $goalQuotation->follow_up_system_goal_uid)
            ->map(fn(Collection $goalQuotations): Collection => $goalQuotations->mapWithKeys(
                fn(FollowUpSystemGoalQuotation $goalQuotation) => [
                    $inputMomentsById->get($goalQuotation->follow_up_system_input_moment_id)->uid => true,
                ],
            ));
    }
}
