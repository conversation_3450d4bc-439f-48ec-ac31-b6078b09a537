<?php

namespace Cfa\Common\Application\Controllers\Export;

use App\Http\Requests\FormRequest;
use Override;

use function array_keys;

class DataTableExportRequest extends FormRequest
{
    private const TITLE = 'title';
    private const TITLE_DATA = 'titleData';
    private const HEADERS = 'headers';
    private const ITEMS = 'items';
    private const HEADER_GROUPS = 'headerGroups';
    private const SECONDARY_HEADER_GROUPS = 'secondaryHeaderGroups';

    /**
     * Get the validation rules that apply to the request.
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                self::TITLE => [
                    'required',
                    'string',
                ],
                self::TITLE_DATA => [
                    'required',
                    'string',
                ],
                self::HEADERS => [
                    'required',
                    'array',
                ],
                self::ITEMS => [
                    'required',
                    'array',
                ],
                self::HEADER_GROUPS => [
                    'present',
                    'array',
                ],
                self::SECONDARY_HEADER_GROUPS => [
                    'present',
                    'array',
                ],
            ],
        );
    }

    public function getData(): array
    {
        return $this->all(array_keys($this->rules()));
    }
}
