<?php

namespace Cfa\Common\Application\Controllers\UserDataTransfer;

use App\Controllers\Controller;
use Illuminate\View\View;

use function compact;
use function trans;
use function view;

class UserDataTransferExecutingController extends Controller
{
    /**
     * Displays the request data-transfer form.
     */
    public function __invoke(): View
    {
        $title = trans('titles.user-data-transfer.executing');
        $message = trans('labels.user-data-transfer.executing');

        return view(
            'common.user-data-transfer.info',
            compact('title', 'message'),
        );
    }
}
