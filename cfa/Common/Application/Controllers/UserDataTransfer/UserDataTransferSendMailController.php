<?php

namespace Cfa\Common\Application\Controllers\UserDataTransfer;

use App\Controllers\Controller;
use Carbon\Carbon;
use Cfa\Common\Application\Mail\UserDataTransferIctCoordinatorEmail;
use Cfa\Common\Application\Mail\UserDataTransferUserEmail;
use Cfa\Common\Application\Repositories\UserDataTransferCacheRepository;
use Cfa\Common\Domain\User\User;
use Cfa\Common\Domain\User\UserRepositoryInterface;
use Cfa\Common\Domain\UserDataTransfer\UserDataTransfer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\URL;

use function array_filter;
use function request;
use function route;

class UserDataTransferSendMailController extends Controller
{
    public function __invoke(UserDataTransferRequest $request, UserRepositoryInterface $userRepository): JsonResponse
    {
        $response = $this->respondJsonRedirect(route('web.common.users.data-transfer.mail-sent'));
        $user = Auth::user();
        $email = $request->get('email');
        $userDataTransferCacheRepository = app(UserDataTransferCacheRepository::class);
        $userDataTransferCacheRepository->storeUserDataTransferForUser($user, $email);
        $attempts = $userDataTransferCacheRepository->getUserDataTransferEmailsForUser($user);
        if ($attempts->count() > UserDataTransferCacheRepository::MAX_ATTEMPTS) {
            return $response;
        }

        /** @var User $oldAccount */
        $oldAccount = User::whereAccountName($email)->withTrashed()->where('id', '<>', $user->id)->first();
        if ($oldAccount === null) {
            return $response;
        }
        $ictCoordinators = $userRepository->getIctCoordinatorsForUser($oldAccount);
        $userDataTransfer = $this->createUserDataTransfer($user, $oldAccount);
        $contactEmails = array_filter([$oldAccount->email, $oldAccount->account_name]);
        if (!empty($contactEmails)) {
            Mail::to($contactEmails)
                ->locale($oldAccount->preferredLocale())
                ->send(new UserDataTransferUserEmail($oldAccount, $this->getTemporaryUrl($userDataTransfer)));
        }
        if ($ictCoordinators->isNotEmpty()) {
            $ictCoordinators
                ->reject(fn(User $ictCoordinator): bool => empty($ictCoordinator->contact_email))
                ->each(function (User $ictCoordinator) use ($oldAccount, $userDataTransfer): void {
                    Mail::to($ictCoordinator->contact_email)
                        ->locale($ictCoordinator->preferredLocale())
                        ->send(
                            new UserDataTransferIctCoordinatorEmail(
                                $oldAccount,
                                $ictCoordinator,
                                $this->getTemporaryUrl($userDataTransfer),
                            ),
                        );
                });
        }

        return $response;
    }

    private function getTemporaryUrl(UserDataTransfer $userDataTransfer): string
    {
        return URL::temporarySignedRoute(
            'web.common.users.data-transfer.confirm',
            $userDataTransfer->expires_at,
            ['userDataTransfer' => $userDataTransfer->uid],
        );
    }

    private function createUserDataTransfer(User $user, User $oldUserAccount): UserDataTransfer
    {
        $expiresAt = Carbon::now()->addDays(UserDataTransferCacheRepository::DAYS_TO_CACHE);
        $userDataTransfer = new UserDataTransfer();
        $userDataTransfer->ip = request()->header(Request::HEADER_X_FORWARDED_FOR) ?? request()->ip();
        $userDataTransfer->from_user_id = $oldUserAccount->id;
        $userDataTransfer->to_user_id = $user->id;
        $userDataTransfer->expires_at = $expiresAt;
        $userDataTransfer->save();

        return $userDataTransfer;
    }
}
