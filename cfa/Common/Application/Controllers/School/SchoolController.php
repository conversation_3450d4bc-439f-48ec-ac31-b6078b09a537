<?php

namespace Cfa\Common\Application\Controllers\School;

use App\Controllers\Controller;
use Cfa\Common\Application\Exceptions\NoActiveSchoolsException;
use Cfa\Common\Domain\School\School;
use Illuminate\Http\RedirectResponse;

use function get_subdomain;

class SchoolController extends Controller
{
    /**
     * Saves the new selected school in the cookie and redirects to the home of the application.
     *
     * @param School $school The new selected school.
     * @param SchoolSwitchRequest $request The incoming request.
     *
     * @throws NoActiveSchoolsException Exception is thrown when you want to switch to a school you haven't rights for.
     */
    public function switch(School $school, SchoolSwitchRequest $request): RedirectResponse
    {
        $routeName = 'web.';
        $routeName .= $request->redirectToSettings() ? 'settings.' : '';
        $routeName .= (get_subdomain() ?: 'tms') . '.home';

        // Set school.
        school($school);

        return redirect(route($routeName));
    }
}
