<?php

namespace Cfa\Common\Application\Services\NationalRegisterNumber;

use Carbon\Carbon;
use Cfa\Common\Domain\User\Gender;

use function str_pad;

class NationalRegisterNumber
{
    private Gender $gender;

    private Carbon $dateOfBirth;

    private string $nationalRegisterNumber;

    private bool $isValid = false;

    public function __construct(Gender $gender, Carbon $dateOfBirth, string $nationalRegisterNumber)
    {
        $this->gender = $gender;
        $this->dateOfBirth = $dateOfBirth;
        $this->setNationalRegisterNumber($nationalRegisterNumber);
    }

    private function setNationalRegisterNumber(string $nationalRegisterNumber): void
    {
        $this->isValid = false;
        $this->nationalRegisterNumber = $this->prepareNationalRegisterNumber($nationalRegisterNumber);
        $this->isValid = app(NationalRegisterNumberValidator::class)
            ->validateNationalRegisterNumber($this);
    }

    public function isValid(): bool
    {
        return $this->isValid;
    }

    public function getHash(?string $key = null): string
    {
        return app(NationalRegisterNumberHashingService::class)
            ->getHash($this, $key);
    }

    public function getGender(): Gender
    {
        return $this->gender;
    }

    public function getDateOfBirth(): Carbon
    {
        return $this->dateOfBirth;
    }

    public function getNationalRegisterNumber(): string
    {
        return $this->nationalRegisterNumber;
    }

    private function prepareNationalRegisterNumber(string $nationalRegisterNumber): string
    {
        return $this->padWithZeros($this->cleanNonNumericCharacters($nationalRegisterNumber));
    }

    private function cleanNonNumericCharacters(string $nationalRegisterNumber): string
    {
        return preg_replace('~\D~', '', $nationalRegisterNumber);
    }

    private function padWithZeros(string $nationalRegisterNumber): string
    {
        return str_pad($nationalRegisterNumber, 11, '0', STR_PAD_LEFT);
    }
}
