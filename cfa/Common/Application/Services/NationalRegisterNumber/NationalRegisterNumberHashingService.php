<?php

namespace Cfa\Common\Application\Services\NationalRegisterNumber;

use DomainException;

class NationalRegisterNumberHashingService
{
    public const HASHING_ALGO = 'sha3-512';
    public const DEFAULT_HASHING_KEY = 'default';
    public const WISA_HASHING_KEY = 'wisa';

    public function getHash(
        NationalRegisterNumber $nationalRegisterNumber,
        ?string $key = null,
    ): string {
        if ($key === null) {
            $key = static::DEFAULT_HASHING_KEY;
        }

        if ($nationalRegisterNumber->isValid() === false) {
            throw new DomainException('NationalRegisterNumber is invalid!');
        }

        if (array_key_exists($key, config('national-register-number.hashing-keys')) === false) {
            throw new DomainException('Invalid key given!');
        }

        return hash_hmac(
            (string) static::HASHING_ALGO,
            $nationalRegisterNumber->getNationalRegisterNumber(),
            (string) config('national-register-number.hashing-keys.' . $key),
        );
    }
}
