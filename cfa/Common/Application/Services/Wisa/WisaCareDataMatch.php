<?php

namespace Cfa\Common\Application\Services\Wisa;

use Cfa\Common\Domain\User\User;
use Cfa\Wisa\Domain\CareData\WisaCareData;

class WisaCareDataMatch
{
    private User $user;
    private ?WisaCareData $wisaCareData;

    public function __construct(
        User $user,
        ?WisaCareData $wisaCareData,
    ) {
        $this->user = $user;
        $this->wisaCareData = $wisaCareData;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function getWisaNationalRegisterNumberHash(): ?string
    {
        return $this->wisaCareData?->national_register_number_hash;
    }

    public function getWisaUid(): ?string
    {
        return $this->wisaCareData?->wisa_uid;
    }

    public function hasWisaCareData(): bool
    {
        return $this->wisaCareData && $this->wisaCareData->care_data !== null;
    }

    public function getWisaCareData(): ?WisaCareData
    {
        return $this->wisaCareData;
    }
}
