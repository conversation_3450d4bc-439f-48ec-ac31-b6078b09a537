<?php

namespace Cfa\Common\Application\Traits\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Carbon;
use Override;

class MaskedScope implements Scope
{
    /**
     * All of the extensions to be added to the builder.
     *
     * @var array
     */
    protected $extensions = ['Mask', 'Unmask', 'WithMasked',  'WithoutMasked', 'OnlyMasked'];

    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param Builder $builder The query builder.
     * @param Model $model The model.
     */
    #[Override]
    public function apply(Builder $builder, Model $model): void
    {
        $builder->whereNull($model->getQualifiedMaskedAtColumn());
    }

    /**
     * Extend the query builder with the needed functions.
     *
     * @param Builder $builder The query builder.
     */
    public function extend(Builder $builder): void
    {
        foreach ($this->extensions as $extension) {
            $this->{"add{$extension}"}($builder);
        }
    }

    /**
     * Get the "hidden at" column for the builder.
     *
     * @param Builder $builder The query builder.
     */
    protected function getMaskedAtColumn(Builder $builder): string
    {
        if (count((array) $builder->getQuery()->joins) > 0) {
            return $builder->getModel()->getQualifiedMaskedAtColumn();
        }

        return $builder->getModel()->getMaskedAtColumn();
    }

    /**
     * Add the mask extension to the builder.
     *
     * @param Builder $builder The query builder.
     */
    protected function addMask(Builder $builder): void
    {
        $builder->macro(
            'mask',
            function (Builder $builder) {
                return $builder->update([$builder->getModel()->getMaskedAtColumn() => Carbon::now()]);
            },
        );
    }

    /**
     * Add the unmask extension to the builder.
     *
     * @param Builder $builder The query builder.
     */
    protected function addUnmask(Builder $builder): void
    {
        $builder->macro(
            'unmask',
            function (Builder $builder) {
                return $builder->update([$builder->getModel()->getMaskedAtColumn() => null]);
            },
        );
    }

    /**
     * Add the without-Masked extension to the builder.
     *
     * @param Builder $builder The query builder.
     */
    protected function addWithMasked(Builder $builder): void
    {
        $builder->macro(
            'withMasked',
            function (Builder $builder) {
                return $builder->withoutGlobalScope($this);
            },
        );
    }

    /**
     * Add the without-Masked extension to the builder.
     *
     * @param Builder $builder The query builder.
     */
    protected function addWithoutMasked(Builder $builder): void
    {
        $builder->macro(
            'withoutMasked',
            function (Builder $builder) {
                $model = $builder->getModel();

                $builder->withoutGlobalScope($this)->whereNull(
                    $model->getQualifiedMaskedAtColumn(),
                );

                return $builder;
            },
        );
    }

    /**
     * Add the only-Masked extension to the builder.
     *
     * @param Builder $builder The query builder.
     */
    protected function addOnlyMasked(Builder $builder): void
    {
        $builder->macro(
            'onlyMasked',
            function (Builder $builder) {
                $model = $builder->getModel();

                $builder->withoutGlobalScope($this)->whereNotNull(
                    $model->getQualifiedMaskedAtColumn(),
                );

                return $builder;
            },
        );
    }
}
