<?php

namespace Cfa\Common\Application\Traits\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Carbon;
use Override;

class ArchivingScope implements Scope
{
    /**
     * All of the extensions to be added to the builder.
     *
     * @var array
     */
    protected $extensions = ['Archive', 'Dearchive', 'WithoutArchived', 'WithArchived', 'OnlyArchived'];

    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param Builder $builder The query builder.
     * @param Model $model The model.
     */
    #[Override]
    public function apply(Builder $builder, Model $model): void
    {
        // This could be used to force the builder to exclude the archived models.
        // At this point we don't want to do this yet, but this function
        // is required by the Scope and this is required by the addGlobalScope.
        // Example: $builder->whereNull($model->getQualifiedArchivedAtColumn());.
    }

    /**
     * Extend the query builder with the needed functions.
     *
     * @param Builder $builder The query builder.
     */
    public function extend(Builder $builder): void
    {
        foreach ($this->extensions as $extension) {
            $this->{"add{$extension}"}($builder);
        }
    }

    /**
     * Get the "archived at" column for the builder.
     *
     * @param Builder $builder The query builder.
     */
    protected function getArchivedAtColumn(Builder $builder): string
    {
        if (count((array) $builder->getQuery()->joins) > 0) {
            return $builder->getModel()->getQualifiedArchivedAtColumn();
        }

        return $builder->getModel()->getArchivedAtColumn();
    }

    /**
     * Add the archive extension to the builder.
     *
     * @param Builder $builder The query builder.
     */
    protected function addArchive(Builder $builder): void
    {
        $builder->macro(
            'archive',
            function (Builder $builder) {
                return $builder->update([$builder->getModel()->getArchivedAtColumn() => Carbon::now()]);
            },
        );
    }

    /**
     * Add the dearchive extension to the builder.
     *
     * @param Builder $builder The query builder.
     */
    protected function addDearchive(Builder $builder): void
    {
        $builder->macro(
            'dearchive',
            function (Builder $builder) {
                return $builder->update([$builder->getModel()->getArchivedAtColumn() => null]);
            },
        );
    }

    /**
     * Add the without-Archived extension to the builder.
     *
     * @param Builder $builder The query builder.
     */
    protected function addWithoutArchived(Builder $builder): void
    {
        $builder->macro(
            'withoutArchived',
            function (Builder $builder) {
                $model = $builder->getModel();

                $builder->withoutGlobalScope($this)->whereNull(
                    $model->getQualifiedArchivedAtColumn(),
                );

                return $builder;
            },
        );
    }

    /**
     * Add the with-archived extension to the builder.
     *
     * @param Builder $builder The query builder.
     */
    protected function addWithArchived(Builder $builder): void
    {
        $builder->macro(
            'withArchived',
            function (Builder $builder) {
                return $builder->withoutGlobalScope($this);
            },
        );
    }

    /**
     * Add the only-Archived extension to the builder.
     *
     * @param Builder $builder The query builder.
     */
    protected function addOnlyArchived(Builder $builder): void
    {
        $builder->macro(
            'onlyArchived',
            function (Builder $builder) {
                $model = $builder->getModel();

                $builder->withoutGlobalScope($this)->whereNotNull(
                    $model->getQualifiedArchivedAtColumn(),
                );

                return $builder;
            },
        );
    }
}
