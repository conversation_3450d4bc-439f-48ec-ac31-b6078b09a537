<?php

namespace Cfa\Common\Application\Traits;

use Carbon\Carbon;
use Cfa\Common\Application\Traits\Scopes\DeactivatingScope;
use Illuminate\Database\Eloquent\Builder;

/**
 * Trait Deactivates
 *
 * @method static Builder|$this onlyDeactivated()
 * @method static Builder|$this withDeactivated()
 * @method static Builder|$this withoutDeactivated()
 */
trait Deactivates
{
    public static bool $returnsDeactivated = false;

    /**
     * Boot the deactivating trait for a model.
     */
    public static function bootDeactivates(): void
    {
        static::addGlobalScope(new DeactivatingScope());
    }

    public static function enableDeactivated(): void
    {
        self::$returnsDeactivated = true;
    }

    public static function disableDeactivated(): void
    {
        self::$returnsDeactivated = false;
    }

    public function initializeTouchedByUser(): void
    {
        if (!isset($this->casts[$this->getDeactivatedAtColumn()])) {
            $this->casts[$this->getDeactivatedAtColumn()] = 'datetime';
        }

        if (!in_array($this->getDeactivatedAtColumn(), $this->visible)) {
            $this->visible[] = $this->getDeactivatedAtColumn();
        }
    }

    /**
     * Deactivate a model instance.
     */
    public function deactivate(): ?bool
    {
        $this->setAttribute($this->getDeactivatedAtColumn(), Carbon::now());

        return $this->save();
    }

    /**
     * Activate a deactivated model instance.
     */
    public function activate(): ?bool
    {
        $this->setAttribute($this->getDeactivatedAtColumn(), null);

        return $this->save();
    }

    /**
     * Determine if the model instance has been deactivated.
     */
    public function deactivated(): bool
    {
        return !is_null($this->getAttribute($this->getDeactivatedAtColumn()));
    }

    /**
     * Get the name of the "deactivated at" column.
     */
    public function getDeactivatedAtColumn(): string
    {
        return 'deactivated_at';
    }

    /**
     * Get the fully qualified "deactivated at" column.
     */
    public function getQualifiedDeactivatedAtColumn(): string
    {
        return $this->getTable() . '.' . $this->getDeactivatedAtColumn();
    }
}
