<?php

namespace Cfa\Common\Application\Traits;

use Cfa\Admin\Domain\CmsUser\CmsUser;
use Cfa\Common\Domain\User\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

trait TouchedByUser
{
    /**
     * Can be used to disable the events touched by the user.
     *
     * @var bool
     */
    protected static $touchedByUser = true;

    /**
     * Observe create, update and save events on the model and set created_by and updated_by values to current user.
     */
    protected static function bootTouchedByUser(): void
    {
        static::creating(function ($model): void {
            if (self::$touchedByUser) {
                self::setFieldToUserId($model, self::getCreatorIdColumn());
            }
        });

        static::saving(function ($model): void {
            if (self::$touchedByUser) {
                self::setFieldToUserId($model, self::getUpdaterIdColumn());
            }
        });
    }

    public function initializeTouchedByUser(): void
    {
        if (!isset($this->casts[self::getCreatorIdColumn()])) {
            $this->casts[self::getCreatorIdColumn()] = 'integer';
        }

        if (!isset($this->casts[self::getUpdaterIdColumn()])) {
            $this->casts[self::getUpdaterIdColumn()] = 'integer';
        }
    }

    /**
     * Set the touchedByUser so you can enable or disable it.
     *
     * @param bool $enabled Flag to enable or disable.
     */
    public function setTouchedByUser(bool $enabled): void
    {
        self::$touchedByUser = $enabled;
    }

    /**
     * Set the value of the field to the current user's id.
     *
     * @param Model $model The model to apply the changes to.
     * @param string $fieldName The name of the field to apply the changes to.
     */
    private static function setFieldToUserId(Model $model, string $fieldName): void
    {
        $user = Auth::user();
        $model->$fieldName = $user instanceof CmsUser ? null : ($user->id ?? null);
    }

    /**
     * Get the user that created the model.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, self::getCreatorIdColumn());
    }

    /**
     * Get the user that last updated the model.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, self::getUpdaterIdColumn());
    }

    protected static function getCreatorIdColumn(): string
    {
        return 'creator_id';
    }

    protected static function getUpdaterIdColumn(): string
    {
        return 'updater_id';
    }
}
