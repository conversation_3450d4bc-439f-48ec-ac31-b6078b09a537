<?php

namespace Cfa\Common\Application\Listeners\PermissionSchoolCreated;

use Cfa\Common\Domain\Icon\Icon;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\Permission\PermissionSchoolCreated;
use Cfa\Evaluation\Domain\QuotationSystem\Quotation\Quotation;
use Cfa\Evaluation\Domain\QuotationSystem\QuotationSystem;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AddPredefinedQuotationSystems extends PermissionSchoolCreatedListener
{
    /**
     * @var array
     */
    private $icons = [];

    /**
     * Handle the event.
     */
    public function handle(PermissionSchoolCreated $event): void
    {
        /* @var School $school */
        $school = $event->permissionSchool->school;

        if ($event->permissionSchool->name !== PermissionName::HasAccessToEvaluation) {
            return;
        }

        if (QuotationSystem::where('school_id', $school->id)->exists()) {
            return;
        }

        $predefinedQuotationSystems = config('quotation_systems.' . tenant()->id->value);
        foreach ($predefinedQuotationSystems as $name => $quotationSystemConfig) {
            try {
                DB::transaction(function () use ($name, $school, $quotationSystemConfig): void {
                    $quotationSystem = QuotationSystem::forceCreate(
                        [
                            'name' => $name,
                            'school_id' => $school->id,
                            'type' => $quotationSystemConfig['type'],
                        ],
                    );
                    $this->addQuotationsToQuotationSystem($quotationSystemConfig['quotations'], $quotationSystem);
                });
            } catch (ModelNotFoundException $exception) {
                Log::warning('Unsupported Predefined Quotation Systems Config: ' . $exception->getMessage());
                continue;
            }
        }
    }

    private function getIcon(string $iconHandle): Icon
    {
        if (array_key_exists($iconHandle, $this->icons)) {
            return $this->icons[$iconHandle];
        }
        /** @var Icon $icon */
        $icon = Icon::where(['handle' => $iconHandle])->firstOrFail();
        $this->icons[$iconHandle] = $icon;

        return $icon;
    }

    private function addQuotationsToQuotationSystem(array $quotations, QuotationSystem $quotationSystem): void
    {
        $order = 0;
        foreach ($quotations as $quotationConfig) {
            $icon = $this->getIcon($quotationConfig['icon']);
            Quotation::forceCreate(
                [
                    'order' => $order++,
                    'icon_id' => $icon->id,
                    'label' => $quotationConfig['label'],
                    'color' => $quotationConfig['color'],
                    'icon_color' => $quotationConfig['color'],
                    'icon_count' => $quotationConfig['iconCount'],
                    'quotation_system_id' => $quotationSystem->id,
                ],
            );
        }
    }
}
