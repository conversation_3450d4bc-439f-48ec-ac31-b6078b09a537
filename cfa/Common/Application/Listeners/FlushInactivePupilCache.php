<?php

namespace Cfa\Common\Application\Listeners;

use Cfa\Care\Domain\CareInfo\CareInfoSavedEvent;
use Cfa\Common\Domain\School\SchoolRepositoryInterface;

class FlushInactivePupilCache
{
    public function __construct(
        private readonly SchoolRepositoryInterface $schoolRepository,
    ) {}

    public function handle(CareInfoSavedEvent $event): void
    {
        if ($event->careInfo->wasChanged('pupil_status') === false) {
            return;
        }

        $this->schoolRepository->flushInactivePupilsCache($event->careInfo->school);
    }
}
