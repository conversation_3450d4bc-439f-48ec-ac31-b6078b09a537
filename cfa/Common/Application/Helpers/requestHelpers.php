<?php

use Cfa\Common\Application\Exceptions\NoActiveSchoolsException;
use Cfa\Common\Application\Services\Helpers\GroupHelperService;
use Cfa\Common\Application\Services\Helpers\SchoolHelperService;
use Cfa\Common\Application\Services\Helpers\TenantHelperService;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Tenant\Tenant;

/**
 * Get an instance of the tenant applicable for the request or the default if none is set.
 */
function tenant(): Tenant
{
    return app(TenantHelperService::class)->tenant();
}

/**
 * Retrieve the selected school related to the request.
 *
 * @param School|null $school School that has to be saved for this user.
 *
 * @throws NoActiveSchoolsException Thrown if the cookie is empty.
 */
function school(?School $school = null): School
{
    return app(SchoolHelperService::class)->school($school);
}

/**
 * Retrieve the selected group related to the request.
 *
 * @param Group|null $group Group that has to be saved for this user.
 *
 * @throws NoActiveSchoolsException Thrown if linked school is not available for user.
 */
function group(?Group $group = null): ?Group
{
    return app(GroupHelperService::class)->group($group);
}
