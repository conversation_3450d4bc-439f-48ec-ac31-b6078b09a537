<?php

namespace Cfa\Common\Application\Repositories;

use App\Models\Model;
use App\Repositories\Repository;
use Carbon\Carbon;
use Cfa\Common\Domain\Permission\CollectionLicense;
use Cfa\Common\Domain\Permission\CollectionLicenseRepositoryInterface;
use Cfa\Common\Domain\Permission\LicensePortalLicenceType;
use Cfa\Common\Domain\Permission\LicensePortalProductType;
use Illuminate\Database\Eloquent\Builder;
use Override;

class CollectionLicenseRepository extends Repository implements CollectionLicenseRepositoryInterface
{
    #[Override]
    public function getModel(): Model
    {
        return new CollectionLicense();
    }

    public function scopeActiveLicense(Builder $query, ?Carbon $date = null): Builder
    {
        $date ??= Carbon::now();

        return $query
            ->where('startdate', '<=', $date)
            ->where(fn(Builder $q)
                => $q
                    ->where('enddate', '>=', $date)
                    ->orWhereNull('enddate'));
    }

    public function scopeLicenseEligibleForCollectionActivation(Builder $query, ?Carbon $date = null): Builder
    {
        $licenseTypesToActivate = [
            LicensePortalLicenceType::HasAccessToMethodsPaying,
            LicensePortalLicenceType::CanAccessBoardBookStreaming,
            LicensePortalLicenceType::HasAccessToDc,
        ];

        $licenseProductTypesToActivate = [
            LicensePortalProductType::Yearbook,
        ];

        $query
            ->whereIn('license_type', $licenseTypesToActivate)
            ->whereIn('product_type', $licenseProductTypesToActivate);

        return $this->scopeActiveLicense($query, $date);
    }

    public function scopeLicenseCreatedOrUpdatedRecently(Builder $query, int $minutesRange): Builder
    {
        $threshold = Carbon::now()->subMinutes($minutesRange);

        return $query->where(function (Builder $q) use ($threshold): void {
            $q->where('created_at', '>=', $threshold)
                ->orWhere('updated_at', '>=', $threshold);
        });
    }
}
