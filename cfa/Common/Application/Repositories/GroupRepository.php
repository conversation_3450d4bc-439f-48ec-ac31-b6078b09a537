<?php

namespace Cfa\Common\Application\Repositories;

use App\Models\Model;
use App\Repositories\Repository;
use Carbon\Carbon;
use Cfa\Common\Application\Services\Filters\FilterGroupsWithPupils;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\Group\GroupRepositoryInterface;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudience;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\Role;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\Settings\EvaluationSubjectPermission\EvaluationSubjectPermission;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Override;

class GroupRepository extends Repository implements GroupRepositoryInterface
{
    /** @var SchoolyearRepositoryInterface */
    private $schoolyearRepository;

    /** @var Group */
    protected Model $model;

    public function __construct(SchoolyearRepositoryInterface $schoolyearRepository)
    {
        parent::__construct();
        $this->schoolyearRepository = $schoolyearRepository;
    }

    #[Override]
    public function getModel(): Model
    {
        return new Group();
    }

    #[Override]
    public function getGroupsForUserAndSchool(User $user, School $school, ?Schoolyear $schoolyear = null): Collection
    {
        return $this->getGroupsForUserAndSchoolBuilder($user, $school, $schoolyear)->get();
    }

    #[Override]
    public function getGroupIdsForUserAndSchool(User $user, School $school, Schoolyear $schoolyear): Collection
    {
        return $this->getGroupsForUserAndSchoolBuilder($user, $school, $schoolyear)->pluck('groups.id');
    }

    private function getGroupsForUserAndSchoolBuilder(
        User $user,
        School $school,
        ?Schoolyear $schoolyear = null,
    ): Builder {
        $isCurrentSchoolyear = $schoolyear === null || $schoolyear->isCurrent();
        $careers = $isCurrentSchoolyear ? $user->activeCareers() : $user->careersBySchoolYear($schoolyear);

        if ($user->hasAccessToAllGroups($school)) {
            return $this->getGroupsForSchoolBuilder($careers, $school, $schoolyear);
        }

        return $this->getGroupsLinkedToCareersBuilder($careers, $school);
    }

    protected function getGroupsForSchoolBuilder(
        Collection $careers,
        School $school,
        ?Schoolyear $schoolyear = null,
    ): Builder {
        if ($schoolyear === null) {
            $schoolyear = Schoolyear::getRepository()->getCurrent();
        }

        return $this->model
            ->where('groups.school_id', $school->id)
            ->whereIn(
                'groups.id',
                function (QueryBuilder $builder) use ($school, $schoolyear, $careers): void {
                    $builder->select('group_id')
                        ->distinct()
                        ->from('careers')
                        ->where('groups.school_id', $school->id)
                        ->whereNotNull('group_id')
                        ->whereNull('deleted_at')
                        ->where('startdate', '<=', $schoolyear->end)
                        ->where(function (QueryBuilder $builder) use ($careers, $school): void {
                            $builder->where('groups.is_classgroup', true)
                                ->orWhereIn(
                                    'groups.id',
                                    $careers->where('school_id', $school->id)
                                        ->whereNotNull('group_id')
                                        ->pluck('group_id')
                                        ->unique(),
                                );
                        })
                        ->where(function (QueryBuilder $builder) use ($schoolyear): void {
                            $builder->whereNull('enddate')
                                ->orWhere('enddate', '>', $schoolyear->start);
                        });
                },
            )
            ->orderBy('is_classgroup', 'desc')
            ->orderBy('target_audience_type', 'asc')
            ->orderBy('natural_study_year', 'asc')
            ->orderBy('name', 'asc');
    }

    #[Override]
    public function getGroupIdsOfGroupsWithPupilsForSchool(School $school, ?Schoolyear $schoolyear = null): Collection
    {
        $periodStart = Carbon::now();
        $periodEnd = Carbon::now();

        if ($schoolyear !== null && !$schoolyear->isCurrent()) {
            $periodStart = $schoolyear->start;
            $periodEnd = $schoolyear->end;
        }

        return Career::query()
            ->distinct()
            ->join('roles', 'careers.role_id', 'roles.id')
            ->where('roles.role_name_enum', RoleName::Pupil->value)
            ->where('startdate', '<=', $periodEnd)
            ->where(
                fn(Builder $query): Builder => $query->where('enddate', '>=', $periodStart)->orWhereNull('enddate'),
            )
            ->whereNotNull('group_id')
            ->where('school_id', $school->id)
            ->pluck('group_id');
    }

    #[Override]
    public function getClassGroupsForUserAndSchool(
        User $user,
        School $school,
        ?Schoolyear $schoolyear = null,
    ): Collection {
        return $this->getGroupsForUserAndSchool($user, $school, $schoolyear)
            ->filter(fn(Group $group): bool => $group->is_classgroup)
            ->values();
    }

    #[Override]
    public function getClassGroupsForUserAndSchoolHavingReportPeriods(
        User $user,
        School $school,
        ?Schoolyear $schoolyear = null,
    ): Collection {
        if ($schoolyear === null) {
            $schoolyear = Schoolyear::getRepository()->getCurrent();
        }

        return $this->getGroupsForUserAndSchoolBuilder($user, $school, $schoolyear)
            ->join(
                'target_audiences',
                fn(JoinClause $joinClause): JoinClause => $joinClause
                    ->whereColumn('target_audiences.natural_study_year', 'groups.natural_study_year')
                    ->whereColumn('target_audiences.type', 'groups.target_audience_type'),
            )
            ->distinct()
            ->join(
                'report_settings_target_audience',
                'target_audiences.id',
                'report_settings_target_audience.target_audience_id',
            )
            ->join(
                'report_periods',
                'report_settings_target_audience.report_settings_id',
                'report_periods.report_settings_id',
            )
            ->where('report_periods.school_id', $school->id)
            ->where('report_periods.schoolyear_id', $schoolyear->id)
            ->whereNull('report_periods.deleted_at')
            ->get('groups.*');
    }

    #[Override]
    public function getAllGroupsOfSchool(School $school): Collection
    {
        return Group::where('school_id', $school->id)->whereNull('schoolyear_id')->orderBy('name')->get();
    }

    /** @SuppressWarnings(PHPMD.UnusedLocalVariable) */
    #[Override]
    public function getGroupsWithPupilsForUserByTargetAudiences(
        Collection $targetAudiences,
        User $user,
        School $school,
        ?Schoolyear $schoolyear = null,
    ): Collection {
        $groups = $this->getGroupsByTargetAudiences($targetAudiences, $user, $school, $schoolyear);

        return app(FilterGroupsWithPupils::class)
            ->forSchoolyear($schoolyear)($school, $groups)
            ->sortBy('name')
            ->values();
    }

    #[Override]
    public function getGroupsByTargetAudiences(
        Collection $targetAudiences,
        User $user,
        School $school,
        ?Schoolyear $schoolyear,
    ): Collection {
        $targetAudienceNames = $targetAudiences
            ->map(fn(TargetAudience $targetAudience): string => $targetAudience->type->name .
                $targetAudience->natural_study_year);

        return ($this->cacheRepository ?? $this)
            ->getGroupsForUserAndSchool($user, $school, $schoolyear)
            ->filter(fn(Group $group): bool => $targetAudienceNames->contains(
                $group->target_audience_type->name . $group->natural_study_year,
            ))
            ->values();
    }

    #[Override]
    public function getGroupsForUserBySubjectPermissions(User $user, School $school, Schoolyear $schoolyear): Collection
    {
        $groupIds = EvaluationSubjectPermission::forUserAndSchool($user, $school, $schoolyear)
            ->select('evaluation_subject_permissions.group_id')
            ->distinct()
            ->join(
                'report_periods',
                'report_settings_target_audience.report_settings_id',
                'report_periods.report_settings_id',
            )
            ->where('report_periods.school_id', $school->id)
            ->where('report_periods.schoolyear_id', $schoolyear->id)
            ->whereNull('report_periods.deleted_at')
            ->pluck('group_id');

        return Group::whereIn('id', $groupIds)->orderBy('name')->get();
    }

    /** Get the groups linked to the collection of careers. */
    private function getGroupsLinkedToCareersBuilder(Collection $careers, School $school): Builder
    {
        return Group::withoutGlobalScopes()
            ->where('groups.school_id', $school->id)
            ->whereIn(
                'groups.id',
                $careers->where('school_id', $school->id)
                    ->whereNotNull('group_id')
                    ->pluck('group_id')
                    ->unique(),
            )
            ->orderBy('is_classgroup', 'desc')
            ->orderBy('target_audience_type', 'asc')
            ->orderBy('natural_study_year', 'asc')
            ->orderBy('name', 'asc');
    }

    #[Override]
    public function flushGroupCacheForSchool(School $school): void
    {
        // Does nothing.
    }

    #[Override]
    public function getLinkedCalendaritemsWithGoals(
        Group $group,
        array $dateRange,
        string $parentNodeUid,
        ?User $owner = null,
    ): Collection {
        return $group
            ->calendaritems()
            ->select(['calendaritems.*'])
            ->distinct()
            ->join('calendaritemrows', 'calendaritemrows.calendaritem_id', 'calendaritems.id')
            ->join('records', 'records.id', 'calendaritemrows.record_id')
            ->join('curriculumnode_record', 'curriculumnode_record.record_id', 'records.id')
            ->join(
                'curriculumnode_curriculumnode',
                'curriculumnode_curriculumnode.child_id',
                'curriculumnode_record.curriculumnode_id',
            )
            ->join('curriculumnodes', 'curriculumnodes.id', 'curriculumnode_curriculumnode.parent_id')
            ->inDateInterval($dateRange['fromStartOfDay'], $dateRange['untilEndOfDay'])
            ->when(
                $owner,
                function ($query) use ($owner) {
                    return $query->where('calendaritems.owner_id', $owner->id);
                },
            )
            ->where('curriculumnodes.uid', $parentNodeUid)
            ->get()
            ->load(
                'owner',
                'rows.record.curriculumnodes.parents.parents',
                'rows.record.curriculumnodes.parents.gradelevelconfigurations',
                'rows.record.curriculumnodes.gradelevelconfigurations',
            );
    }

    #[Override]
    public function getUsersWithAccessToGroups(School $school, Collection $groups): Collection
    {
        if ($groups->isEmpty()) {
            return collect();
        }

        $now = Carbon::now();

        return User::distinct()
            ->join('careers', 'users.id', 'careers.user_id')
            ->join('roles', 'careers.role_id', 'roles.id')
            ->join('permission_role_names', 'roles.role_name_enum', 'permission_role_names.role_name_enum')
            ->where('startdate', '<=', $now)
            ->where(fn(Builder $query): Builder => $query->where('enddate', '>', $now)->orWhereNull('enddate'))
            ->where(
                fn(Builder $query): Builder => $query->whereIn('group_id', $groups->pluck('id'))
                    ->orWhere('permission_role_names.name', PermissionName::HasAccessToAllGroups),
            )
            ->where('roles.role_name_enum', '<>', RoleName::Pupil)
            ->where('school_id', $school->id)
            ->whereNull('careers.deleted_at')
            ->get(['users.id', 'users.uid', 'users.firstname', 'users.lastname']);
    }

    #[Override]
    public function getPupilsOfGroup(Group $group, ?Schoolyear $schoolyear = null): Collection
    {
        $startOfPeriod = Carbon::now();
        $endOfPeriod = Carbon::now();

        if ($schoolyear !== null && !$schoolyear->isCurrent()) {
            $startOfPeriod = $schoolyear->start;
            $endOfPeriod = $schoolyear->end;
        }

        return Pupil::with(['careInfos' => fn(HasMany $query) => $query->where('school_id', $group->school_id)])
            ->select(['users.*', 'careers.class_number'])
            ->where('startdate', '<=', $endOfPeriod)
            ->where(
                fn(Builder $query): Builder => $query->where('enddate', '>', $startOfPeriod)->orWhereNull('enddate'),
            )
            ->where('group_id', $group->id)
            ->orderByRaw('
                SUBSTR(
                    "000000" || CASE WHEN careers.class_number IS NOT NULL THEN careers.class_number ELSE 999999 END,
                     -6,
                     6
                ) || "-" || users.lastname || "-" || users.firstname
            ')
            ->get();
    }

    #[Override]
    public function recentlyAccessedGroupsIds(User $user, School $school, int $count = 5): Collection
    {
        $groupIds = $this->getGroupsForUserAndSchool($user, $school)->pluck('id');

        return $user->accessedGroups()
            ->whereIn('group_id', $groupIds)
            ->withPivot('accessed_at')
            ->latest('accessed_at')
            ->limit($count)
            ->pluck('id');
    }

    #[Override]
    public function getHistoricalGroupsForPupilsInSchoolYear(array $pupilsIds, Schoolyear $schoolyear): Collection
    {
        return Group::join('careers', 'careers.group_id', '=', 'groups.id')
            ->select(['groups.*'])
            ->whereIn('careers.user_id', $pupilsIds)
            ->whereBetween('careers.startdate', [$schoolyear->start, $schoolyear->end])
            ->whereNotNull('careers.enddate')
            ->whereNull('careers.deleted_at')
            ->distinct()
            ->get();
    }

    public function flushUsersOfGroup(Group $group, Role $role, Carbon $startOfPeriod, Carbon $endOfPeriod): void
    {
        // Does nothing.
    }

    #[Override]
    public function flushPupilsOfGroup(Group $group, ?Schoolyear $schoolyear = null): void
    {
        // Does nothing.
    }
}
