<?php

declare(strict_types=1);

namespace Cfa\Common\Application\Repositories;

use Cfa\Common\Application\Services\Helpers\SchoolHelperService;
use Cfa\Common\Domain\School\School;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Context;
use Illuminate\Support\Facades\Session;

use function collect;

class UserAccessRepository
{
    private const SESSION_KEY = 'UserAccessRepository.schools';

    /** @param Collection<int, School> $schools */
    public function setSchoolsForUser(Collection $schools): void
    {
        Session::put(self::SESSION_KEY, $schools->pluck('id'));
    }

    public function hasAccessToSchool(School $school): bool
    {
        Context::push(SchoolHelperService::class, 'hasAccessToSchool');
        $hasAccess = Session::get(self::SESSION_KEY, collect())->contains($school->id);
        Context::push(SchoolHelperService::class, ['$hasAccess' => $hasAccess]);

        return $hasAccess;
    }
}
