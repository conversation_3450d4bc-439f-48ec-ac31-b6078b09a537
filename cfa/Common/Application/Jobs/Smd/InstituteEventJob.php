<?php

namespace Cfa\Common\Application\Jobs\Smd;

use Carbon\Carbon;
use Cfa\Common\Domain\School\Institute\Institute;
use Override;

class InstituteEventJob extends SmdJob
{
    protected string $modelClass = Institute::class;

    protected array $addressPayload;

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function transformPayload(): array
    {
        $payload = $this->getPayload();

        if (isset($payload['address'])) {
            $this->addressPayload = $this->transformAddress($payload['address']);
        }

        return array_merge(parent::transformPayload(), [
            'uid' => $payload['uid'],
            'institute_number' => $payload['instituteNumber'],
            'school_title' => $payload['schoolTitle'],
            'long_name' => $payload['longName'],
            'short_name' => $payload['shortName'],
            'nis_code' => $payload['nisCode'],
            'crab_code' => $payload['crabCode'],
            'im_number' => $payload['imNumber'],
            'school_community_number' => $payload['schoolCommunityNumber'] ?? null,
            'admin_first_name' => $payload['adminFirstName'] ?? null,
            'admin_last_name' => $payload['adminLastName'] ?? null,
            'start_date' => isset($payload['startDate']) ? Carbon::parse($payload['startDate']) : null,
            'end_date' => isset($payload['endDate']) ? Carbon::parse($payload['endDate']) : null,
            'email' => $payload['email'] ?? null,
            'fax' => $payload['fax'] ?? null,
            'phone' => $payload['phone'] ?? null,
            'website' => $payload['website'] ?? null,
            'foundation_date' => isset($payload['foundationDate']) ? Carbon::parse($payload['foundationDate']) : null,
            'closedown_date' => isset($payload['closedownDate']) ? Carbon::parse($payload['closedownDate']) : null,
            'active' => $payload['active'] ?? null,
        ]);
    }

    #[Override]
    protected function saveModel(array $payload): void
    {
        $payload['address_id'] = isset($this->addressPayload) ? $this->saveAddress($this->addressPayload)->id : null;
        parent::saveModel($payload);
    }
}
