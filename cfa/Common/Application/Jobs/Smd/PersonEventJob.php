<?php

namespace Cfa\Common\Application\Jobs\Smd;

use App\Models\Feature\Feature;
use App\Models\Feature\FeatureToggle;
use Carbon\Carbon;
use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\CareInfo\PupilStatus;
use Cfa\Common\Application\Jobs\Wisa\RequestWisaCareDataForUser;
use Cfa\Common\Application\Services\Users\PupilDataExistsService;
use Cfa\Common\Application\Services\Wisa\WisaCareDataMatch;
use Cfa\Common\Application\Services\Wisa\WisaCareDataMatcher;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\CareersSaved;
use Cfa\Common\Domain\User\Career\Role\Role;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\Gender;
use Cfa\Common\Domain\User\PupilChange\PupilChange;
use Cfa\Common\Domain\User\PupilChange\PupilChangeHandledStatus;
use Cfa\Common\Domain\User\SchoolUserAccess\SchoolUserAccess;
use Cfa\Common\Domain\User\User;
use Cfa\Wisa\Domain\CareData\WisaCareData;
use DomainException;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use LogicException;
use Override;

use function app;
use function array_key_exists;
use function collect;
use function in_array;

/** @SuppressWarnings(PHPMD.ExcessiveClassComplexity) */
class PersonEventJob extends SmdJob
{
    protected string $modelClass = User::class;

    protected Collection $careerPayloads;

    protected array $addressPayload;

    protected bool $active;

    /** @var array<string, School> */
    private array $schools = [];

    /** @var array<string, Role> */
    private $roles = [];

    private ?WisaCareDataMatch $wisaCareDataMatch;
    private int $pupilRoleId;

    public function __construct(array $payload, array $event)
    {
        parent::__construct($payload, $event);

        $this->careerPayloads = collect();
    }

    #[Override]
    public function transformPayload(): array
    {
        $payload = $this->getPayload();

        if (isset($payload['address'])) {
            $this->addressPayload = $this->transformAddress($payload['address']);
        }

        if (isset($payload['careers'])) {
            $this->careerPayloads = collect($payload['careers'])
                ->map(function (array $careerPayload) {
                    return $this->transformCareer($careerPayload);
                });
        }

        if (isset($payload['active'])) {
            $this->active = $payload['active'];
        }

        return array_merge(
            parent::transformPayload(),
            [
                'firstname' => $payload['firstName'],
                'lastname' => $payload['lastName'],
                'gender' => isset($payload['gender']) ? Gender::fromName($payload['gender']) : null,
                'date_of_birth' => isset($payload['dateOfBirth']) ? Carbon::parse($payload['dateOfBirth']) : null,
                'phone' => $payload['phone'] ?? null,
                'cellphone' => $payload['cellphone'] ?? null,
                'email' => Arr::first($payload['emails']) ?? null,
                'official_teacher_number' => $payload['officialNumber'] ?? null,
            ],
        );
    }

    private function transformCareer(array $careerPayload): array
    {
        $role = $this->getRole($careerPayload['roleType']);
        $school = $this->getSchool($careerPayload['schoolUid']);
        $group = empty($careerPayload['groupUid'])
            ? null
            : Group::withoutGlobalScopes()->whereUid($careerPayload['groupUid'])->firstOrFail();

        return [
            'uid' => $careerPayload['uid'],
            'role_id' => $role->id,
            'school_id' => $school->id,
            'group_id' => $group?->id,
            'group_name' => $group?->name,
            'group_data' => [
                'natural_study_year' => $group?->natural_study_year,
                'target_audience_type' => $group?->target_audience_type,
                'is_classgroup' => $group?->is_classgroup,
            ],
            'startdate' => Carbon::parse($careerPayload['startDate']),
            'enddate' => empty($careerPayload['endDate']) ? null : Carbon::parse($careerPayload['endDate']),
            'class_number' => $careerPayload['classNumber'] ?? null,
            'repeating_year' => 0,
            'deleted_at' => null,
        ];
    }

    #[Override]
    protected function saveModel(array $transformedPayload): void
    {
        if ($this->shouldPersonEventBeImported($transformedPayload) === false) {
            return;
        }

        $transformedPayload = $this->deletePersonIfInactive($transformedPayload);
        $this->logSuspiciousChanges($this->model, $transformedPayload);
        $transformedPayload = $this->setAddress($transformedPayload);

        parent::saveModel($transformedPayload);

        DB::transaction(function (): void {
            $this->saveCareerModels();
            $this->saveCareInfos();
            $this->saveSchoolUserAccess();
        });

        $this->runEventsForCareers();
        $this->requestWisaData();
        $this->flushCacheForUser();
    }

    public function deletePersonIfInactive(array $transformedPayload): array
    {
        if (!FeatureToggle::isActive(Feature::PersonEventJobActiveStatus)) {
            return $transformedPayload;
        }

        // Inactive persons are immediately set as deleted so they can't be used.
        // They must be created though, so they can be merged later on.
        if ($this->active !== true && $this->isPupil() === false) {
            $transformedPayload['deleted_at'] = Carbon::now();
        }

        return $transformedPayload;
    }

    public function setAddress(array $transformedPayload): array
    {
        $transformedPayload['address_id'] = isset($this->addressPayload)
            ? $this->saveAddress($this->addressPayload)->id
            : null;

        return $transformedPayload;
    }

    private function saveCareerModels(): void
    {
        // The deleteModel method uses this method to soft delete the user.
        // Since the careers don't need to be saved again, we check if the user is deleted or not.
        if ($this->model->trashed() && $this->active === true) {
            return;
        }

        $this->calculateRepeatingYears();

        $existingCareers = Career::withTrashed()
            ->whereIn('uid', $this->careerPayloads->pluck('uid'))
            ->orWhere('user_id', $this->model->id)
            ->get();
        $existingCareerUids = $existingCareers->pluck('uid');
        $this->checkForConflictingCareers($existingCareers);

        [$careersToUpdate, $careersToCreate] = $this->careerPayloads->partition(
            fn(array $careerPayload): bool => $existingCareerUids->contains($careerPayload['uid']),
        );

        $this->updateCareers($careersToUpdate, $existingCareers);
        $this->createCareers($careersToCreate);
        $this->deleteCareers($careersToUpdate, $existingCareerUids);
    }

    private function checkForConflictingCareers(Collection $existingCareers): void
    {
        $pupilRoleId = ($this->roles['PUPIL'] ?? null)?->id;
        if ($pupilRoleId === null) {
            return;
        }

        if (count($this->roles) > 1) {
            throw new DomainException('Pupil message contains other roles');
        }

        if ($existingCareers->contains(fn(Career $career): bool => $career->role_id !== $pupilRoleId)) {
            throw new DomainException('Existing pupil has conflicting roles');
        }
    }

    public function calculateRepeatingYears(): void
    {
        if (!$this->isPupil()) {
            return;
        }

        $previousSchoolyear = app(SchoolyearRepositoryInterface::class)->getPrevious();

        $this->careerPayloads = $this->careerPayloads->map(function (array $career) use ($previousSchoolyear) {
            if ($career['group_data']['is_classgroup'] !== true) {
                return $career;
            }

            $previousGroup = $this->model->lastActiveClassGroupOfSchoolyear(
                $career['school_id'],
                $previousSchoolyear,
            );

            if (
                $previousGroup !== null &&
                $career['group_data']['target_audience_type'] === $previousGroup->target_audience_type &&
                $career['group_data']['natural_study_year'] === $previousGroup->natural_study_year
            ) {
                $repeatingYear = $this->model
                    ->careersBySchoolYear($previousSchoolyear)
                    ->where('group_id', $previousGroup->id)
                    ->value('repeating_year') + 1;
                // @codeCoverageIgnoreStart
                if ($repeatingYear > Career::REPEATING_YEAR_MAX_VALUE && app()->environment('production')) {
                    report(
                        new LogicException(
                            sprintf(
                                'Repeating year reached maximum value of %s for user: %s',
                                Career::REPEATING_YEAR_MAX_VALUE,
                                $this->model->getKey(),
                            ),
                        ),
                    );
                }
                // @codeCoverageIgnoreEnd
                $career['repeating_year'] = min($repeatingYear, Career::REPEATING_YEAR_MAX_VALUE);
            }

            return $career;
        });
    }

    public function requestWisaData(): void
    {
        if (!isset($this->wisaCareDataMatch)) {
            return;
        }

        if ($this->wisaCareDataMatch->getWisaUid() === null) {
            return;
        }

        if ($this->wisaCareDataMatch->hasWisaCareData()) {
            return;
        }

        $this->chain([new RequestWisaCareDataForUser($this->wisaCareDataMatch->getWisaUid())]);
    }

    private function saveCareInfos(): void
    {
        if (!$this->isPupil()) {
            return;
        }

        $nationalRegisterNumberHash = isset($this->wisaCareDataMatch)
            ? $this->wisaCareDataMatch->getWisaNationalRegisterNumberHash()
            : null;
        $wisaUid = isset($this->wisaCareDataMatch) ? $this->wisaCareDataMatch->getWisaUid() : null;

        $this->careerPayloads->pluck('school_id')->each(
            function (int $schoolId) use ($nationalRegisterNumberHash, $wisaUid): void {
                $careInfo = CareInfo::createOrIgnoreCareInfo(
                    $this->model->id,
                    $schoolId,
                    $nationalRegisterNumberHash,
                    $wisaUid,
                );
                if (isset($this->wisaCareDataMatch) && $this->wisaCareDataMatch->getWisaCareData() !== null) {
                    $this->wisaCareDataMatch->getWisaCareData()->care_info_id = $careInfo->id;
                }
            },
        );
    }

    private function saveSchoolUserAccess(): void
    {
        $this->careerPayloads->pluck('school_id')->each(function (int $schoolId): void {
            $schoolUserAccess = SchoolUserAccess::withTrashed()
                ->firstOrNew(['school_id' => $schoolId, 'user_id' => $this->model->id]);
            $schoolUserAccess->deleted_at = null;
            $schoolUserAccess->save();
        });
    }

    /**
     * @param Collection<int, array> $careersToUpdate
     * @param Collection<int, Career> $existingCareers
     */
    private function updateCareers(Collection $careersToUpdate, Collection $existingCareers): void
    {
        $careersToUpdate->each(function (array $careerPayload) use ($existingCareers): void {
            $career = $existingCareers->firstWhere('uid', $careerPayload['uid']);

            $career->fill($careerPayload);
            if ($career->getOriginal('enddate') !== null) {
                $career['group_name'] = $career->getOriginal('group_name');
            }
            $career['user_id'] = $this->model->id;

            if (!$career->isDirty()) {
                return;
            }

            $career['smd_updated_at'] = $this->model->smd_updated_at;
            $career['smd_incremental_event_id'] = $this->model->smd_incremental_event_id;
            $career->save();
        });
    }

    /** @param Collection<int, array> $careersToCreate */
    private function createCareers(Collection $careersToCreate): void
    {
        $now = Carbon::now();
        $careersToCreate->transform(function (array $career) use ($now) {
            $career['user_id'] = $this->model->id;
            $career['smd_updated_at'] = $this->model->smd_updated_at;
            $career['smd_incremental_event_id'] = $this->model->smd_incremental_event_id;
            $career['updated_at'] = $now;
            $career['created_at'] = $now;
            unset($career['group_data']);

            return $career;
        });
        Career::insert($careersToCreate->toArray());
    }

    /**
     * @param Collection<int, array> $careersToUpdate
     * @param Collection<int, string> $existingCareerUids
     */
    private function deleteCareers(Collection $careersToUpdate, Collection $existingCareerUids): void
    {
        if ($this->isPupil()) {
            return;
        }

        $careerUidsFromMessageInDatabase = $careersToUpdate->pluck('uid');
        $careersToDeleteUids = $existingCareerUids->reject(
            fn(string $uid) => $careerUidsFromMessageInDatabase->contains($uid),
        );
        Career::whereIn('uid', $careersToDeleteUids)->where('role_id', '!=', $this->getPupilRoleId())->delete();
    }

    #[Override]
    protected function deleteModel(array $transformedPayload): void
    {
        $this->isPupil() ? $this->deletePupil($transformedPayload) : $this->deleteTeacher($transformedPayload);
        $this->flushCacheForUser();
    }

    private function deleteTeacher(array $transformedPayload): void
    {
        $now = Carbon::now();
        $this->model->careers()->update(['deleted_at' => $now, 'updated_at' => $now]);
        parent::deleteModel($transformedPayload);
    }

    private function deletePupil(array $transformedPayload): void
    {
        $now = Carbon::now();
        $transformedPayload['deleted_at'] = null;
        $this->model
            ->careers()
            ->whereNull('enddate')
            ->update(['enddate' => $now->copy()->startOfDay(), 'updated_at' => $now]);
        parent::saveModel($transformedPayload);
    }

    private function getPupilRoleId(): int
    {
        return $this->pupilRoleId ??= Role::getRepository()->findRoleByName(RoleName::Pupil)->id;
    }

    private function flushCacheForUser(): void
    {
        $this->model->load('careersWithTrashed.school');

        if ($userId = $this->model->id) {
            $this->model->getRepository()->flushUserCaches($userId);

            $schools = $this->model->careersWithTrashed->unique('school_id')->map->school->filter();

            $schools->each(function (School $school): void {
                $this->flushGroupCache($school);
            });
        }
    }

    private function flushGroupCache(School $school): void
    {
        Group::getRepository()->flushGroupCacheForSchool($school);
    }

    private function getSchool(string $schoolUid): School
    {
        if (!array_key_exists($schoolUid, $this->schools)) {
            $school = School::withoutGlobalScopes()
                ->whereUid($schoolUid)
                ->firstOrFail();
            $this->schools[$schoolUid] = $school;
        }

        return $this->schools[$schoolUid];
    }

    private function getRole(string $roleType): Role
    {
        if (!array_key_exists($roleType, $this->schools)) {
            $role = Role::findBySMDType($roleType);
            $this->roles[$roleType] = $role;
        }

        return $this->roles[$roleType];
    }

    private function shouldPersonEventBeImported(array $transformedPayload): bool
    {
        if ($this->model && $this->incrementalId && $this->model->smd_incremental_event_id > $this->incrementalId) {
            return false;
        }

        if (!$this->isFirstnameAndLastnameValid($transformedPayload['firstname'], $transformedPayload['lastname'])) {
            return false;
        }

        if ($this->model->id) {
            return true;
        }

        if (empty($this->payload['careers'])) {
            return false;
        }

        if ($this->isPupil() === false) {
            return true;
        }

        if ($this->isMissingWisaCareData()) {
            return false;
        }

        return $this->hasSchoolWithCareOrEvaluation();
    }

    public function isMissingWisaCareData(): bool
    {
        if (isset($this->wisaCareDataMatch) && $this->wisaCareDataMatch->getWisaUid() !== null) {
            return false;
        }

        return WisaCareData::query()
            ->where('school_id', Arr::first($this->schools)->id)
            ->where('updated_at', '>=', Schoolyear::getRepository()->getCurrent()->start)
            ->exists();
    }

    private function hasSchoolWithCareOrEvaluation(): bool
    {
        return collect($this->schools)->contains(function (School $school) {
            if ($school->deleted_at !== null) {
                return false;
            }

            $schoolPermissions = $school->permissions->pluck('name');

            return $schoolPermissions->contains(PermissionName::HasAccessToCare)
                || $schoolPermissions->contains(PermissionName::HasAccessToEvaluation);
        });
    }

    private function isFirstnameAndLastnameValid(string $firstname, string $lastname): bool
    {
        return Str::cleanAndNormalizeInput($firstname) !== '' &&
            Str::cleanAndNormalizeInput($lastname) !== '';
    }

    #[Override]
    public function findModel(array $transformedPayload): void
    {
        parent::findModel($transformedPayload);
        if ($this->model->id !== null || $this->isPupil() === false) {
            return;
        }

        // This optimization is meant to be used pre sms-extraction. Only one school should be known per pupil.
        $school = count($this->schools) === 1 ? Arr::first($this->schools) : null;
        if ($school === null) {
            return;
        }

        $groupData = $this->getActiveGroupData();
        if ($groupData['class_number'] === null || $groupData['name'] === null) {
            return;
        }

        $this->wisaCareDataMatch = app(WisaCareDataMatcher::class)->findModelByWisaCareData(
            $this->model,
            $school,
            $transformedPayload['firstname'],
            $transformedPayload['lastname'],
            $groupData['name'],
            $groupData['class_number'],
        );
        $this->model = $this->wisaCareDataMatch->getUser();
    }

    private function getActiveGroupData(): array
    {
        $now = Carbon::now();
        $activePayloadWithGroup = $this->careerPayloads->first(
            fn(array $careerPayload): bool => !empty($careerPayload['group_name']) &&
            $careerPayload['group_data']['is_classgroup'] &&
            ($careerPayload['enddate'] === null || $careerPayload['enddate']->isAfter($now)) &&
            $careerPayload['startdate']->lessThanOrEqualTo($now),
        );

        return [
            'name' => $activePayloadWithGroup['group_name'] ?? null,
            'class_number' => $activePayloadWithGroup['class_number'] ?? null,
        ];
    }

    private function isPupil(): bool
    {
        if (count($this->roles) !== 1) {
            return false;
        }

        return array_key_exists('PUPIL', $this->roles);
    }

    private function logSuspiciousChanges(User $originalUser, array $payload): void
    {
        if (!$this->model->exists || !$this->incrementalId) {
            return;
        }

        if ($this->isPupil() === false) {
            return;
        }

        // Do not warn about automated tests.
        if (in_array($originalUser->lastname, ['Automation', 'Automation(Update)'])) {
            return;
        }

        $normalizedOriginalName = Str::cleanAndNormalizeInputWithSorting(
            $originalUser->firstname,
            $originalUser->lastname,
        );
        $normalizedNewName = Str::cleanAndNormalizeInputWithSorting($payload['firstname'], $payload['lastname']);

        if (Str::matchWithDistance($normalizedOriginalName, $normalizedNewName)) {
            return;
        }

        $careInfo = CareInfo::wherePupilId($originalUser->id)
            ->where('school_id', $this->careerPayloads->value('school_id'))
            ->first(['id', 'pupil_status', 'wisa_uid', 'school_id', 'pupil_id']);
        $data = $this->getLogDataForSuspiciousNameChange($originalUser, $careInfo, $payload);
        $this->clearNationalRegisterNumberForNameChange($originalUser, $careInfo);
        if ($careInfo?->pupil_status === PupilStatus::SuspiciousNameChangeException) {
            unset($data['handled_at'], $data['handled_status']);
        }

        PupilChange::query()->insert($data);
    }

    private function getLogDataForSuspiciousNameChange(User $originalUser, ?CareInfo $careInfo, array $payload): array
    {
        $careerWithClassNumber = $originalUser->activeCareers()->whereNotNull('class_number')->first();
        $classNumber = $careerWithClassNumber?->class_number;
        $currentPupilStatus = $careInfo?->pupil_status;

        $now = Carbon::now();

        return [
            'user_id' => $originalUser->id,
            'old_firstname' => $originalUser->firstname,
            'old_lastname' => $originalUser->lastname,
            'old_class_number' => $classNumber,
            'previous_pupil_status' => $currentPupilStatus ? $currentPupilStatus->value : PupilStatus::New->value,
            'new_firstname' => $payload['firstname'],
            'new_lastname' => $payload['lastname'],
            'smd_incremental_event_id' => $this->incrementalId,
            'created_at' => $now,
            'handled_at' => $now,
            'handled_status' => $careInfo?->wisa_uid !== null
                ? PupilChangeHandledStatus::HandledAutomaticallyForWisa->value
                : PupilChangeHandledStatus::HandledAutomatically->value,
        ];
    }

    private function clearNationalRegisterNumberForNameChange(User $originalUser, ?CareInfo $careInfo): void
    {
        if ($careInfo === null || $careInfo->wisa_uid !== null) {
            return;
        }

        // We check if Pupil has relevant data linked to him and only then block user input on the pupil.
        if (app(PupilDataExistsService::class)->dataExistForPupilID($originalUser->id)) {
            $careInfo->forceFill(['pupil_status' => PupilStatus::SuspiciousNameChangeException])->save();

            return;
        }

        $careInfo->clearNationalRegisterNumber()->save();
    }

    private function runEventsForCareers(): void
    {
        event(new CareersSaved($this->careerPayloads->pluck('uid')->toArray()));
    }
}
