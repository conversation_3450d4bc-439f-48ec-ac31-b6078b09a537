<?php

namespace Cfa\Common\Application\Jobs\Smd;

use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\SchoolUserAccess\SchoolUserAccess;
use Cfa\Common\Domain\User\User;
use DomainException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Override;

class MergeEventJob extends SmdJob
{
    private ?Collection $personsFrom;
    private ?User $personTo;

    #[Override]
    public function findModel(array $transformedPayload): void
    {
        $this->personsFrom = User::withoutGlobalScopes()
            ->with(['careers', 'schoolUserAccess'])
            ->whereIn('uid', $transformedPayload['from_uids'])
            ->get();

        if ($this->personsFrom->count() !== count($transformedPayload['from_uids'])) {
            throw new ModelNotFoundException('Not all user models were found');
        }

        $this->personTo = User::withoutGlobalScopes()
            ->with('schoolUserAccess')
            ->where('uid', $transformedPayload['to_uid'])
            ->firstOrFail();
    }

    #[Override]
    public function transformPayload(): array
    {
        $payload = $this->getPayload();

        return [
            'uid' => $payload['to']['uid'],
            'from_uids' => Arr::pluck($payload['from'], 'uid'),
            'to_uid' => $payload['to']['uid'],
        ];
    }

    #[Override]
    protected function performOperation(array $transformedPayload): void
    {
        if ($this->personsFrom->contains(fn(User $user): bool => $user->trashed() === false)) {
            throw new DomainException('The person to be merged must be soft deleted.');
        }

        $this->personsFrom->flatMap->careers->each(function (Career $career): void {
            $career->user_id = $this->personTo->id;
            $career->save();
        });

        $existingSchoolUserAccesses = $this->personTo->schoolUserAccess->keyBy('school_id');
        $this->personsFrom->flatMap->schoolUserAccess->each(
            function (SchoolUserAccess $schoolUserAccess) use ($existingSchoolUserAccesses): void {
                if ($existingSchoolUserAccesses->has($schoolUserAccess->school_id)) {
                    $schoolUserAccess->delete();

                    return;
                }

                $schoolUserAccess->user_id = $this->personTo->id;
                $schoolUserAccess->save();
            },
        );
    }
}
