<?php

namespace Cfa\Common\Domain\Schoolyear;

use App\Repositories\RepositoryInterface;
use Carbon\Carbon;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Illuminate\Support\Collection;

interface SchoolyearRepositoryInterface extends RepositoryInterface
{
    /**
     * Get the current schoolyear.
     */
    public function getCurrent(): Schoolyear;

    /**
     * Get the next schoolyear.
     */
    public function getNext(): Schoolyear;

    /**
     * Get the previous schoolyear.
     */
    public function getPrevious(): Schoolyear;

    /**
     * Get the schoolyears between the two given dates.
     *
     * @param Carbon $fromDate Date in the first schoolyear.
     * @param Carbon $toDate Date in the second schoolyear.
     */
    public function getBetween(Carbon $fromDate, Carbon $toDate): Collection;

    /**
     * Lookup Schoolyear by year.
     *
     * @param int $startYear The year of the start date.
     */
    public function findSchoolyearByYear(int $startYear): ?Schoolyear;

    /**
     * Lookup Schoolyear by date.
     *
     * @param Carbon $date Search the schoolyear by this date.
     */
    public function findSchoolyearByDate(Carbon $date): ?Schoolyear;

    /**
     * Get the schoolyears the given user has access to.
     *
     * @param User $user The user.
     * @param School $school The school.
     */
    public function getSchoolyearsForUserAndSchool(User $user, School $school): Collection;

    /**
     * Flush all cached schoolyears.
     */
    public function flushAllSchoolyears(): void;

    /**
     * Flush the cached schoolyears for the given user and school.
     */
    public function flushForUserAndSchool(int $userId, int $schoolId): void;
}
