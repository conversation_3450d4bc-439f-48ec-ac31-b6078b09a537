<?php

namespace Cfa\Common\Domain\Permission;

enum LicensePortalLicenceType: string
{
    case CanAccessBingel = 'BINGEL_BASIC';
    case HasAccessToMethodsBasic = 'MY_METHODS_BASIC';
    case HasAccessToMethodsPaying = 'MY_METHODS_PAYING';
    case CanAccessBoardBookStreaming = 'BOARD_BOOK_STREAMING';
    case CanAccessBoardBookManual = 'BOARD_BOOK_DOWNLOAD';
    case CanAccessBoardBookDownload = 'BOARD_BOOK_MANUAL';
    case HasAccessToDc = 'DC';
    case HasAccessToReadingBox = 'READING_BOX';
    case HasAccessToPlannerBasic = 'PLANNER_BASIC';
    case HasAccessToCare = 'CARE';
    case HasAccessToEvaluation = 'EVALUATION';

    /**
     * @return self[]
     */
    public static function getPermissionSchoolLicenseTypes(): array
    {
        return [self::HasAccessToPlannerBasic, self::HasAccessToCare, self::HasAccessToEvaluation];
    }

    /**
     * @return self[]
     */
    public static function getCollectionLicenseLicenseTypes(): array
    {
        return [
            self::CanAccessBingel, self::HasAccessToMethodsBasic, self::HasAccessToMethodsPaying,
            self::CanAccessBoardBookStreaming, self::CanAccessBoardBookManual, self::CanAccessBoardBookDownload,
            self::HasAccessToReadingBox, self::HasAccessToDc,
        ];
    }
}
