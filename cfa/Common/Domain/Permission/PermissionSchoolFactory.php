<?php

namespace Cfa\Common\Domain\Permission;

use App\Factories\Factory;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Override;

/**
 * PermissionSchoolFactory
 *
 * @codingStandardsIgnoreStart
 * @method Collection|PermissionSchool[]|PermissionSchool create($attributes = [], Model|null $parent = null)
 * @method Collection|PermissionSchool[]|PermissionSchool createWithEvents(array $attributes = [], Model|null $parent = null)
 * @codingStandardsIgnoreEnd
 */
class PermissionSchoolFactory extends Factory
{
    /** @var string */
    protected $model = PermissionSchool::class;

    public function inSchool(School $school): self
    {
        return $this->state(fn(): array => [
            'school_id' => $school->id,
        ]);
    }

    public function setPermission(PermissionName $permissionName): self
    {
        return $this->state(fn(): array => [
            'name' => $permissionName,
        ]);
    }

    public function forTargetAudienceType(TargetAudienceType $targetAudienceType): self
    {
        return $this->state(fn(): array => [
            'target_audience_type' => $targetAudienceType,
        ]);
    }

    public function expired(): self
    {
        $previousSchoolyear = app(SchoolyearRepositoryInterface::class)->getPrevious();

        return $this->state(fn(): array => [
            'startdate' => $previousSchoolyear->start,
            'enddate' => $previousSchoolyear->end,
        ]);
    }

    #[Override]
    public function definition(): array
    {
        $schoolyearRepository = app(SchoolyearRepositoryInterface::class);

        return [
            'name' => $this->faker->randomElement(PermissionName::cases()),
            'target_audience_type' => $this->faker->randomElement(TargetAudienceType::cases()),
            'school_id' => School::randomId(),
            'startdate' => $schoolyearRepository->getCurrent()->start,
            'enddate' => $schoolyearRepository->getNext()->start,
        ];
    }
}
