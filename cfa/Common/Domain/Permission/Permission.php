<?php

namespace Cfa\Common\Domain\Permission;

use Carbon\Carbon;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\SchoolUserAccess\SchoolUserAccess;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use JsonSerializable;
use Override;

/**
 * App\Models\Permission
 *
 * Helper model for permission serialization.
 */
class Permission implements JsonSerializable
{
    private ?PermissionName $permissionName;

    /**
     * The school to get permissions for.
     *
     * @var School
     */
    private $school;

    /**
     * Permission constructor.
     *
     * @param PermissionName $permissionName The Permission enum.
     * @param School $school The school to get permissions for.
     */
    public function __construct(PermissionName $permissionName, School $school)
    {
        $this->permissionName = $permissionName;
        $this->school = $school;
    }

    /**
     * Return the translated name of the permission enum.
     */
    public function getName(): string
    {
        return $this->permissionName->getHumanReadableName();
    }

    /**
     * Get the users that are linked to the permission within the school.
     */
    public function getSchoolUserAccess(): Collection
    {
        return SchoolUserAccess::select('school_user_access.*')
            ->distinct()
            ->with('user')
            ->join(
                'permission_school_user_access',
                'school_user_access.id',
                '=',
                'permission_school_user_access.school_user_access_id',
            )
            ->join(
                'careers',
                'careers.user_id',
                '=',
                'school_user_access.user_id',
            )
            ->whereNull('careers.deleted_at')
            ->where('school_user_access.school_id', $this->school->id)
            ->where('careers.school_id', $this->school->id)
            ->where('permission_school_user_access.name', $this->permissionName)
            ->where(function (Builder $query): void {
                $query->where('enddate', '>', Carbon::now())
                    ->orWhereNull('enddate');
            })
            ->whereNull('permission_school_user_access.deleted_at')
            ->get();
    }

    /**
     * Return the fullname of the linked school user access as a comma seperated list of names.
     */
    public function getFullnames(): string
    {
        $schoolUserAccess = $this->getSchoolUserAccess();

        return $schoolUserAccess->sortBy('user.fullname')->pluck('user.fullname')->implode(', ');
    }

    /**
     * Return the url to add users to permission.
     */
    protected function getAddUrl(): ?string
    {
        if (!request()->route()) {
            return null;
        }

        return route('web.settings.permissions.add', ['type' => $this->permissionName->name]);
    }

    /**
     * Serialize the data to an array.
     */
    public function toArray(): array
    {
        return [
            'name' => $this->getName(),
            'fullnames' => $this->getFullnames(),
            'addUrl' => $this->getAddUrl(),
        ];
    }

    /**
     * Return the data that can be json serialized.
     */
    #[Override]
    public function jsonSerialize(): array
    {
        return $this->toArray();
    }
}
