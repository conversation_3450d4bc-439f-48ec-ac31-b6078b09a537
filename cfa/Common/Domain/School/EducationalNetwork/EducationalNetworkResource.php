<?php

namespace Cfa\Common\Domain\School\EducationalNetwork;

use Cfa\Planner\Domain\CurriculumNode\CurriculumType;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Override;

use function tenant;

/**
 * @mixin EducationalNetwork
 * @extends JsonResource<EducationalNetwork>
 */
class EducationalNetworkResource extends JsonResource
{
    /**
     * {@inheritdoc}
     * @param Request $request The request.
     */
    #[Override]
    public function toArray($request): array
    {
        return [
            'id' => $this->uid,
            'name' => $this->name,
            'schoolIds' => $this->additional['schoolsByEducationalNetwork'][$this->id]->pluck('uid'),
            'learningTrail' => $this->getLearningTrail(),
            'curriculumTypes' => $this->getCurriculumTypes()
                ->map(fn(CurriculumType $curriculumType): string => Str::upper($curriculumType->name))
                ->sort()
                ->all(),
        ];
    }

    /**
     * Get the learning trail for a Tenant and Educationalnetwork.
     */
    private function getLearningTrail(): Collection
    {
        $tenantUid = tenant()->uid;
        $key = "tenants.$tenantUid.educationalNetworkConfig.$this->uid.learningTrailConfigs";

        return collect(config($key) ?? [])->map(function (array $learningTrail) {
            return [
                'naturalStudyYear' => $learningTrail['naturalStudyYear'],
                'targetAudienceType' => $learningTrail['targetAudienceType'],
                'name' => $learningTrail['name'],
            ];
        });
    }
}
