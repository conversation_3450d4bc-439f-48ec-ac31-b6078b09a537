<?php

namespace Cfa\Common\Domain\School\Settings;

use App\Casts\Json;
use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\School\SchoolMfaStatus;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Cfa\Common\Domain\School\Settings\SchoolSettings
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property int $school_id
 * @property SchoolSettingsType $type
 * @property mixed|null $value
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read array $validation_rules
 * @property-read School $school
 * @method static SchoolSettingsFactory factory($count = null, $state = [])
 * @method static Builder|SchoolSettings newModelQuery()
 * @method static Builder|SchoolSettings newQuery()
 * @method static Builder|SchoolSettings query()
 * @method static Carbon|null randomCreatedAt()
 * @method static int randomId()
 * @method static int randomSchoolId()
 * @method static SchoolSettingsType randomType()
 * @method static Carbon|null randomUpdatedAt()
 * @method static mixed|null randomValue()
 * @method static Builder|SchoolSettings whereCreatedAt($value)
 * @method static Builder|SchoolSettings whereId($value)
 * @method static Builder|SchoolSettings whereSchoolId($value)
 * @method static Builder|SchoolSettings whereType($value)
 * @method static Builder|SchoolSettings whereUpdatedAt($value)
 * @method static Builder|SchoolSettings whereValue($value)
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class SchoolSettings extends Model
{
    /** @var string */
    protected $table = 'school_settings';

    /** @var array */
    protected $casts = [
        'school_id' => 'integer',
        'type' => SchoolSettingsType::class,
    ];

    /** @var array */
    protected $dispatchesEvents = [
        'saved' => SchoolSettingsSaved::class,
    ];

    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    public function getValueAttribute(): mixed
    {
        if (array_key_exists('value', $this->attributes) === false) {
            return null;
        }

        $value = new Json()->get($this, 'value', $this->attributes['value'], $this->attributes);

        if ($this->type === SchoolSettingsType::MfaStatus) {
            return SchoolMfaStatus::from($value);
        }

        return $value;
    }

    public function setValueAttribute(mixed $value): void
    {
        if ($this->type === SchoolSettingsType::MfaStatus) {
            $value = $value->value;
        }

        $this->attributes['value'] = new Json()->set($this, 'value', $value, $this->attributes);
    }
}
