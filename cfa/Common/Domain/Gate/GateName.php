<?php

namespace Cfa\Common\Domain\Gate;

use App\Traits\Enum\EnumFromName;

enum GateName: string
{
    use EnumFromName;

    case CanAccessCareOrEvaluation = 'canAccessCareOrEvaluation';
    case IsClassTeacher = 'isClassTeacher';
    case MfaEnabled = 'mfaEnabled';
    case CanAccessPlannerAndSettings = 'canAccessPlannerAndSettings';
    case CanAccessCareAndSettings = 'canAccessCareAndSettings';
    case CanAccessEvaluationAndSettings = 'canAccessEvaluationAndSettings';
    case CanAccessSettingsOrHistory = 'canAccessSettingsOrHistory';
    case CanAccessHistory = 'canAccessHistory';
    case CanAccessBingelDC = 'canAccessBingelDC';
}
