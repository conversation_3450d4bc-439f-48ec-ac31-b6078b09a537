<?php

namespace Cfa\Common\Domain\User\Career\Role;

use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Application\Traits\UpdatedBySMD;
use Cfa\Common\Domain\Permission\PermissionRoleName;
use Cfa\Common\Domain\User\Career\Career;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Query\Builder;

/**
 * Cfa\Common\Domain\User\Career\Role\Role
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid Unique identifier for the role
 * @property string $name
 * @property int|null $role_name_enum Name of the role.
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $smd_updated_at
 * @property Carbon|null $deleted_at
 * @property int|null $smd_incremental_event_id
 * @property RoleName $role_name
 * @property-read Collection<int, Career> $careers
 * @property-read int|null $careers_count
 * @property-read array $validation_rules
 * @property-read Collection<int, PermissionRoleName> $permissions
 * @property-read int|null $permissions_count
 * @method static RoleRepositoryInterface getRepository()
 * @method static Builder|Role newModelQuery()
 * @method static Builder|Role newQuery()
 * @method static Builder|Role onlyTrashed()
 * @method static Builder|Role query()
 * @method static Carbon|null randomCreatedAt()
 * @method static Carbon|null randomDeletedAt()
 * @method static int randomId()
 * @method static string randomName()
 * @method static int|null randomRoleNameEnum()
 * @method static int|null randomSmdIncrementalEventId()
 * @method static Carbon|null randomSmdUpdatedAt()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static Builder|Role whereCreatedAt($value)
 * @method static Builder|Role whereDeletedAt($value)
 * @method static Builder|Role whereId($value)
 * @method static Builder|Role whereName($value)
 * @method static Builder|Role whereRoleNameEnum($value)
 * @method static Builder|Role whereSmdIncrementalEventId($value)
 * @method static Builder|Role whereSmdUpdatedAt($value)
 * @method static Builder|Role whereUid($value)
 * @method static Builder|Role whereUpdatedAt($value)
 * @method static Builder|Role withTrashed()
 * @method static Builder|Role withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class Role extends Model
{
    use Uid;
    use SoftDeletes;
    use UpdatedBySMD;

    /**
     * Return the careers linked to role.
     */
    public function careers(): HasMany
    {
        return $this->hasMany(Career::class);
    }

    /**
     * Get the permissions for this Role.
     */
    public function permissions(): HasMany
    {
        return $this->hasMany(PermissionRoleName::class, 'role_name_enum', 'role_name_enum');
    }

    /**
     * Will give you RoleName object.
     * This is a hack not to use enums array to convert it because the field 'role_name' is used as key.
     */
    public function getRoleNameAttribute(): RoleName
    {
        return RoleName::from($this->role_name_enum);
    }

    /**
     * Will set the Role.
     *
     * @param RoleName $roleName The roleName we want to set.
     */
    public function setRoleNameAttribute(RoleName $roleName): void
    {
        $this->attributes['role_name_enum'] = $roleName->value;
    }

    /**
     * Find the correct role due to the given role type from SMD.
     *
     * @param string $roleType The given role type.
     *
     * @throws ModelNotFoundException When no role is found.
     */
    public static function findBySMDType(string $roleType): self
    {
        $roleNameEnum = null;

        switch ($roleType) {
            case 'TEACHER':
                $roleNameEnum = RoleName::Teacher;
                break;
            case 'TEACHER_ALL_CLASSES':
                $roleNameEnum = RoleName::TeacherForAllClasses;
                break;
            case 'SCHOOLADMIN':
                $roleNameEnum = RoleName::SchoolIctAdministrator;
                break;
            case 'SCHOOL_BOARD':
                $roleNameEnum = RoleName::Management;
                break;
            case 'PUPIL':
                $roleNameEnum = RoleName::Pupil;
                break;
        }

        if (is_null($roleNameEnum)) {
            throw new ModelNotFoundException($roleType . ' is no valid role');
        }

        return self::whereRoleNameEnum($roleNameEnum)->first();
    }
}
