<?php

namespace Cfa\Common\Domain\User;

use Cfa\Common\Domain\Permission\PermissionResource;
use Cfa\Common\Domain\Permission\PermissionRoleName;
use Cfa\Common\Domain\Permission\PermissionSchoolUserAccess;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\SchoolUserAccess\SchoolUserAccess;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;
use Override;

use function tenant;

/**
 * @mixin User
 * @extends JsonResource<User>
 */
class UserProfileResource extends JsonResource
{
    /**
     * {@inheritdoc}
     *
     * @param Request $request The request.
     */
    #[Override]
    public function toArray($request): array
    {
        $responseData = [
            'id' => $this->uid,
            'initials' => $this->initials,
            'firstname' => $this->firstname,
            'lastname' => $this->lastname,
            'fullname' => $this->fullname,
        ];

        $responseData['tenant'] = tenant()->uid;

        $schoolUserPermissions = $this->schoolUserAccess->flatMap(
            function (SchoolUserAccess $schoolUserAccess) {
                return $schoolUserAccess->permissionSchoolUserAccess->map(
                    function (PermissionSchoolUserAccess $permission) use ($schoolUserAccess) {
                        return collect([
                            'school' => $schoolUserAccess->school,
                            'permission' => $permission->name,
                        ]);
                    },
                );
            },
        );

        $rolePermissions = $this->activeCareers()
            ->flatMap(function (Career $career) {
                return $career->role ?
                    $career->role->permissions->map(
                        function (PermissionRoleName $permissionRoleName) use ($career) {
                            return collect([
                                'school' => $career->school,
                                'permission' => $permissionRoleName->name,
                            ]);
                        },
                    ) : null;
            })->filter(function ($schoolPermission) {
                return $schoolPermission;
            });

        // Merge the school/user permissions and role permissions.
        $allPermissions = $schoolUserPermissions
            ->concat($rolePermissions)
            ->unique()
            ->filter(fn(Collection $collection): bool => $collection->get('school') !== null);

        $responseData['permissions'] = PermissionResource::collection($allPermissions);

        return $responseData;
    }
}
