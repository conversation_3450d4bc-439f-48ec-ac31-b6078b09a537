<?php

namespace Cfa\Common\Domain\User\Badge;

use Faker\Generator;

$methods = [
    'POST',
    'GET',
    'PUT',
    'PATCH',
    'DELETE',
];

$factory->define(Badge::class, function (Generator $faker) use ($methods) {
    return [
        'name' => $faker->sentence,
        'description' => $faker->optional(0.8)->sentence,
        'route' => $faker->word,
        'method' => collect($methods)->random(),
    ];
});
