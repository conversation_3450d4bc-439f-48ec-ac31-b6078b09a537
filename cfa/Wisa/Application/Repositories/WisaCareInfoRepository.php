<?php

namespace Cfa\Wisa\Application\Repositories;

use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\CareInfo\PupilStatus;
use Cfa\Wisa\Domain\CareData\WisaCareInfoRepositoryInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Override;

class WisaCareInfoRepository implements WisaCareInfoRepositoryInterface
{
    /** @return CareInfo[]|Collection */
    #[Override]
    public function getCareInfosForNationalRegisterNumberHashOrWisaUid(
        int $schoolId,
        string $wisaUid,
        ?string $nationalRegisterNumberHash,
    ): Collection {
        return CareInfo::with(['pupilWithTrashed', 'pupil'])
            ->where('school_id', $schoolId)
            ->where(function (Builder $builder) use ($wisaUid, $nationalRegisterNumberHash): void {
                $builder->where('care_infos.wisa_uid', $wisaUid)
                    ->when(
                        !empty($nationalRegisterNumberHash),
                        fn(Builder $builder): Builder => $builder->orWhere(
                            'wisa_national_register_number',
                            $nationalRegisterNumberHash,
                        ),
                    );
            })
            ->where('pupil_status', '!=', PupilStatus::SuspiciousNameChangeException)
            ->get();
    }
}
