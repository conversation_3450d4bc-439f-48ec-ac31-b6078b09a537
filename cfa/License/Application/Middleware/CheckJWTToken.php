<?php

namespace Cfa\License\Application\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use <PERSON><PERSON>bucci\JWT\Configuration;
use <PERSON>cobucci\JWT\Signer\Key\InMemory;
use Lcobucci\JWT\Validation\Constraint\LooseValidAt;
use <PERSON><PERSON>bucci\JWT\Validation\Constraint\SignedWith;
use Psr\Clock\ClockInterface;

use function abort;
use function app;
use function config;

class CheckJWTToken
{
    public function handle(Request $request, Closure $next): mixed
    {
        $signingAlgorithm = app(config('licenses.jwt.algo'));
        $key = InMemory::file(config('licenses.jwt.keys.public'));

        $config = Configuration::forAsymmetricSigner($signingAlgorithm, InMemory::plainText('empty'), $key);
        $config->setValidationConstraints(
            new LooseValidAt(app(ClockInterface::class)),
            new SignedWith($config->signer(), $config->verificationKey()),
        );
        $token = $config->parser()->parse($request->bearerToken());

        if ($config->validator()->validate($token, ...$config->validationConstraints())) {
            return $next($request);
        }

        abort(Response::HTTP_FORBIDDEN);
    }
}
