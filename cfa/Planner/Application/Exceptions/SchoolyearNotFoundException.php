<?php

namespace Cfa\Planner\Application\Exceptions;

use App\Constants\ExceptionMessages;
use App\Exceptions\System\RuntimeException;
use Carbon\Carbon;

class SchoolyearNotFoundException extends RuntimeException
{
    public function __construct(Carbon $date)
    {
        parent::__construct(trans(
            ExceptionMessages::SCHOOLYEAR_NOT_FOUND,
            ['date' => $date->toDateTimeString()],
        ));
    }
}
