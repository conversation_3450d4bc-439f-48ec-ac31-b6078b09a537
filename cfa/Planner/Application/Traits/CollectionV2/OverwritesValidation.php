<?php

namespace Cfa\Planner\Application\Traits\CollectionV2;

use DomainException;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;
use ValueError;

use function array_merge;
use function is_array;

trait OverwritesValidation
{
    /** @var array */
    protected $motherClassRules;

    /**
     * Will resolve the validation rules according to the given column.
     * If the given column is not valid you will get an always failing rule.
     *
     * @return array The validation rules according the given column.
     * @throws DomainException If the given column has no rules in the Model you will get an exception.
     */
    protected function getValueRule(): array
    {
        $data = $this->validationData();
        $column = $data['column'];
        $columnEnumType = $this->getColumnEnumType();
        if ($column instanceof $columnEnumType) {
            if (!is_array($this->motherClassRules)) {
                $motherClass = $this->getMotherClass();
                $this->motherClassRules = new $motherClass()->rules();
            }
            $column = $column->value;
            if (Arr::has($this->motherClassRules, $column)) {
                return $this->motherClassRules[$column];
            }

            throw new DomainException('No rules for ' . $column . '.');
        }

        throw ValidationException::withMessages([
            'column' => trans(
                'validation.enum_value',
                [
                    'attribute' => 'column',
                    'other' => $this->getColumnEnumType(),
                ],
            ),
        ]);
    }

    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'column' => [
                    'required',
                    'enumValue:' . $this->getColumnEnumType(),
                ],
                'value' => $this->getValueRule(),
            ],
        );
    }

    /**
     * This should return the class path to the Class you are implementing overwrites on.
     */
    abstract protected function getMotherClass(): string;

    /**
     * This should return the class path to the Enum class whitelisting the columns that can have overwrites.
     */
    abstract protected function getColumnEnumType(): string;

    protected function validationData(): array
    {
        $data = parent::validationData();
        // We want the json decoded data, not the raw data.
        $data['value'] = $this->getAttribute('value');
        try {
            $data['column'] = $this->getAttribute('column');
        } catch (ValueError $exception) {
            $data['column'] = $this->getAttributes()['column'];
        }

        return $data;
    }
}
