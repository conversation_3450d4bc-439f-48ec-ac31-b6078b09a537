<?php

namespace Cfa\Planner\Application\Policies;

use Cfa\Admin\Domain\CmsUser\CmsUser;
use Cfa\Admin\Domain\CmsUser\CmsUserPermission;
use Cfa\Planner\Domain\Collection\Chapter\Chapter;
use Illuminate\Auth\Access\HandlesAuthorization;

class ChapterPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the Chapter in cms.
     *
     * @param CmsUser $user Authenticated user.
     * @param Chapter $chapter Chapter item to manage.
     */
    public function viewInCms(CmsUser $user, Chapter $chapter): bool
    {
        if (!$user->checkPermission(CmsUserPermission::CollectionsCrud)) {
            return false;
        }

        return empty($user->publisher_id) || $chapter->plannercollection->publisher_id === $user->publisher_id;
    }

    /**
     * Determine whether the user can make changes to the chapters.
     *
     * @param CmsUser $user Authenticated user.
     * @param Chapter $chapter Chapter item to manage.
     */
    public function manageInCms(CmsUser $user, Chapter $chapter): bool
    {
        return $this->viewInCms($user, $chapter) && !$chapter->plannercollection->isPublished();
    }
}
