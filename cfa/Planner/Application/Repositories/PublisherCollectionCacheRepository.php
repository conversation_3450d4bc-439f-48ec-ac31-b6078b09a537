<?php

namespace Cfa\Planner\Application\Repositories;

use App\Models\Model;
use App\Repositories\CacheRepository;
use Cfa\Planner\Domain\Collection\PublisherCollection\PublisherCollection;
use Cfa\Planner\Domain\Collection\PublisherCollection\PublisherCollectionRepositoryInterface;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Override;

class PublisherCollectionCacheRepository extends CacheRepository implements PublisherCollectionRepositoryInterface
{
    private const KEY_PUBLISHER_COLLECTIONS = 'publishercollections';

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function getAll(): Collection
    {
        return Cache::tags($this->getCacheTags())
            ->rememberForever(self::KEY_PUBLISHER_COLLECTIONS, function () {
                return $this->repository->getAll();
            });
    }

    /**
     * Delete the publishercollection with linked chapters and linked records.
     *
     * @param PublisherCollection $publishercollection The publishercollection to delete.
     */
    #[Override]
    public function deleteWithChapters(PublisherCollection $publishercollection): mixed
    {
        $result = $this->repository->deleteWithChapters($publishercollection);

        $this->flush();

        return $result;
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function flushPublisherCollections(): void
    {
        $this->flush();
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function flush(?Model $model = null): void
    {
        Cache::tags($this->getCacheTags())->forget(self::KEY_PUBLISHER_COLLECTIONS);
    }
}
