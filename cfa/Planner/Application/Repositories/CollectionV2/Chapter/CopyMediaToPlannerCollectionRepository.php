<?php

namespace Cfa\Planner\Application\Repositories\CollectionV2\Chapter;

use App\Services\MediaLibrary\MediaCollections;
use Cfa\Planner\Application\Exceptions\UnsupportedMediaCollectionTypeException;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\CopyMediaToPlannerCollectionRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Illuminate\Support\Collection;
use Override;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class CopyMediaToPlannerCollectionRepository implements CopyMediaToPlannerCollectionRepositoryInterface
{
    public function __construct(private readonly PlannerCollectionRepositoryInterface $plannerCollectionRepository) {}

    #[Override]
    public function copyMediaToPlannerCollection(
        PlannerCollectionInterface $sourceCollection,
        PlannerCollectionInterface $targetCollection,
    ): Collection {
        return $sourceCollection
            ->media
            ->map(fn(Media $media): Media => $this->setToTargetCollection($media, $targetCollection));
    }

    private function setToTargetCollection(Media $media, PlannerCollectionInterface $targetCollection): Media
    {
        if ($media->collection_name !== MediaCollections::COVER) {
            throw new UnsupportedMediaCollectionTypeException('Only covers can be copied');
        }

        return $this->plannerCollectionRepository->saveCover($targetCollection, $media->getPathRelativeToRoot());
    }
}
