<?php

namespace Cfa\Planner\Application\Repositories;

use App\Models\Model;
use App\Repositories\Repository;
use Cfa\Planner\Domain\Collection\PublisherCollection\Publisher\Publisher;
use Cfa\Planner\Domain\Collection\PublisherCollection\PublisherCollection;
use Cfa\Planner\Domain\Collection\PublisherCollection\PublisherCollectionRepositoryInterface;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Override;

class PublisherCollectionRepository extends Repository implements PublisherCollectionRepositoryInterface
{
    #[Override]
    public function getModel(): Model
    {
        return new PublisherCollection();
    }

    #[Override]
    public function getAll(): Collection
    {
        return $this->model
            ->with(['publisher:id,uid,name', 'targetAudiences', 'media'])
            ->select('plannercollections.*')
            // Join on the publishers so the fields can be used to order on.
            ->join('publishers', 'publishers.id', '=', 'plannercollections.publisher_id')
            ->isPublisherCollection()
            ->whereNull('plannercollections.archived_at')
            // Publisher VAN IN must be at the start of the list, so order descending on the uid of VAN IN.
            // @see https://stackoverflow.com/a/5418071.
            ->orderByDesc(DB::raw('publishers.uid = \'' . Publisher::VAN_IN . '\''))
            // All the other publishers are ordered on the name.
            ->orderBy('publishers.name')
            // Lastly, it orders on the name of the collection.
            ->orderBy('plannercollections.name')
            ->get();
    }

    #[Override]
    public function deleteWithChapters(PublisherCollection $publishercollection): mixed
    {
        if ($result = $publishercollection->delete()) {
            $publishercollection->chapters->each->deleteWithRecords();
        }

        return $result;
    }

    #[Override]
    public function flushPublisherCollections(): void
    {
        // Does nothing.
    }
}
