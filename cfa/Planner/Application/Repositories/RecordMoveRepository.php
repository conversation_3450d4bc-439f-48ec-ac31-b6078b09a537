<?php

namespace Cfa\Planner\Application\Repositories;

use Cfa\Planner\Domain\Collection\Chapter\Chapter;
use Cfa\Planner\Domain\Record\Record;
use Cfa\Planner\Domain\Record\RecordMoveRepositoryInterface;
use Override;

class RecordMoveRepository implements RecordMoveRepositoryInterface
{
    #[Override]
    public function move(Record $record, Chapter $targetChapter): Record
    {
        $nextOrder = Record::where('chapter_id', $targetChapter->id)->max('order') + 1;
        $record->order = $nextOrder;
        $record->parent_record_id = null;
        $record->chapter_id = $targetChapter->id;
        $record->save();
        $record->load('allMaterials');

        return $record;
    }
}
