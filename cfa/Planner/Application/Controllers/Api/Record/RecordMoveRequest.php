<?php

namespace Cfa\Planner\Application\Controllers\Api\Record;

use App\Http\Requests\FormRequest;
use Cfa\Planner\Domain\Collection\Chapter\Chapter;
use Cfa\Planner\Domain\Collection\PlannerCollection;
use Cfa\Planner\Domain\Collection\UserCollection\UserCollection;
use DomainException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\Rule;
use Override;

class RecordMoveRequest extends FormRequest
{
    private const TARGET_COLLECTION_ID = 'target_collection_id';
    private const TARGET_CHAPTER_ID = 'target_chapter_id';

    protected UserCollection $targetCollection;

    protected Chapter $targetChapter;

    /**
     * @throws DomainException
     * @throws ModelNotFoundException
     */
    #[Override]
    public function authorize(): bool
    {
        if (parent::authorize() === false) {
            return false;
        }
        // Will throw exception if collection is not found.
        if ($targetCollection = $this->getTargetCollection()) {
            if ($targetCollection->owner_id !== $this->user()->id) {
                return false;
            }

            // Will throw exception if the chapter is not in the collection.
            $this->getTargetChapter();
        }

        return true;
    }

    public function getTargetCollection(): PlannerCollection
    {
        if (isset($this->targetCollection)) {
            return $this->targetCollection;
        }

        $this->targetCollection = UserCollection::where('uid', $this->get(self::TARGET_COLLECTION_ID))
            ->where('owner_id', $this->user()->id)
            ->firstOrFail();

        return $this->targetCollection;
    }

    public function getTargetChapter(): Chapter
    {
        if (isset($this->targetChapter)) {
            return $this->targetChapter;
        }

        $this->targetChapter = Chapter::where('uid', $this->get(self::TARGET_CHAPTER_ID))
            ->where('plannercollection_id', $this->getTargetCollection()->id)
            ->firstOrFail();

        return $this->targetChapter;
    }

    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                self::TARGET_COLLECTION_ID => [
                    'required',
                    'uuid',
                    Rule::exists('plannercollections', 'uid')
                        ->whereNull('deleted_at'),
                ],
                self::TARGET_CHAPTER_ID => [
                    'required',
                    'uuid',
                    Rule::exists('chapters', 'uid')
                        ->whereNull('deleted_at'),
                ],
            ],
        );
    }
}
