<?php

namespace Cfa\Planner\Application\Controllers\Api\PlannerCollectionAttachment;

use App\Controllers\Controller;
use App\Models\File\FileUsage;
use App\Services\File\FileUploadService;
use Cfa\Planner\Domain\Collection\Attachment\PlannerCollectionAttachment;
use Cfa\Planner\Domain\Collection\UserCollection\UserCollection;
use Illuminate\Http\JsonResponse;

class PlannerCollectionAttachmentController extends Controller
{
    public function store(
        FileUploadService $fileUploadService,
        UserCollection $collection,
        PlannerCollectionAttachmentRequest $request,
    ): JsonResponse {
        $collectionAttachment = new PlannerCollectionAttachment();
        $filePath = $fileUploadService->uploadFile($request->user(), FileUsage::CollectionAttachment, $request->file);
        $collectionAttachment->file = $filePath;
        $collectionAttachment->owner_id = $request->user()->id;
        $collection->attachments()->save($collectionAttachment);

        return $this->sendStoreResponse($collectionAttachment);
    }

    public function delete(UserCollection $collection, PlannerCollectionAttachment $attachment): JsonResponse
    {
        $attachment->delete();

        return $this->respondNoContent();
    }
}
