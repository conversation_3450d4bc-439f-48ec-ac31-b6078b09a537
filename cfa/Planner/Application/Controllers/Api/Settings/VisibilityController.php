<?php

namespace Cfa\Planner\Application\Controllers\Api\Settings;

use App\Controllers\Controller;
use Cfa\Common\Domain\School\School;
use Cfa\Planner\Application\Services\Settings\VisibilityService;
use Cfa\Planner\Domain\Settings\VisibilityResource;
use Illuminate\Http\JsonResponse;

class VisibilityController extends Controller
{
    /**
     * Gets the active visibility settings for the given school.
     *
     * @param School $school School object specified as a uid in the url.
     */
    public function index(School $school): JsonResponse
    {
        $settings = app(VisibilityService::class)->getActiveSettings($school);

        return $this->respond(new VisibilityResource($settings));
    }
}
