<?php

namespace Cfa\Planner\Application\Controllers\Api\CalendarItemRow;

use App\Http\Requests\FormRequest;
use Cfa\Planner\Domain\CalendarItem\Row\CalendarItemRowPurpose;
use Override;

use function array_merge;

class CalendarItemRowSaveRequest extends FormRequest
{
    public const RECORD_ID = 'recordId';
    public const COLLECTION_ID = 'collectionId';
    public const TITLE = 'title';
    public const PURPOSE = 'purpose';
    public const ONLY_SELF = 'onlySelf';
    public const ONLY_SAVE_CALENDAR_ITEM_ROW = 'onlySaveCalendarItemRow';

    #[Override]
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            self::RECORD_ID => [
                'string',
                'nullable',
                'required_with:' . self::COLLECTION_ID,
            ],
            self::COLLECTION_ID => [
                'string',
                'nullable',
                'required_with:' . self::RECORD_ID,
            ],
            self::TITLE => [
                'required',
                'string',
                'max:500',
            ],
            self::PURPOSE => [
                'required',
                'enum:' . CalendarItemRowPurpose::class,
            ],
            self::ONLY_SELF => [
                'required',
                'boolean',
            ],
            self::ONLY_SAVE_CALENDAR_ITEM_ROW => [
                'required',
                'boolean',
            ],
        ]);
    }

    public function onlySaveInCalendarItemRow(): bool
    {
        return $this->get(self::ONLY_SAVE_CALENDAR_ITEM_ROW);
    }
}
