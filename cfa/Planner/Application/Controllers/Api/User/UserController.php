<?php

namespace Cfa\Planner\Application\Controllers\Api\User;

use App\Controllers\Controller;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Cfa\Common\Domain\User\UserProfileResource;
use Cfa\Common\Domain\User\UserResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class UserController extends Controller
{
    public function staff(School $school, UserSearchRequest $request): JsonResponse
    {
        $groupId = $request->filled('groupId') ?
            optional(Group::where('uid', $request->get('groupId'))->first(['id']))->id : null;
        $users = User::getRepository()->getStaff($school->id, $groupId);

        return $this->respond(UserResource::collection($users));
    }

    /**
     * Return the user-profile info.
     * Uses the logged in user info
     */
    public function userProfile(): JsonResponse
    {
        return $this->respond(new UserProfileResource(Auth::user()));
    }
}
