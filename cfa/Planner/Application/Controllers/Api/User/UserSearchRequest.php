<?php

namespace Cfa\Planner\Application\Controllers\Api\User;

use App\Http\Requests\FormRequest;
use Illuminate\Validation\Rule;
use Override;

class UserSearchRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'groupId' => [
                'string',
                Rule::exists('groups', 'uid')
                    ->whereNull('deleted_at'),
            ],
        ]);
    }

    /**
     * Get data to be validated from the request.
     * We add the schoolUids where the user is linked.
     */
    #[Override]
    public function validationData(): array
    {
        $data = parent::validationData();

        if (isset($data['schoolId']) && !is_array($data['schoolId'])) {
            $data['schoolId'] = [$data['schoolId']];
        }

        $data['linkedSchoolUids'] = $this->user()->schools()->pluck('uid')->toArray();

        return $data;
    }
}
