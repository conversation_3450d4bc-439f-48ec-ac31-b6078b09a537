<?php

namespace Cfa\Planner\Application\Controllers\Api\PlannerCollectionV2\Chapter\Record;

use App\Controllers\Controller;
use Cfa\Planner\Domain\CollectionV2\Chapter\Chapter;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\DuplicateRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\MinimalRecordResource;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Record;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordData;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionInterface;
use Illuminate\Http\JsonResponse;

class RecordDuplicateController extends Controller
{
    public function __invoke(
        PlannerCollectionInterface $plannerCollection,
        Chapter $chapter,
        Record $record,
        RecordDuplicateRequest $recordDuplicateRequest,
        RecordRepositoryInterface $recordRepository,
        DuplicateRepositoryInterface $recordDuplicateRepository,
    ): JsonResponse {
        $targetCollection = $plannerCollection;
        $targetChapter = $chapter;
        if ($recordDuplicateRequest->hasTargetChapterAndCollection()) {
            $targetCollection = $recordDuplicateRequest->getTargetCollection();
            $targetChapter = $recordDuplicateRequest->getTargetChapter();
        }
        $newRecord = $recordDuplicateRepository
            ->duplicateAndReorderRecords($targetCollection, $targetChapter, $record);

        return $this->respond(new MinimalRecordResource(RecordData::fromRecord($newRecord)));
    }
}
