<?php

namespace Cfa\Planner\Application\Controllers\Api\PlannerCollectionV2\Chapter;

use App\Controllers\Controller;
use Cfa\Planner\Domain\CollectionV2\Chapter\Chapter;
use Cfa\Planner\Domain\CollectionV2\Chapter\ChapterRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionInterface;
use Illuminate\Http\JsonResponse;

class ChapterDeleteController extends Controller
{
    public function __invoke(
        PlannerCollectionInterface $plannerCollection,
        Chapter $chapter,
        ChapterRepositoryInterface $chapterRepository,
    ): JsonResponse {
        $chapterRepository->delete($chapter);

        return $this->respondNoContent();
    }
}
