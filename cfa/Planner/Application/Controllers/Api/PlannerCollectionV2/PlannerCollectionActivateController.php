<?php

namespace Cfa\Planner\Application\Controllers\Api\PlannerCollectionV2;

use App\Controllers\Controller;
use Cfa\Planner\Application\Repositories\CollectionV2\ShareAccessStatusRepository;
use Cfa\Planner\Domain\CollectionV2\AccessLevel;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollectionRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\ActivateRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class PlannerCollectionActivateController extends Controller
{
    public function __invoke(
        PlannerCollectionActivateRequest $request,
        PlannerCollection $plannerCollection,
        ActivateRepositoryInterface $activateRepository,
        ActivatedPlannerCollectionRepositoryInterface $activatedPlannerCollectionRepository,
        PlannerCollectionRepositoryInterface $plannerCollectionRepository,
        ShareAccessStatusRepository $shareAccessStatusRepository,
    ): JsonResponse {
        if (!$plannerCollection->activablePublisherCollection() && !$plannerCollection->activableSharedCollection()) {
            return $this->respondNotFound();
        }
        $shareAccessStatusDTO = $request->getShareAccessStatusDTO();
        $schoolHasAccess = $shareAccessStatusRepository->schoolHasAccess($plannerCollection, school()->id);

        if ($schoolHasAccess && $shareAccessStatusDTO->accessLevel === AccessLevel::NO_ACCESS) {
            $plannerCollectionRepository->giveAccessToUser($plannerCollection, Auth::user()->id, AccessLevel::READ);
        }

        $activatedCollection = $activateRepository->activateForUser(
            Auth::user(),
            $plannerCollection,
            $shareAccessStatusDTO->accessLevel->isWritable(),
        );

        if (!$activatedCollection->is_writable && $shareAccessStatusDTO->hasActivatedTheCollection) {
            $activatedPlannerCollectionRepository->overwriteAttributes(
                $activatedCollection,
                ['name' => $request->input('name')],
            );
        }

        return $this->sendStoreResponse($activatedCollection, 'v2.api.collections.show');
    }
}
