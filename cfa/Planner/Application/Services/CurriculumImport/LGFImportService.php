<?php

namespace Cfa\Planner\Application\Services\CurriculumImport;

use App\Services\Sequence\SequenceCalculator;
use Carbon\Carbon;
use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetwork;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Evaluation\Domain\FollowUpSystem\LGFImportConstants;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNode;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNodeType;
use Cfa\Planner\Domain\CurriculumNode\CurriculumType;
use Cfa\Planner\Domain\CurriculumNode\GradeLevelConfiguration\GradeLevelClassification;
use Cfa\Planner\Domain\CurriculumNode\GradeLevelConfiguration\GradeLevelConfiguration;
use Cfa\Planner\Domain\CurriculumNode\GradeLevelConfigurationRepositoryInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

use function array_fill;
use function array_key_exists;
use function array_push;
use function array_slice;
use function collect;
use function config;
use function count;
use function explode;
use function in_array;

class LGFImportService
{
    private readonly EducationalNetwork $educationalNetwork;
    private Carbon $now;
    private SequenceCalculator $sequenceCalculator;
    private array $childrenOfParent;

    private const int MASS_INSERT_LIMIT = 500;
    private const string STRUCTURE_KEY = 'structure';
    private const string CHILDREN_KEY = 'children';
    private const string GOALS_KEY = 'goals';
    private const string GOAL_KEY = 'goal';
    private const string STATUS_KEY = 'status';
    private const string TYPE_KEY = 'type';
    private const string ATTITUDE_TYPE = 'attitude';
    private const array ACTIVE_STATES = ['published'];

    private Collection $goals;

    private string $mandatoryLevelOVSG;

    private string $observationLevelOVSG;

    private const array FIELD_MAPPING = [
        'uid' => 'id',
        'reference_code' => 'id',
        'name' => 'value',
        'code' => 'code',
        'description' => 'description',
    ];
    private const array LEVEL_TO_CURRICULUM_NODE_TYPE_MAPPING = [
        // learning area
        'b8231c32-2fe5-4db3-b42f-4a45ac5a3f48' => CurriculumNodeType::LearningArea,
        // theme
        '52b3aaf0-8311-4cb6-8f57-0f77b830dca4' => CurriculumNodeType::Domain,
        // learning line
        'f1834e2a-35fe-4e2b-8db5-498e22ea4b84' => CurriculumNodeType::Subsection,
    ];

    private const array ATTITUDES_LEARNING_AREA = [
        'id' => 'f50e96fe-3969-4701-b905-8f34f4031b5f',
        'value' => 'Attitudes',
        'code' => 'ATT',
        'description' => null,
    ];

    public function __construct(
        private readonly GradeLevelConfigurationRepositoryInterface $gradeLevelRepo,
        private readonly CurriculumType $curriculumType = CurriculumType::LeerLokaal,
        private readonly string $educationalNetworkUid = EducationalNetwork::OVSG_UID,
        private readonly int $version = 1,
    ) {
        $this->educationalNetwork = EducationalNetwork::whereUid($this->educationalNetworkUid)->firstOrFail();
        $this->mandatoryLevelOVSG = GradeLevelClassification::filterGradeLevelsByClassification(
            collect([GradeLevelClassification::Mandatory->value]),
            EducationalNetwork::OVSG_UID,
        )->first();
        $this->observationLevelOVSG = GradeLevelClassification::filterGradeLevelsByClassification(
            collect([GradeLevelClassification::Observation->value]),
            EducationalNetwork::OVSG_UID,
        )->first();
    }

    public function clean(): self
    {
        GradeLevelConfiguration::query()
            ->whereIn('curriculumnode_id', $this->getCurriculumNodesToCleanQuery()->select('id'))
            ->delete();

        $this->getCurriculumNodesToCleanQuery()->forceDelete();

        return $this;
    }

    private function getCurriculumNodesToCleanQuery(): Builder
    {
        return CurriculumNode::query()
            ->where('curriculum_type', $this->curriculumType)
            ->where('educationalnetwork_id', $this->educationalNetwork->id)
            ->where('version', $this->version);
    }

    public function parse(array $json): void
    {
        $this->now = Carbon::now();
        $this->sequenceCalculator = app(SequenceCalculator::class);
        $this->childrenOfParent = [];
        $this->goals = collect($json['goals'])->keyBy('id');

        DB::beginTransaction();
        $items = [];

        foreach ($json['body']['children'] as $child) {
            array_push($items, ...$this->parseItem($child));
        }

        array_push($items, ...$this->parseAttitudes());

        $this->insertCurriculumNodeItems($items);
        DB::commit();
        DB::beginTransaction();
        $this->insertChildRelations();
        $this->gradeLevelRepo->clearAndUpdateNonGoalConfigurations(
            $this->educationalNetwork->id,
            $this->curriculumType,
            $this->mandatoryLevelOVSG,
            $this->observationLevelOVSG,
        );
        DB::commit();
    }

    private function parseAttitudes(): array
    {
        $attitudes = [];
        $attitudes[] = $this->createCurriculumNodeData(
            self::ATTITUDES_LEARNING_AREA,
            CurriculumNodeType::LearningArea,
        );
        $parentUid = self::ATTITUDES_LEARNING_AREA['id'];
        $this->childrenOfParent[$parentUid] = [];

        foreach ($this->goals as $goal) {
            if ($goal['type'] !== self::ATTITUDE_TYPE) {
                continue;
            }

            $attitude = $this->createCurriculumNodeData($goal, CurriculumNodeType::Goal);
            $attitudes[] = $attitude;
            $this->childrenOfParent[$parentUid][] = $attitude['uid'];
        }

        return $attitudes;
    }

    private function parseItem(array $item): array
    {
        $items = [];
        $newNode = [];
        if ($this->hasNodeElement($item)) {
            $key = $this->getNodeElementKey($item);
            $nodeItem = $item[$key];
            if (($nodeItem[self::TYPE_KEY] ?? null) === self::ATTITUDE_TYPE) {
                return $items;
            }

            $curriculumNodeType =
                $key === self::GOAL_KEY ? CurriculumNodeType::Goal : $this->getCurriculumNodeType($nodeItem);
            $newNode = $this->createCurriculumNodeData($nodeItem, $curriculumNodeType);
            $items[] = $newNode;
            $this->childrenOfParent[$newNode['uid']] = [];
        }
        if ($this->hasList($item)) {
            $list = $item[$this->getListKey($item)];
            foreach ($list as $childItem) {
                $childrenToInsert = $this->parseItem($childItem);
                $this->childrenOfParent[$newNode['uid']] = [
                    ...$this->childrenOfParent[$newNode['uid']],
                    ...Arr::pluck($childrenToInsert, 'uid'),
                ];
                $items = [...$items, ...$childrenToInsert];
            }
        }

        return $items;
    }

    private function hasNodeElement(array $item): bool
    {
        return array_key_exists(self::STRUCTURE_KEY, $item) || array_key_exists(self::GOAL_KEY, $item);
    }

    private function getNodeElementKey(array $item): string
    {
        return array_key_exists(self::STRUCTURE_KEY, $item) ? self::STRUCTURE_KEY : self::GOAL_KEY;
    }

    private function hasList(array $item): bool
    {
        return array_key_exists(self::CHILDREN_KEY, $item) || array_key_exists(self::GOALS_KEY, $item);
    }

    private function getListKey(array $item): string
    {
        return array_key_exists(self::CHILDREN_KEY, $item) ? self::CHILDREN_KEY : self::GOALS_KEY;
    }

    private function insertCurriculumNodeItems(array $items): void
    {
        $itemsCount = count($items);
        for ($offset = 0; $offset < $itemsCount; $offset += self::MASS_INSERT_LIMIT) {
            CurriculumNode::upsert(array_slice($items, $offset, self::MASS_INSERT_LIMIT), 'uid');
        }

        $curriculumNodeIdsByUids = CurriculumNode::query()
            ->where('educationalnetwork_id', $this->educationalNetwork->id)
            ->where('curriculum_type', $this->curriculumType)
            ->pluck('id', 'uid');

        $gradeLevelConfigurations = collect($items)
            ->flatMap(
                function (array $goal) use ($curriculumNodeIdsByUids): Collection {
                    return collect($this->getTargetAgeMapping($this->goals[$goal['uid']]['target'] ?? null))
                        ->flatMap(
                            fn(Collection $studyYears, int $audienceType): Collection => $studyYears
                                ->map(fn(?string $level, int $studyYear): array => [
                                    'curriculumnode_id' => $curriculumNodeIdsByUids->get($goal['uid']),
                                    'level' => $level,
                                    'target_audience_type' => $audienceType,
                                    'natural_study_year' => $studyYear,
                                ]),
                        );
                },
            )
            ->all();

        $gradeLevelCount = count($gradeLevelConfigurations);
        for ($offset = 0; $offset < $gradeLevelCount; $offset += self::MASS_INSERT_LIMIT) {
            GradeLevelConfiguration::upsert(
                array_slice($gradeLevelConfigurations, $offset, self::MASS_INSERT_LIMIT),
                'curriculumnode_id',
            );
        }
    }

    private function getTargetAgeMapping(?string $level): Collection
    {
        $mapping = $this->initializeEmptyMapping();

        if (!$level) {
            return $mapping;
        }

        foreach ($this->parseTargetLevelEntries($level) as [$ageKey, $isObservation]) {
            foreach ($this->getTargetAudiencesForAgeKey($ageKey) as $targetAudience) {
                $audienceType = $targetAudience[0]->value;
                $naturalYear = $targetAudience[1];

                $mapping[$audienceType][$naturalYear] = $isObservation
                    ? $this->observationLevelOVSG
                    : $this->mandatoryLevelOVSG;
            }
        }

        return $mapping;
    }

    private function parseTargetLevelEntries(string $level): array
    {
        $entries = [];

        foreach (explode(LGFImportConstants::GRADE_SEPARATOR, $level) as $entry) {
            $entry = trim($entry);

            if (
                preg_match(
                    '/^([0-9,-]+)(' . preg_quote(LGFImportConstants::OBSERVATION_MARKER, '/') . ')?$/',
                    $entry,
                    $matches,
                )
            ) {
                $ageKey = $matches[1];
                $isObservation = isset($matches[2]);
                $entries[] = [$ageKey, $isObservation];
            }
        }

        return $entries;
    }

    private function getTargetAudiencesForAgeKey(string $ageKey): Collection
    {
        $mapping = config('tenants.sol-fl.ageMapping')[$ageKey] ?? null;

        if ($mapping === null) {
            Log::warning("Missing LGF age mapping for key '{$ageKey}'");
        }

        return collect($mapping ?? []);
    }

    private function initializeEmptyMapping(): Collection
    {
        return collect([
            TargetAudienceType::Ko->value => collect(array_fill(0, 4, null)),
            TargetAudienceType::Lo->value => collect(array_fill(1, 6, null)),
        ]);
    }

    private function createCurriculumNodeData(array $item, CurriculumNodeType $curriculumNodeType): array
    {
        $data = [
            'type' => $curriculumNodeType,
            'sequence' => $this->sequenceCalculator->getNext($curriculumNodeType),
            'is_observation' => Str::contains(
                $this->goals[$item['id']]['target'] ?? '',
                LGFImportConstants::OBSERVATION_MARKER,
            ),
            ...$this->getCurriculumNodeDefaultFields(),
        ];
        foreach (self::FIELD_MAPPING as $curriculumFieldName => $structureValue) {
            if (array_key_exists($structureValue, $item)) {
                $data[$curriculumFieldName] = $item[$structureValue];
            }
        }
        if ($curriculumNodeType === CurriculumNodeType::Goal) {
            $data['name'] = $item['description'];
            $data['code'] = $item['name'];
        }
        $data['search_code'] = CurriculumNode::makeSearchCode($item['name'] ?? $item['code']);

        if (array_key_exists(self::STATUS_KEY, $item)) {
            $data['is_active'] = in_array($item[self::STATUS_KEY], self::ACTIVE_STATES, true);
        }

        return $data;
    }

    private function getCurriculumNodeDefaultFields(): array
    {
        return [
            'educationalnetwork_id' => $this->educationalNetwork->id,
            'curriculum_type' => $this->curriculumType,
            'version' => $this->version,
            'is_active' => true,
            'name' => null,
            'code' => null,
            'search_code' => null,
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ];
    }

    private function getCurriculumNodeType(array $structure): CurriculumNodeType
    {
        return self::LEVEL_TO_CURRICULUM_NODE_TYPE_MAPPING[$structure['level']['id']];
    }

    private function insertChildRelations(): void
    {
        $curriculumNodesIdsKeydByUid = CurriculumNode::where('curriculum_type', $this->curriculumType)
            ->where('educationalnetwork_id', $this->educationalNetwork->id)
            ->where('version', $this->version)
            ->pluck('id', 'uid');
        $inserts = [];
        foreach ($this->childrenOfParent as $parentUid => $childUids) {
            $parentId = $curriculumNodesIdsKeydByUid[$parentUid];
            foreach ($childUids as $childUid) {
                $childId = $curriculumNodesIdsKeydByUid[$childUid];
                $inserts[] = ['parent_id' => $parentId, 'child_id' => $childId];
            }
        }
        $this->insertChildAndParents($inserts);
    }

    private function insertChildAndParents(array $inserts): void
    {
        $insertCount = count($inserts);
        for ($offset = 0; $offset < $insertCount; $offset += self::MASS_INSERT_LIMIT) {
            DB::table('curriculumnode_curriculumnode')->upsert(
                array_slice($inserts, $offset, self::MASS_INSERT_LIMIT),
                ['parent_id'],
            );
        }
    }
}
