<?php

namespace Cfa\Planner\Application\Services\Collection;

use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\CallWithQueryingEnabledOnModels;
use Cfa\Planner\Application\Exceptions\SyncDraftCollectionException;
use Cfa\Planner\Domain\Collection\PublisherCollection\Publisher\Publisher;
use Cfa\Planner\Domain\CollectionV2\Chapter\Chapter;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Record;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

use function collect;
use function trim;
use function uuid;

class SyncDraftCollection
{
    use CallWithQueryingEnabledOnModels;

    public function sync(PlannerCollection $draftCollection): void
    {
        if ($draftCollection->publisher->uid !== Publisher::VAN_IN_DRAFT) {
            throw new SyncDraftCollectionException(trans('nova.sync_draft.only_van_in_exception'));
        }

        DB::transaction(function () use ($draftCollection): void {
            $this->withQueryingEnabledOnModels(
                fn() =>
                $this->syncCollection($draftCollection),
                [
                    PlannerCollection::class,
                    Chapter::class,
                    Record::class,
                ],
            );
        });
    }

    private function syncCollection(PlannerCollection $draftCollection): void
    {
        $eagerLoads = [
            'media',
            'chapters' => fn(HasMany $builder) => $builder->withoutGlobalScopes(),
            'chapters.records' => fn(HasMany $builder) => $builder->withoutGlobalScopes(),
        ];
        $draftCollection->load($eagerLoads);

        $now = Carbon::now();
        $draftCollection->sent_at = $now;
        $syncedCollection = PlannerCollection::with($eagerLoads)->find($draftCollection->published_parent_id) ??
            $this->createNewPlannerCollectionForVanIn();

        /** @var PlannerCollection $publisherCollection */
        $publisherCollection = $this->syncDraftToPublisherModel(
            $draftCollection,
            $syncedCollection,
            [
                'name' => trim(Str::before($draftCollection->name, '(Draft)')),
                'publisher_id' => $syncedCollection->publisher_id,
                'published_at' => $now,
                'sent_at' => $now,
            ],
        );

        $this->syncDraftMediaToPublisher($draftCollection, $publisherCollection);

        $chaptersById = $publisherCollection->chapters->keyBy('id');
        $draftCollection
            ->chapters
            ->each(
                fn(Chapter $draftChapter) => $this->syncChapter($draftChapter, $publisherCollection, $chaptersById),
            );
    }

    private function syncChapter(
        Chapter $draftChapter,
        PlannerCollection $publisherCollection,
        Collection $publisherChaptersById,
    ): void {
        /** @var Chapter $publisherChapter */
        $publisherChapter = $this->syncDraftToPublisherModel(
            $draftChapter,
            $publisherChaptersById->get($draftChapter->published_parent_id) ?? $this->createNewChapter(),
            ['planner_collection_id' => $publisherCollection->id],
        );

        $recordsById = $publisherChapter->records->keyBy('id');
        $draftChapter
            ->records
            ->each(
                fn(Record $draftRecord) => $this->syncRecord(
                    $draftRecord,
                    $publisherCollection,
                    $publisherChapter,
                    $recordsById,
                ),
            );
    }

    private function syncRecord(
        Record $draftRecord,
        PlannerCollection $publisherCollection,
        Chapter $publisherChapter,
        Collection $publisherRecordsById,
    ): void {
        /* @var Record $publisherRecord */
        $this->syncDraftToPublisherModel(
            $draftRecord,
            $publisherRecordsById->get($draftRecord->published_parent_id) ?? $this->createNewRecord(),
            [
                'chapter_id' => $publisherChapter->id,
                'planner_collection_id' => $publisherCollection->id,
            ],
        );
    }

    private function syncDraftToPublisherModel(
        Model $draftModel,
        Model $publisherModel,
        array $attributeOverwrites,
    ): Model {
        $publisherModel->setRawAttributes([
            ...$publisherModel->getAttributes(),
            ...Arr::except($draftModel->getAttributes(), [
                'id',
                'uid',
                'published_parent_id',
                'bingel_uid',
                'bingel_dc_uid',
                'digiboardware_uid',
            ]),
            ...$attributeOverwrites,
        ]);

        if ($publisherModel->getAttribute('id') === null) {
            $publisherModel->setAttribute('uid', uuid());
        }

        $publisherModel->setTouchedRelations([]);
        $publisherModel->saveQuietly();

        $draftModel->setAttribute('published_parent_id', $publisherModel->getAttribute('id'));
        $draftModel->setTouchedRelations([]);
        $draftModel->saveQuietly();

        return $publisherModel;
    }

    private function syncDraftMediaToPublisher(
        PlannerCollection $draftCollection,
        PlannerCollection $publisherCollection,
    ): void {
        $draftMedia = $draftCollection->media->first();
        $publishedMedia = $publisherCollection->media->first() ?? new Media();

        if (!$draftMedia) {
            return;
        }

        $publishedMedia->setRawAttributes([
            ...$publishedMedia->getAttributes(),
            ...Arr::except($draftMedia->getAttributes(), ['id']),
            'model_id' => $publisherCollection->id,
        ]);
        $publishedMedia->saveQuietly();
    }

    private function createNewPlannerCollectionForVanIn(): PlannerCollection
    {
        $plannerCollection = new PlannerCollection();
        $plannerCollection->publisher_id = Publisher::where('uid', Publisher::VAN_IN)->value('id');
        $plannerCollection->setRelation('chapters', collect());
        $plannerCollection->setRelation('media', collect());

        return $plannerCollection;
    }

    private function createNewChapter(): Chapter
    {
        $chapter = new Chapter();
        $chapter->setRelation('records', collect());

        return $chapter;
    }

    private function createNewRecord(): Record
    {
        $record = new Record();
        $record->setRelation('allMaterials', collect());
        $record->setRelation('curriculumnodes', collect());

        return $record;
    }
}
