<?php

namespace Cfa\Planner\Application\Services\Collection;

use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\Collection\Attachment\PlannerCollectionAttachment;
use Cfa\Planner\Domain\Collection\PlannerCollection as LegacyCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class MigrateLegacyCollections
{
    public function __construct(
        private readonly PlannerCollectionRepositoryInterface $plannerCollectionRepository,
        private readonly MigrateLegacyChapters $migrateLegacyChapters,
        private readonly MigrateLegacyRecords $migrateLegacyRecords,
    ) {}

    public function migrateCollection(LegacyCollection $legacyCollection): PlannerCollection
    {
        $legacyCollection->load([
            'media',
            'attachments',
            'chaptersWithTemplates.records.allMaterials',
            'usersWithAccess.careers',
            'schoolsWithAccess',
        ]);
        $plannerCollection = new PlannerCollection();
        $plannerCollection->forceFill(
            Arr::except(
                $legacyCollection->getAttributes(),
                [
                    'id',
                    'owner_id',
                    'parent_collection_id',
                    'creator_id',
                    'updater_id',
                    'published_parent_id',
                    'migration_status',
                    'migrate_started_at',
                    'migrated_at',
                    'template',
                ],
            ),
        );
        $plannerCollection->user_id = $legacyCollection->owner_id;
        $plannerCollection->target_audiences = $legacyCollection
            ->targetAudiences()
            ->orderBy('type')
            ->orderBy('natural_study_year')
            ->pluck('uid');
        $legacyCollection->attachments->each(
            fn(PlannerCollectionAttachment $attachment): Collection =>
                $plannerCollection->attachments->push($attachment->file),
        );

        $this->plannerCollectionRepository->save($plannerCollection);

        $usersWithAccess = $legacyCollection
            ->usersWithAccess
            ->filter(fn(User $user): bool => $legacyCollection->owner && $user->isColleague($legacyCollection->owner));
        $plannerCollection->usersWithAccess()->sync($usersWithAccess->pluck('id'));

        $schoolsWithAccess = $legacyCollection
            ->schoolsWithAccess
            ->filter(fn(School $school): bool => $legacyCollection->owner?->isActiveInSchool($school) === true);
        $plannerCollection->schoolsWithAccess()->sync($schoolsWithAccess->pluck('id'));

        $legacyCollection->media->each(
            function (Media $media) use ($plannerCollection): void {
                $newMedia = $media->replicate();
                $newMedia->model_type = $plannerCollection->getMorphClass();
                $newMedia->model_id = $plannerCollection->id;
                $newMedia->save();
            },
        );

        $chapterIdsByLegacyIds = $this->migrateLegacyChapters->migrateChapters(
            $plannerCollection,
            $legacyCollection->chaptersWithTemplates,
        );
        $this->migrateLegacyRecords->migrateRecords(
            $plannerCollection,
            $legacyCollection->chaptersWithTemplates->flatMap->records,
            $chapterIdsByLegacyIds,
        );

        return $plannerCollection;
    }
}
