<?php

namespace Cfa\Planner\Application\Services\PublisherCollectionImport;

use Cfa\Common\Application\Traits\CallWithQueryingEnabledOnModels;
use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetwork;
use Cfa\Planner\Application\Services\PublisherCollectionImport\Parsers\AbstractParser;
use Cfa\Planner\Application\Services\Zill\ZILLService;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Record;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNode;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNodeType;
use Cfa\Planner\Domain\CurriculumNode\CurriculumType;
use Cfa\Planner\Domain\CurriculumNode\Zill\ZillVersion;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Ramsey\Uuid\Uuid;

use function app;
use function array_key_exists;
use function collect;
use function explode;
use function preg_replace;

class SaveRecordGoals
{
    use CallWithQueryingEnabledOnModels;

    private ?string $subjectString = null;

    private array $missingGoals = [];

    private AbstractParser $goalParser;

    private int $currentZillVersionId;

    /** @var Collection<EducationalNetwork> */
    private Collection $educationalNetworksByUid;

    private array $cachedNodes = [];

    public function __construct(AbstractParser $goalParser)
    {
        $this->goalParser = $goalParser;
        $this->setQueryingEnabledModels([Record::class]);
    }

    public function forLearningArea(?string $learningArea): self
    {
        if ($learningArea) {
            $this->subjectString = $this->goalParser->findSubject($learningArea);
        }

        return $this;
    }

    /**
     * Saves the goals on the given record by looking them up in the database and saves the zill status of the record.
     * Expects the goals to be ordered by educational network and curriculum type like so:
     *
     * [
     *      'GO_UID' => [
     *          'DEFAULT' => [ 'goal1', 'goal2' ],
     *      ],
     *      'VVKBAO_UID' => [
     *          'DEFAULT' => ['goal1', 'goal2' ],
     *          'ZILL' => ['goal1', 'goal2'],
     *      ],
     * ]
     */
    public function saveRecordGoals(Record $record, array $goalsByEducationalNetworkUid): void
    {
        $goals = collect();
        foreach ($goalsByEducationalNetworkUid as $educationalNetworkUid => $networkGoals) {
            foreach ($networkGoals as $curriculumTypeValue => $curriculumTypeGoals) {
                $goals = $goals->merge(
                    $this->getCurriculumnodesWithParents(
                        $this->getEducationalNetworksByUid()[$educationalNetworkUid],
                        CurriculumType::fromName($curriculumTypeValue),
                        $curriculumTypeGoals,
                    ),
                );
            }
        }
        $goals = $goals->unique();
        // Get the matching goalbook goals for the curriculum nodes.
        $goalbookGoalIds = DB::table('goalbooknode_curriculumnode')
            ->distinct()
            ->whereIn('curriculumnode_id', $goals->all())
            ->pluck('goalbooknode_id');

        $record->curriculumnodes = $goals->merge($goalbookGoalIds)->unique()->all();
        $record->zill = app(ZILLService::class)->isZill($record->curriculumnodes);
    }

    protected function getCurriculumnodesWithParents(
        EducationalNetwork $educationalNetwork,
        CurriculumType $curriculumType,
        array $goals,
    ): Collection {
        $curriculumnodeIds = $this->getCurriculumnodeIds($educationalNetwork, $curriculumType, $goals);

        return $curriculumnodeIds->merge($this->getCurriculumnodeParentIds($curriculumnodeIds))->unique();
    }

    protected function getCurriculumnodeParentIds(Collection $childIds): Collection
    {
        if ($childIds->isEmpty()) {
            return collect();
        }

        return DB::table('curriculumnode_curriculumnode')
            ->join('curriculumnodes', 'parent_id', '=', 'curriculumnodes.id')
            ->where('type', CurriculumNodeType::Goal->value)
            ->whereIn('child_id', $childIds)
            ->groupBy('curriculumnodes.id', 'parent_id')
            ->pluck('parent_id');
    }

    protected function getCurriculumnodeIds(
        EducationalNetwork $educationalNetwork,
        CurriculumType $curriculumType,
        array $goals,
    ): Collection {
        return collect($goals)
            ->map(function ($goalCodes) use ($educationalNetwork, $curriculumType) {
                return $this->findCurriculumnodeId(
                    $educationalNetwork,
                    $curriculumType,
                    Arr::wrap($goalCodes),
                );
            })
            ->filter()
            ->flatten()
            ->unique();
    }

    protected function findCurriculumnodeId(
        EducationalNetwork $educationalNetwork,
        CurriculumType $curriculumType,
        array $goalCodes,
    ): ?int {
        $cacheKey = $educationalNetwork->uid . $curriculumType->value;
        $firstGoalCode = Arr::first($goalCodes);

        if (!array_key_exists($cacheKey, $this->cachedNodes)) {
            $this->cachedNodes[$cacheKey] = [];
        }

        if (!array_key_exists($firstGoalCode, $this->cachedNodes[$cacheKey])) {
            $curriculumNode = $curriculumType === CurriculumType::Zill ?
                $this->findZillCurriculumnodeInDatabase($educationalNetwork, $goalCodes) :
                $this->findCurriculumnodeInDatabase($educationalNetwork, $goalCodes);
            $this->cachedNodes[$cacheKey][$firstGoalCode] = $curriculumNode;

            if (!$curriculumNode) {
                $this->reportMissingGoal($firstGoalCode, $educationalNetwork, $curriculumType);
            }
        }

        return $this->cachedNodes[$cacheKey][$firstGoalCode];
    }

    protected function findZillCurriculumnodeInDatabase(EducationalNetwork $educationalNetwork, array $goalCodes): ?int
    {
        $goalCode = Arr::first($goalCodes);

        if (!Uuid::isValid($goalCode)) {
            return $this->findZillGoalByCodeInDatabase($educationalNetwork, $goalCode);
        }

        $curriculumNode = CurriculumNode::query()
            ->where('educationalnetwork_id', $educationalNetwork->id)
            ->where('type', CurriculumNodeType::Goal)
            ->where('reference_code', $goalCode)
            ->where('is_active', 1)
            ->first(['id']);

        return optional($curriculumNode)->id;
    }

    protected function findZillGoalByCodeInDatabase(EducationalNetwork $educationalNetwork, string $goal): ?int
    {
        $goalParts = explode('_', $goal);
        $goal = $goalParts[0];
        // When there is no underscore, the first goal should be used!
        $sequenceNumber = $goalParts[1] ?? 0;

        $results = CurriculumNode::query()
            ->where('educationalnetwork_id', $educationalNetwork->id)
            ->where('type', CurriculumNodeType::Goal)
            ->where('zill_version_id', $this->getCurrentZillVersionId())
            ->where('is_active', 1)
            ->where('code', $goal)
            ->orderBy('sequence')
            ->pluck('id');

        return $results->get($sequenceNumber);
    }

    protected function findCurriculumnodeInDatabase(EducationalNetwork $educationalNetwork, array $goalCodes): ?int
    {
        $goalSearchConfig = $this->subjectString === 'GOD' ?
            $this->getGoalParser()->getGoalSearchConfigForReligion() :
            $this->getGoalParser()->getGoalSearchConfig($educationalNetwork);

        $curriculumNode = CurriculumNode::query()
            ->where('educationalnetwork_id', $educationalNetwork->id)
            ->where('type', CurriculumNodeType::Goal)
            ->where(
                function (Builder $query) use (
                    $goalSearchConfig,
                    $goalCodes,
                    $educationalNetwork
                ): void {
                    [$uuids, $codes] = collect($goalCodes)->partition(function (string $goalCode) {
                        return Uuid::isValid($goalCode);
                    });
                    $this->addUuidLookups($query, $uuids);
                    $this->addGoalCodeLookups($query, $codes, $goalSearchConfig, $educationalNetwork);
                },
            )
            ->first(['id']);

        return optional($curriculumNode)->id;
    }

    protected function addUuidLookups(Builder $query, Collection $goalCodes): void
    {
        $query->orWhereIn('smartschool_uid', $goalCodes)
            ->orWhereIn('reference_code', $goalCodes);
    }

    protected function addGoalCodeLookups(
        Builder $query,
        Collection $goalCodes,
        array $goalSearchConfig,
        EducationalNetwork $educationalNetwork,
    ): void {
        $goalCodes = $this->transformGoalCodes($educationalNetwork, $goalCodes, $goalSearchConfig);
        $field = $goalSearchConfig['field'];
        $operator = $goalSearchConfig['operator'];

        $goalCodes->each(function (string $goalCode) use ($query, $field, $operator): void {
            $query->orWhere($field, $operator, $goalCode);
        });
    }

    protected function transformGoalCodes(
        EducationalNetwork $educationalNetwork,
        Collection $goalCodes,
        array $goalConfig,
    ): Collection {
        $stripNonAlpha = $goalConfig['stripNonAlpha'];
        $addLearningArea = $goalConfig['addLearningArea'];

        return $goalCodes
            ->flatMap(function (string $goalCode) use ($educationalNetwork) {
                return Arr::wrap($this->getGoalParser()->transformNetworkGoal($educationalNetwork, $goalCode));
            })
            ->map(function (string $goalCode) use ($stripNonAlpha, $addLearningArea) {
                // Search with learningarea prefix and wildcard, example WIS-%-1.1.2.
                if ($this->subjectString && $addLearningArea) {
                    $goalCode = $this->subjectString . '%' . $goalCode;
                }

                if ($stripNonAlpha) {
                    // Replace special chars to underscore.
                    $goalCode = preg_replace("/[\(\.\- ]/", '_', $goalCode);
                    // Remove the leading zeros.
                    $goalCode = preg_replace("/_0+(\d)/", '_$1', $goalCode);
                    // Remove non-alphanumeric characters.
                    $goalCode = preg_replace('/[^A-Za-z0-9_]/', '', $goalCode);
                }

                return $goalCode;
            });
    }

    protected function reportMissingGoal(
        string $goalCode,
        EducationalNetwork $educationalNetwork,
        CurriculumType $curriculumType,
    ): void {
        $this->missingGoals[] = $goalCode . ' (' . $educationalNetwork->name . ' - ' . $curriculumType->name . ')';
    }

    protected function getEducationalNetworksByUid(): Collection
    {
        $this->educationalNetworksByUid ??= EducationalNetwork::query()
            ->whereIn('uid', [
                EducationalNetwork::GO_UID,
                EducationalNetwork::VVKBAO_UID,
                EducationalNetwork::OVSG_UID,
            ])
            ->get()
            ->keyBy('uid');

        return $this->educationalNetworksByUid;
    }

    protected function getCurrentZillVersionId(): int
    {
        $this->currentZillVersionId ??= ZillVersion::whereIsCurrent(true)->value('id');

        return $this->currentZillVersionId;
    }

    protected function getGoalParser(): AbstractParser
    {
        return $this->goalParser;
    }

    public function getMissingGoals(): array
    {
        return $this->missingGoals;
    }
}
