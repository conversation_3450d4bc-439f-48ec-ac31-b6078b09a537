<?php

namespace Cfa\Planner\Application\Console\Commands;

use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Cfa\Planner\Domain\CurriculumNode\Reporting\CurriculumNodeReporting;
use Illuminate\Console\Command;
use Illuminate\Database\Query\Builder;
use Override;
use Symfony\Component\Console\Input\InputOption;

class CleanUpCurriculumnodeReporting extends Command
{
    /** @var string */
    protected $name = 'tms:reporting:clean';

    /** @var string */
    protected $description = 'Cleans incorrect entries from the curriculumnode_reporting tables for the given or' .
        'current schoolyear';

    private const BATCH_SIZE = 1000000;

    public function handle(): void
    {
        $schoolyear = $this->getSchoolyear();

        $min = CurriculumNodeReporting::forSchoolyear($schoolyear)->min('calendar_item_row_id') ?? 0;
        $max = CurriculumNodeReporting::forSchoolyear($schoolyear)->max('calendar_item_row_id') ?? 0;

        $bar = $this->output->createProgressBar($max);
        $bar->start();

        for ($startId = $min; $startId <= $max; $startId += self::BATCH_SIZE) {
            $endId = $startId + self::BATCH_SIZE;
            CurriculumNodeReporting::forSchoolyear($schoolyear)
                ->whereNotIn(
                    'calendar_item_row_id',
                    fn(Builder $builder): Builder => $builder->select('calendaritemrows.id')
                        ->from('calendaritemrows')
                        ->join('calendaritems', 'calendaritem_id', 'calendaritems.id')
                        ->whereNull('calendaritems.deleted_at')
                        ->whereNull('calendaritemrows.deleted_at')
                        ->where('calendar_item_row_id', '>=', $startId)
                        ->where('calendar_item_row_id', '<', $endId),
                )
                ->where('calendar_item_row_id', '>=', $startId)
                ->where('calendar_item_row_id', '<', $endId)
                ->delete();
            $bar->advance(self::BATCH_SIZE);
        }
        $bar->finish();
    }

    private function getSchoolyear(): Schoolyear
    {
        $schoolyearRepository = app(SchoolyearRepositoryInterface::class);
        $startYear = $this->option('schoolyear');

        if ($startYear !== null) {
            return $schoolyearRepository->findSchoolyearByYear($startYear);
        }

        return $schoolyearRepository->getCurrent();
    }

    #[Override]
    protected function getOptions(): array
    {
        return array_merge(
            [
                [
                    '--schoolyear',
                    null,
                    InputOption::VALUE_OPTIONAL,
                    'The start year of the schoolyear to clean the curriculumnode_reporting_queue table for.',
                ],
            ],
            parent::getOptions(),
        );
    }
}
