<?php

namespace Cfa\Planner\Application\Console\Commands;

use App\Models\Feature\Feature;
use App\Models\Feature\FeatureToggle;
use Cfa\Common\Domain\Permission\CollectionLicense;
use Cfa\Common\Domain\Permission\CollectionLicenseRepositoryInterface;
use Cfa\Common\Domain\Permission\LicenseProductIdActivationTriggered;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

class ActivateLicensedCollections extends Command
{
    /** {@inheritdoc} */
    protected $signature = 'tms:collections:activate-licensed-collections
        {--incremental= : Check only licences created/edited in X previous hours. : int}
    ';

    /** {@inheritdoc} */
    protected $description = 'Activate licensed collections';

    public function __construct(private CollectionLicenseRepositoryInterface $licenseRepository)
    {
        parent::__construct();
    }

    public function handle(): void
    {
        if (!FeatureToggle::isActive(Feature::AutomaticallyActivateLicensedCollections)) {
            return;
        }

        $incremental = $this->option('incremental');

        if (!is_null($incremental) && !is_numeric($incremental)) {
            throw new InvalidArgumentException('Incremental must be a number');
        }

        $productsIds = $this->licenseRepository
            ->scopeLicenseEligibleForCollectionActivation(CollectionLicense::query())
            ->when(
                $incremental,
                fn(Builder $query): Builder => $this->licenseRepository->scopeLicenseCreatedOrUpdatedRecently(
                    $query,
                    $incremental,
                ),
            )
            ->select('product_id')
            ->distinct()
            ->pluck('product_id');

        foreach ($productsIds as $productId) {
            event(new LicenseProductIdActivationTriggered($productId));
        }

        Log::info(sprintf('Triggered %s license product activations.', $productsIds->count()));
    }
}
