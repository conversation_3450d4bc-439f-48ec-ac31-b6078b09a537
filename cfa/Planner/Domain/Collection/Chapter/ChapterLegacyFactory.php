<?php

namespace Cfa\Planner\Domain\Collection\Chapter;

use Cfa\Planner\Domain\Collection\PlannerCollection;
use Faker\Generator;

$factory->define(Chapter::class, function (Generator $faker) {
    $parentChapterIds = Chapter::whereNull('parent_chapter_id')->pluck('id');

    return [
        'uid' => uuid(),
        'plannercollection_id' => PlannerCollection::randomId(),
        // If there are no parent chapters yet, this will be a publisher chapter.
        // Otherwise, there is a chance it will be a user chapter.
        'parent_chapter_id' => $parentChapterIds->isEmpty() ? null :
            $faker->optional()->randomElement($parentChapterIds->toArray()),
        'name' => $faker->name,
        'order' => $faker->numberBetween(0, 100),
        'masked_at' => null,
        'is_template' => false,
    ];
});

$factory->state(Chapter::class, 'sharable', function (Generator $faker) {
    return [
        'parent_chapter_id' => null,
    ];
});
