<?php

namespace Cfa\Planner\Domain\Collection\Chapter;

use App\Casts\EncodedString;
use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\Masked;
use Cfa\Common\Application\Traits\PruneSoftDeletes;
use Cfa\Common\Application\Traits\RestoreSoftDeletes;
use Cfa\Common\Application\Traits\TouchedByUser;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\Collection\PlannerCollection;
use Cfa\Planner\Domain\Record\Record;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Override;

/**
 * Cfa\Planner\Domain\Collection\Chapter\Chapter
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property int $plannercollection_id
 * @property int|null $parent_chapter_id References the parent chapter
 * @property string $name
 * @property int $order Give a chapter an order
 * @property int|null $creator_id
 * @property int|null $updater_id
 * @property Carbon|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $masked_at Time when the activated chapter was deleted.
 * @property bool $is_template
 * @property int|null $published_parent_id
 * @property-read User|null $creator
 * @property-read array $validation_rules
 * @property-read Chapter|null $parentChapter
 * @property-read PlannerCollection $plannercollection
 * @property-read Collection<int, Record> $records
 * @property-read int|null $records_count
 * @property-read User|null $updater
 * @method static ChapterFactory factory($count = null, $state = [])
 * @method static ChapterRepositoryInterface getRepository()
 * @method static Builder|Chapter newModelQuery()
 * @method static Builder|Chapter newQuery()
 * @method static Builder|Chapter onlyTrashed()
 * @method static Builder|Chapter query()
 * @method static Carbon|null randomCreatedAt()
 * @method static int|null randomCreatorId()
 * @method static Carbon|null randomDeletedAt()
 * @method static int randomId()
 * @method static bool randomIsTemplate()
 * @method static Carbon|null randomMaskedAt()
 * @method static string randomName()
 * @method static int randomOrder()
 * @method static int|null randomParentChapterId()
 * @method static int randomPlannercollectionId()
 * @method static int|null randomPublishedParentId()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static int|null randomUpdaterId()
 * @method static Builder|Chapter whereCreatedAt($value)
 * @method static Builder|Chapter whereCreatorId($value)
 * @method static Builder|Chapter whereDeletedAt($value)
 * @method static Builder|Chapter whereId($value)
 * @method static Builder|Chapter whereIsTemplate($value)
 * @method static Builder|Chapter whereMaskedAt($value)
 * @method static Builder|Chapter whereName($value)
 * @method static Builder|Chapter whereOrder($value)
 * @method static Builder|Chapter whereParentChapterId($value)
 * @method static Builder|Chapter wherePlannercollectionId($value)
 * @method static Builder|Chapter wherePublishedParentId($value)
 * @method static Builder|Chapter whereUid($value)
 * @method static Builder|Chapter whereUpdatedAt($value)
 * @method static Builder|Chapter whereUpdaterId($value)
 * @method static Builder|Chapter withTrashed()
 * @method static Builder|Chapter withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class Chapter extends Model
{
    use PruneSoftDeletes;
    use Uid;
    use RestoreSoftDeletes;
    use SoftDeletes;
    use TouchedByUser;
    use Masked;

    /**
     * Update parent timestamps.
     *
     * @var array
     */
    protected $touches = ['plannercollection'];

    /**
     * {@inheritdoc}
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'order',
    ];

    /**
     * {@inheritdoc}
     *
     * @var array
     */
    protected $casts = [
        'is_archived' => 'bool',
        'is_template' => 'boolean',
        'name' => EncodedString::class,
        'order' => 'integer',
        'parent_chapter_id' => 'integer',
        'plannercollection_id' => 'integer',
    ];

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'name' => [
                    'required',
                    'string',
                    'max:255',
                ],
                'plannercollection_id' => [
                    'required',
                    'integer',
                ],
                'is_template' => [
                    'boolean',
                ],
            ],
        );
    }

    /**
     * Get the collection of the chapter.
     */
    public function plannercollection(): BelongsTo
    {
        return $this->belongsTo(PlannerCollection::class, 'plannercollection_id');
    }

    /**
     * Get the records of the chapter.
     */
    public function records(): HasMany
    {
        return $this->hasMany(Record::class)->orderBy('order');
    }

    /**
     * Get the linked parent chapter.
     */
    public function parentChapter(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_chapter_id');
    }

    /**
     * Duplicate this Chapter.
     *
     * @param array $attributes The attributes you want to override.
     * @param int|null $collectionId The id of the collection that needs to be linked to the duplicated Chapter.
     */
    private function duplicate(?array $attributes = null, ?int $collectionId = null): self
    {
        $newChapter = $this->replicate();
        if (isset($attributes)) {
            $newChapter->fill($attributes);
        }

        if (!isset($collectionId) || $collectionId === $this->plannercollection_id) {
            // Duplicate inside own collection => update the order.
            $newChapter->order += 1;
            $this->updateOrder($newChapter->order);
        }

        $newChapter->plannercollection_id = $collectionId;

        $newChapter->save();

        return $newChapter;
    }

    /**
     * Duplicate all the records from this chapter.
     *
     * @param array|null $attributes The attributes you want to overrride.
     * @param int|null $chapterId The chapter that must be linked to the duplicated Record.
     */
    private function duplicateRecords(?array $attributes = null, ?int $chapterId = null): void
    {
        $this->records->each->duplicate($attributes, ['chapter_id' => $chapterId]);
    }

    /**
     * Duplicate this chapter with all the records inclusive.
     *
     * @param array|null $attributes The attributes you want to overrride.
     * @param int|null $collectionId The id of the collection that needs to be linked to the duplicated Chapter.
     */
    public function duplicateWithRecords(?array $attributes = null, ?int $collectionId = null): self
    {
        $newChapter = $this->duplicate($attributes, $collectionId);
        $this->duplicateRecords([], $newChapter->id);

        return $newChapter;
    }

    /**
     * Delete the records too when the chapter is deleted.
     */
    public function deleteWithRecords(): ?bool
    {
        if ($result = $this->delete()) {
            Record::withMasked()->where('chapter_id', $this->id)->delete();
        }

        return $result;
    }

    /**
     * Unlink the records when the chapter is deleted.
     */
    public function deleteAndUnlinkRecords(): ?bool
    {
        if ($result = $this->delete()) {
            $this->records->each->unlinkOrDeleteRecord();
        }

        return $result;
    }

    /**
     * Mask the records too when the chapter is masked.
     */
    public function maskAndUnlinkRecords(): ?bool
    {
        if ($result = $this->mask()) {
            $this->records->each->maskWithCalendaritemCheck();
        }

        return $result;
    }

    /**
     * Check if the chapter is created or updated inside the current plannercollection.
     */
    public function isCreatedInThisPlannerCollection(): bool
    {
        return
            is_null($this->parent_chapter_id) ||
            $this->parentChapter()
                ->withTrashed()
                ->withMasked()
                ->first()->plannercollection_id === $this->plannercollection_id;
    }

    /**
     * This will increase the order for all the chapters equal or above the given one.
     *
     * @param int $newOrder Order value.
     */
    public function updateOrder(int $newOrder): void
    {
        $this->plannercollection->chapters()->where('order', '>=', $newOrder)->increment('order');
    }
}
