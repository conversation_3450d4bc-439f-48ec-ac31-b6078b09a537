<?php

namespace Cfa\Planner\Domain\Collection\PublisherCollection;

use Illuminate\Queue\SerializesModels;

class PublisherCollectionCreated
{
    use SerializesModels;

    /**
     * The collection model that was saved.
     *
     * @var PublisherCollection
     */
    public $publishercollection;

    /**
     * Create a new event instance.
     *
     * @param PublisherCollection $publishercollection The created publisher collection.
     */
    public function __construct(PublisherCollection $publishercollection)
    {
        $this->publishercollection = $publishercollection;
    }
}
