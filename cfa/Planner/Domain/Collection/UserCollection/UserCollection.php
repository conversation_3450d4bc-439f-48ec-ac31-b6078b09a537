<?php

namespace Cfa\Planner\Domain\Collection\UserCollection;

use Carbon\Carbon;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudience;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\Collection\Attachment\PlannerCollectionAttachment;
use Cfa\Planner\Domain\Collection\Chapter\Chapter;
use Cfa\Planner\Domain\Collection\CollectionTemplate;
use Cfa\Planner\Domain\Collection\CollectionUpdatable;
use Cfa\Planner\Domain\Collection\PlannerCollection;
use Cfa\Planner\Domain\Collection\PlannerCollectionUpdated;
use Cfa\Planner\Domain\Collection\PublisherCollection\Publisher\Publisher;
use Cfa\Planner\Domain\Record\Record;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Support\Collection;
use Override;

/**
 * Cfa\Planner\Domain\Collection\UserCollection\UserCollection
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property MigrationStatus|null $migration_status
 * @property int|null $owner_id the user the collection belongs to
 * @property int|null $parent_collection_id the publishercollection the usercollection belongs to
 * @property int|null $publisher_id the publisher that owns the collection
 * @property string $name Name of the collection
 * @property int $version
 * @property string|null $extra_info Free text field for extra information related to the collection.
 * @property Carbon|null $activated_at
 * @property int|null $creator_id
 * @property int|null $updater_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property Carbon|null $sent_at Timestamp for the moment the (publisher) collection was sent.
 * @property Carbon|null $published_at
 * @property Carbon|null $archived_at
 * @property Carbon|null $migrate_started_at
 * @property Carbon|null $migrated_at
 * @property CollectionUpdatable $updatable
 * @property string $uid unique identifier for the collection
 * @property CollectionTemplate $template
 * @property int|null $published_parent_id
 * @property-read Collection<int, PlannerCollectionAttachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read Collection<int, Chapter> $chapters
 * @property-read int|null $chapters_count
 * @property-read Collection<int, Chapter> $chaptersWithMasked
 * @property-read int|null $chapters_with_masked_count
 * @property-read Collection<int, Chapter> $chaptersWithTemplates
 * @property-read int|null $chapters_with_templates_count
 * @property-read Collection<int, Chapter> $chaptersWithTemplatesAndMasked
 * @property-read int|null $chapters_with_templates_and_masked_count
 * @property-read User|null $creator
 * @property-read array $validation_rules
 * @property-read MediaCollection<int,Media> $media
 * @property-read int|null $media_count
 * @property-read User|null $owner
 * @property-read User|null $ownerWithTrashed
 * @property-read PlannerCollection|null $parentCollection
 * @property-read PlannerCollection|null $parentCollectionWithTrashed
 * @property-read UserCollection|null $parentUsercollection
 * @property-read Publisher|null $publisher
 * @property-read Collection<int, Record> $records
 * @property-read int|null $records_count
 * @property-read Collection<int, Record> $recordsWithMasked
 * @property-read int|null $records_with_masked_count
 * @property-read Collection<int, School> $schoolsWithAccess
 * @property-read int|null $schools_with_access_count
 * @property-read Collection<int, TargetAudience> $targetAudiences
 * @property-read int|null $target_audiences_count
 * @property-read Record|null $templateRecord
 * @property-read User|null $updater
 * @property-read Collection<int, UserCollection> $usercollections
 * @property-read int|null $usercollections_count
 * @property-read Collection<int, User> $usersWithAccess
 * @property-read int|null $users_with_access_count
 * @method static UserCollectionRepositoryInterface getRepository()
 * @method static Builder|PlannerCollection isPublisherCollection()
 * @method static Builder|UserCollection newModelQuery()
 * @method static Builder|UserCollection newQuery()
 * @method static Builder|UserCollection onlyTrashed()
 * @method static Builder|UserCollection query()
 * @method static Carbon|null randomActivatedAt()
 * @method static Carbon|null randomArchivedAt()
 * @method static Carbon|null randomCreatedAt()
 * @method static int|null randomCreatorId()
 * @method static Carbon|null randomDeletedAt()
 * @method static string|null randomExtraInfo()
 * @method static int randomId()
 * @method static Carbon|null randomMigrateStartedAt()
 * @method static Carbon|null randomMigratedAt()
 * @method static MigrationStatus|null randomMigrationStatus()
 * @method static string randomName()
 * @method static int|null randomOwnerId()
 * @method static int|null randomParentCollectionId()
 * @method static Carbon|null randomPublishedAt()
 * @method static int|null randomPublishedParentId()
 * @method static int|null randomPublisherId()
 * @method static Carbon|null randomSentAt()
 * @method static CollectionTemplate randomTemplate()
 * @method static string randomUid()
 * @method static CollectionUpdatable randomUpdatable()
 * @method static Carbon|null randomUpdatedAt()
 * @method static int|null randomUpdaterId()
 * @method static int randomVersion()
 * @method static Builder|UserCollection whereActivatedAt($value)
 * @method static Builder|UserCollection whereArchivedAt($value)
 * @method static Builder|UserCollection whereCreatedAt($value)
 * @method static Builder|UserCollection whereCreatorId($value)
 * @method static Builder|UserCollection whereDeletedAt($value)
 * @method static Builder|UserCollection whereExtraInfo($value)
 * @method static Builder|UserCollection whereId($value)
 * @method static Builder|UserCollection whereMigrateStartedAt($value)
 * @method static Builder|UserCollection whereMigratedAt($value)
 * @method static Builder|UserCollection whereMigrationStatus($value)
 * @method static Builder|UserCollection whereName($value)
 * @method static Builder|UserCollection whereOwnerId($value)
 * @method static Builder|UserCollection whereParentCollectionId($value)
 * @method static Builder|UserCollection wherePublishedAt($value)
 * @method static Builder|UserCollection wherePublishedParentId($value)
 * @method static Builder|UserCollection wherePublisherId($value)
 * @method static Builder|UserCollection whereSentAt($value)
 * @method static Builder|UserCollection whereTemplate($value)
 * @method static Builder|UserCollection whereUid($value)
 * @method static Builder|UserCollection whereUpdatable($value)
 * @method static Builder|UserCollection whereUpdatedAt($value)
 * @method static Builder|UserCollection whereUpdaterId($value)
 * @method static Builder|UserCollection whereVersion($value)
 * @method static Builder|UserCollection withTrashed()
 * @method static Builder|UserCollection withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class UserCollection extends PlannerCollection
{
    /**
     * {@inheritdoc}
     *
     * @var array
     */
    protected $dispatchesEvents = [
        'deleted' => UserCollectionDeleted::class,
        'updated' => PlannerCollectionUpdated::class,
        'created' => UserCollectionCreated::class,
    ];

    /**
     * Set global scope for user collections.
     */
    #[Override]
    protected static function boot(): void
    {
        parent::boot();
        static::addGlobalScope('userCollection', function (Builder $query): Builder {
            return $query->whereNotNull('owner_id');
        });
    }

    /**
     * Get the parent user collection of the user collection.
     */
    public function parentUsercollection(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_collection_id');
    }

    /**
     * The template chapters are invisible and only used for template records.
     * Access to the template records is given using the templateRecord relation.
     */
    #[Override]
    public function chapters(): HasMany
    {
        return parent::chapters()->where('is_template', 0);
    }

    /**
     * Get the template record of the collection.
     */
    public function templateRecord(): HasOneThrough
    {
        return $this->hasOneThrough(Record::class, Chapter::class, 'plannercollection_id')
            ->where('chapters.is_template', 1)
            ->where('records.is_template', 1)
            ->orderByDesc('updated_at');
    }
}
