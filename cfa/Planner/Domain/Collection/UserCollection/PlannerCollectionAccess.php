<?php

namespace Cfa\Planner\Domain\Collection\UserCollection;

use App\Models\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\MassPrunable;
use Illuminate\Database\Query\JoinClause;

/**
 * Cfa\Planner\Domain\Collection\UserCollection\PlannerCollectionAccess
 *
 * @codingStandardsIgnoreStart
 * @property int $plannercollection_id
 * @property string $entity_type
 * @property int $entity_id
 * @property-read array $validation_rules
 * @method static Builder|PlannerCollectionAccess newModelQuery()
 * @method static Builder|PlannerCollectionAccess newQuery()
 * @method static Builder|PlannerCollectionAccess query()
 * @method static int randomEntityId()
 * @method static string randomEntityType()
 * @method static int randomPlannercollectionId()
 * @method static Builder|PlannerCollectionAccess whereEntityId($value)
 * @method static Builder|PlannerCollectionAccess whereEntityType($value)
 * @method static Builder|PlannerCollectionAccess wherePlannercollectionId($value)
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class PlannerCollectionAccess extends Model
{
    use MassPrunable;

    /** @var string */
    protected $table = 'plannercollection_access';

    public function prunable(): Builder
    {
        return static::query()
            ->leftJoin('schools', function ($join): void {
                $join->on('schools.id', '=', 'plannercollection_access.entity_id')
                    ->where('plannercollection_access.entity_type', '=', 'school');
            })
            ->leftJoin('users', function (JoinClause $join): void {
                $join->on('users.id', '=', 'plannercollection_access.entity_id')
                    ->where('plannercollection_access.entity_type', '=', 'user');
            })
            ->where(function (Builder $query): void {
                $query->where('plannercollection_access.entity_type', 'school')->whereNull('schools.id');
            })
            ->orWhere(function (Builder $query): void {
                $query->where('plannercollection_access.entity_type', 'user')->whereNull('users.id');
            });
    }
}
