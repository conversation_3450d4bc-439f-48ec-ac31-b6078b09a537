<?php

namespace Cfa\Planner\Domain\Collection\UserCollection;

use Cfa\Common\Domain\User\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;
use Override;

/**
 * @mixin Collection
 * @extends JsonResource<Collection>
 */
class CollectionShareStatusResource extends JsonResource
{
    /**
     * {@inheritdoc}
     *
     * @param Request $request The request.
     */
    #[Override]
    public function toArray($request): array
    {
        /* @var Collection $usersWhoActivatedCollection */
        $usersWhoActivatedCollection = $this->get('usersWhoActivatedCollection');
        /* @var Collection $usersWithAccess */
        $usersWithAccess = $this->get('usersWithAccess');
        /* @var Collection|School[] $schoolsWithAccess */
        $schoolsWithAccess = $this->get('schoolsWithAccess');

        $usersWithoutIndividualAccess = $usersWhoActivatedCollection
            ->diff($usersWithAccess)
            ->map(fn(User $user): array => [
                'user' => $user,
                'activated' => true,
                'shared' => false,
            ]);
        $userIdsWhoActivatedCollection = $usersWhoActivatedCollection->pluck('id');
        $usersWithIndividualAccess = $usersWithAccess
            ->map(fn(User $user): array => [
                'user' => $user,
                'activated' => $userIdsWhoActivatedCollection->contains($user->id),
                'shared' => true,
            ]);

        return [
            'users' => new CollectionUserShareStatusResourceCollection(
                $usersWithIndividualAccess->concat($usersWithoutIndividualAccess),
            ),
            'schools' => $schoolsWithAccess->pluck('uid'),
        ];
    }
}
