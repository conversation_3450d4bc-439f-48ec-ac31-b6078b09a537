<?php

namespace Cfa\Planner\Domain\Record;

use App\Casts\EncodedString;
use App\Casts\RichTextField;
use App\Casts\SanitizedUrl;
use App\Models\File\FileUsage;
use App\Models\Model;
use App\Services\File\FileUploadService;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\Masked;
use Cfa\Common\Application\Traits\PruneSoftDeletes;
use Cfa\Common\Application\Traits\RestoreSoftDeletes;
use Cfa\Common\Application\Traits\TouchedByUser;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\CalendarItem\CalendarItem;
use Cfa\Planner\Domain\CalendarItem\Row\CalendarItemRow;
use Cfa\Planner\Domain\Collection\Chapter\Chapter;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNode;
use Cfa\Planner\Domain\Record\Material\Material;
use Cfa\Planner\Domain\Record\Material\MaterialType;
use Cfa\Planner\Domain\Record\RecordExperienceSituationType\RecordExperienceSituationType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Override;

use function array_merge;

/**
 * Cfa\Planner\Domain\Record\Record
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid unique identifier for the record
 * @property int|null $parent_record_id
 * @property string|null $publisher_record_uid used to store data needed for mapping after import.
 * @property int|null $owner_id
 * @property int|null $chapter_id The chapter the record belongs to
 * @property bool $is_duration_applicable if true, the user can change duration_minutes.
 * @property int|null $duration_minutes
 * @property string $name
 * @property int $order Give a record an order
 * @property string|null $manual
 * @property RecordPurpose $purpose
 * @property string|null $start_situation
 * @property string|null $teacher_goals
 * @property string|null $differentiation
 * @property string|null $lesson_steps
 * @property string|null $tips
 * @property string|null $work_methods
 * @property string|null $manual_license_uid
 * @property int|null $creator_id
 * @property int|null $updater_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property bool|null $zill
 * @property Carbon|null $masked_at First time the activated record was changed, duplicated and hidden.
 * @property bool $is_template
 * @property string|null $boardbook_url
 * @property string|null $instruction_movie_url_1
 * @property string|null $instruction_movie_url_2
 * @property string|null $instruction_movie_url_3
 * @property int|null $published_parent_id
 * @property-read Collection<int, Material> $allMaterials
 * @property-read int|null $all_materials_count
 * @property-read Collection<int, Material> $availableMaterials
 * @property-read int|null $available_materials_count
 * @property-read Collection<int, CalendarItem> $calendaritems
 * @property-read int|null $calendaritems_count
 * @property-read Chapter|null $chapter
 * @property-read User|null $creator
 * @property-read Collection<int, CurriculumNode> $curriculumnodes
 * @property-read int|null $curriculumnodes_count
 * @property-read Collection<int, RecordExperienceSituationType> $experienceSituationTypes
 * @property-read int|null $experience_situation_types_count
 * @property-read array $validation_rules
 * @property-read User|null $owner
 * @property-read User|null $ownerWithTrashed
 * @property-read Record|null $parentRecord
 * @property-read Record|null $trashedParentRecord
 * @property-read User|null $updater
 * @method static RecordRepositoryInterface getRepository()
 * @method static Builder|Record newModelQuery()
 * @method static Builder|Record newQuery()
 * @method static Builder|Record onlyTrashed()
 * @method static Builder|Record query()
 * @method static string|null randomBoardbookUrl()
 * @method static int|null randomChapterId()
 * @method static Carbon|null randomCreatedAt()
 * @method static int|null randomCreatorId()
 * @method static Carbon|null randomDeletedAt()
 * @method static string|null randomDifferentiation()
 * @method static int|null randomDurationMinutes()
 * @method static int randomId()
 * @method static string|null randomInstructionMovieUrl1()
 * @method static string|null randomInstructionMovieUrl2()
 * @method static string|null randomInstructionMovieUrl3()
 * @method static bool randomIsDurationApplicable()
 * @method static bool randomIsTemplate()
 * @method static string|null randomLessonSteps()
 * @method static string|null randomManual()
 * @method static string|null randomManualLicenseUid()
 * @method static Carbon|null randomMaskedAt()
 * @method static string randomName()
 * @method static int randomOrder()
 * @method static int|null randomOwnerId()
 * @method static int|null randomParentRecordId()
 * @method static int|null randomPublishedParentId()
 * @method static string|null randomPublisherRecordUid()
 * @method static RecordPurpose randomPurpose()
 * @method static string|null randomStartSituation()
 * @method static string|null randomTeacherGoals()
 * @method static string|null randomTips()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static int|null randomUpdaterId()
 * @method static string|null randomWorkMethods()
 * @method static bool|null randomZill()
 * @method static Builder|Record whereBoardbookUrl($value)
 * @method static Builder|Record whereChapterId($value)
 * @method static Builder|Record whereCreatedAt($value)
 * @method static Builder|Record whereCreatorId($value)
 * @method static Builder|Record whereDeletedAt($value)
 * @method static Builder|Record whereDifferentiation($value)
 * @method static Builder|Record whereDurationMinutes($value)
 * @method static Builder|Record whereId($value)
 * @method static Builder|Record whereInstructionMovieUrl1($value)
 * @method static Builder|Record whereInstructionMovieUrl2($value)
 * @method static Builder|Record whereInstructionMovieUrl3($value)
 * @method static Builder|Record whereIsDurationApplicable($value)
 * @method static Builder|Record whereIsTemplate($value)
 * @method static Builder|Record whereLessonSteps($value)
 * @method static Builder|Record whereManual($value)
 * @method static Builder|Record whereManualLicenseUid($value)
 * @method static Builder|Record whereMaskedAt($value)
 * @method static Builder|Record whereName($value)
 * @method static Builder|Record whereOrder($value)
 * @method static Builder|Record whereOwnerId($value)
 * @method static Builder|Record whereParentRecordId($value)
 * @method static Builder|Record wherePublishedParentId($value)
 * @method static Builder|Record wherePublisherRecordUid($value)
 * @method static Builder|Record wherePurpose($value)
 * @method static Builder|Record whereStartSituation($value)
 * @method static Builder|Record whereTeacherGoals($value)
 * @method static Builder|Record whereTips($value)
 * @method static Builder|Record whereUid($value)
 * @method static Builder|Record whereUpdatedAt($value)
 * @method static Builder|Record whereUpdaterId($value)
 * @method static Builder|Record whereWorkMethods($value)
 * @method static Builder|Record whereZill($value)
 * @method static Builder|Record withTrashed()
 * @method static Builder|Record withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 *
 * @SuppressWarnings(PHPMD.TooManyPublicMethods)
 * @SuppressWarnings(PHPMD.ExcessiveClassComplexity)
 */
class Record extends Model
{
    use PruneSoftDeletes;
    use Uid;
    use RestoreSoftDeletes;
    use SoftDeletes;
    use TouchedByUser;
    use Masked;

    /**
     * Maximum size for the name of a Record.
     *
     * @var int
     */
    public const MAX_RECORD_NAME_SIZE = 145;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'duration_minutes',
        'is_duration_applicable',
        'name',
        'order',
        'purpose',
        'start_situation',
        'teacher_goals',
        'differentiation',
        'lesson_steps',
        'tips',
        'work_methods',
        'boardbook_url',
        'instruction_movie_url_1',
        'instruction_movie_url_2',
        'instruction_movie_url_3',
    ];

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'name' => [
                    'required',
                    'string',
                    'max:255',
                ],
                'is_template' => [
                    'boolean',
                ],
                'instruction_movie_url_1' => [
                    'nullable',
                    'string',
                    'max:500',
                ],
                'instruction_movie_url_2' => [
                    'nullable',
                    'string',
                    'max:500',
                ],
                'instruction_movie_url_3' => [
                    'nullable',
                    'string',
                    'max:500',
                ],
                'boardbook_url' => [
                    'nullable',
                    'string',
                    'max:500',
                ],
            ],
        );
    }

    /**
     * {@inheritdoc}
     *
     * @var array
     */
    protected $dispatchesEvents = [
        'curriculumnode_record' => CurriculumnodeRecordSynced::class,
    ];

    protected array $enums = [
        'purpose' => RecordPurpose::class,
    ];

    /**
     * The attributes thatŒ should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'boardbook_url' => SanitizedUrl::class,
        'chapter_id' => 'integer',
        'differentiation' => RichTextField::class,
        'duration_minutes' => 'integer',
        'instruction_movie_url_1' => SanitizedUrl::class,
        'instruction_movie_url_2' => SanitizedUrl::class,
        'instruction_movie_url_3' => SanitizedUrl::class,
        'is_duration_applicable' => 'boolean',
        'is_template' => 'boolean',
        'lesson_steps' => RichTextField::class,
        'name' => EncodedString::class,
        'order' => 'integer',
        'owner_id' => 'integer',
        'parent_record_id' => 'integer',
        'start_situation' => RichTextField::class,
        'teacher_goals' => RichTextField::class,
        'tips' => RichTextField::class,
        'work_methods' => RichTextField::class,
        'zill' => 'boolean',
    ];

    /**
     * Update parent timestamps.
     *
     * @var array
     */
    protected $touches = ['chapter'];

    /**
     * Get the user linked to the record.
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * Get the (possible trashed) user linked to the record.
     */
    public function ownerWithTrashed(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id')->withTrashed();
    }

    /**
     * Get the chapter linked to the record.
     */
    public function chapter(): BelongsTo
    {
        return $this->belongsTo(Chapter::class);
    }

    /**
     * Get the chapter linked to the record.
     */
    public function chapterWithMasked(): BelongsTo
    {
        return $this->chapter()->withMasked();
    }

    /**
     * Get all the materials linked to the record.
     */
    public function allMaterials(): HasMany
    {
        return $this->hasMany(Material::class)
            ->where(
                fn(Builder $whereQuery): Builder => $whereQuery
                    ->where(fn(Builder $query): Builder => $query
                        ->where('type', MaterialType::File->value)->whereNotNull('file'))
                    ->orWhere('type', '<>', MaterialType::File->value),
            );
    }

    /**
     * Get only the available materials linked to the record.
     */
    public function availableMaterials(): HasMany
    {
        return $this->hasMany(Material::class)->where('is_available', '=', 1);
    }

    /**
     * Get the curriculum nodes for the record.
     */
    public function curriculumnodes(): BelongsToMany
    {
        return $this->belongsToMany(CurriculumNode::class, 'curriculumnode_record', 'record_id', 'curriculumnode_id');
    }

    /**
     * Get the Calendar items for the record.
     */
    public function calendaritems(): BelongsToMany
    {
        return $this->belongsToMany(CalendarItem::class, 'calendaritemrows', 'record_id', 'calendaritem_id')
            ->whereNull('calendaritemrows.deleted_at')
            ->distinct();
    }

    /**
     * Get the Linked parent record.
     */
    public function parentRecord(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_record_id')->withMasked();
    }

    public function trashedParentRecord(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_record_id')->withMasked()->onlyTrashed();
    }

    /**
     * Get experienceSituationTypes linked to the record.
     */
    public function experienceSituationTypes(): HasMany
    {
        return $this->hasMany(RecordExperienceSituationType::class);
    }

    /**
     * Duplicate this Record and sets a possible given chapter.
     *
     * @param array $attributes The attributes you want to override.
     * @param array|null $relations The relations you want to override.
     */
    public function duplicate(?array $attributes = null, ?array $relations = null): self
    {
        /* @var Record $newRecord */

        $newRecord = $this->replicate();
        if (isset($attributes)) {
            $newRecord->fill($attributes);
        }
        $newRecord->forceFill(collect($relations)->only([
            'chapter_id',
            'owner_id',
            'parent_record_id',
        ])->toArray());

        // there can only be one template
        $newRecord->is_template = false;

        if (!is_null($relations) && array_key_exists('chapter_id', $relations)) {
            if ($relations['chapter_id'] === $this->chapter_id) {
                // Duplicate inside own chapter => update the order.
                $newRecord->order += 1;
                $this->updateOrder($newRecord->order);
            }
        }

        $newRecord->save();

        $this->experienceSituationTypes->each(function ($experienceSituationType) use ($newRecord): void {
            $newRecord->experienceSituationTypes()->create([
                'experience_situation_type' => $experienceSituationType->experience_situation_type,
            ]);
        });

        if ($this->curriculumnodes->isNotEmpty()) {
            $newRecord->curriculumnodes()->sync($this->curriculumnodes);
        }

        $this->availableMaterials->each->duplicate([], $newRecord->id);

        return $newRecord->load('availableMaterials');
    }

    /**
     * Will link all the calenderitems of this record to an other one.
     *
     * @param self $record The record to link the calenderitems to.
     */
    public function moveCalenderItemsTo(self $record): void
    {
        if ($this->calendaritems->isNotEmpty()) {
            CalendarItemRow::withTrashed()
                ->whereRecordId($this->id)
                ->update(['record_id' => $record->id]);
        }
    }

    /**
     * Link materials to record, (soft) delete any removed materials.
     *
     * @param array $materials Array of materials from transformed request to link with record.
     */
    public function syncMaterials(User $user, array $materials): self
    {
        $fileUploadService = app(FileUploadService::class);
        $existingMaterials = $this->allMaterials()->pluck('id')->all();
        $newMaterials = [];

        foreach ($materials as $material) {
            $existing = array_key_exists('id', $material) ? Material::whereUid($material['id'])->first() : false;
            $basedOnExistingMaterial = $existing instanceof Material;
            // If material doesn't exist or belong to another record create it.
            // When a copy is created using an existing record materials id's belonging to another record will be used.
            if (!$existing || $this->id !== $existing->record_id) {
                // Create a new material linked to record.
                $existing = new Material();
                $existing->record_id = $this->id;
            }

            $filePath = $material['file'] ?? null;
            if (empty($existing->id) && !$basedOnExistingMaterial && isset($filePath)) {
                $filePath = $fileUploadService->uploadFile($user, FileUsage::Record, $filePath);
            }
            // Save the material.
            $existing->fill($material);
            $existing->file = $filePath;
            $existing->save();

            $newMaterials[] = $existing->id;
        }//end foreach

        // Check for materials to remove.
        if ($removeIds = array_diff($existingMaterials, $newMaterials)) {
            Material::whereIn('id', $removeIds)->delete();
            event('eloquent.updated: ' . self::class, $this);
        }

        return $this;
    }

    /**
     * Links the record to curriculum nodes.
     *
     * @param array $nodes Array of curriculum node uids.
     */
    public function syncCurriculumNodes(array $nodes): self
    {
        $sync = CurriculumNode::whereIn('uid', $nodes)->get(['id']);
        $this->curriculumnodes()->sync($sync);
        $this->save();

        return $this;
    }

    /**
     * Links the experienceSituationTypes to the record.
     */
    public function syncExperienceSituationTypes(array $experienceSituationTypes): self
    {
        $this->experienceSituationTypes()->delete();

        foreach ($experienceSituationTypes as $experienceSituationType) {
            $this->experienceSituationTypes()->create([
                'experience_situation_type' => $experienceSituationType,
            ]);
        }
        event(RecordExperienceSituationType::class . '.saved', $this);

        return $this;
    }

    /**
     * Unlink a record from his chapter or delete the record itself.
     */
    public function unlinkOrDeleteRecord(): ?bool
    {
        if ($this->calendaritems->isEmpty()) {
            return $this->delete();
        }

        $this->chapter()->dissociate();
        $this->save();

        return false;
    }

    /**
     * Mask a record.
     * If it has linked calendaritems, duplicate the record and relink the calendaritems.
     */
    public function maskWithCalendaritemCheck(): ?self
    {
        $duplicatedRecord = null;

        if ($this->calendaritems->isNotEmpty()) {
            $duplicatedRecord = $this->duplicate(null, [
                'chapter_id' => null,
                'parent_record_id' => $this->id,
            ]);
            CalendarItemRow::whereRecordId($this->id)->update(['record_id' => $duplicatedRecord->id]);
        }

        $this->mask();

        return $duplicatedRecord;
    }

    /**
     * Delete the record with the linked materials.
     */
    public function deleteWithMaterials(): ?bool
    {
        if ($result = $this->unlinkOrDeleteRecord()) {
            $this->allMaterials->each->delete();
        }

        return $result;
    }

    /**
     * Defines if this Record is not copied from someone else.
     */
    public function isSavedByOwner(): bool
    {
        return is_null($this->parent_record_id) ||
            !isset($this->parentRecord) ||
            $this->parentRecord->owner_id === $this->owner_id;
    }

    /**
     * Link all calendarItems to other record.
     *
     * @param Record $record The new record.
     */
    public function transferCalendarItems(self $record): void
    {
        DB::table('calendaritemrows')
            ->where('record_id', $this->id)
            ->update(['record_id' => $record->id]);
    }

    /**
     * This will increase the order for all the records equal or above the given one.
     *
     * @param int $newOrder Order value.
     */
    public function updateOrder(int $newOrder): void
    {
        $this->chapter->records()->where('order', '>=', $newOrder)->increment('order');
    }
}
