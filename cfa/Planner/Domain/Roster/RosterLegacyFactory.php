<?php

namespace Cfa\Planner\Domain\Roster;

use Carbon\Carbon;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Faker\Generator;

$factory->define(Roster::class, function (Generator $faker) {
    return [
        'owner_id' => User::randomId(),
        'school_id' => School::randomId(),
        'name' => $faker->name,
        'applicable_from' => Carbon::instance($faker->dateTimeBetween('-2 years', 'now'))->startOfDay(),
        'applicable_to' => Carbon::instance($faker->dateTimeBetween('now', '+2 years'))->startOfDay(),
        'creator_id' => User::randomId(),
        'updater_id' => User::randomId(),
    ];
});
