<?php

namespace Cfa\Planner\Domain\CollectionV2\Chapter\Record;

use Cfa\Planner\Domain\CollectionV2\AbstractPlannerCollectionFactory;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\Chapter\Chapter;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\Record\RecordPurpose;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Override;

/**
 * ChapterFactory
 *
 * @codingStandardsIgnoreStart
 * @method EloquentCollection|Record[]|Record create($attributes = [], Model|null $parent = null)
 * @method EloquentCollection|Record[]|Record createWithEvents(array $attributes = [], Model|null $parent = null)
 * @codingStandardsIgnoreEnd
 */
class RecordFactory extends AbstractPlannerCollectionFactory
{
    protected array $defaultQueryingEnabledOnModels = [
        PlannerCollection::class,
        ActivatedPlannerCollection::class,
        Chapter::class,
        Record::class,
    ];

    /** @var string */
    protected $model = Record::class;

    public function setUid(string $uid): self
    {
        return $this->set('uid', $uid);
    }

    public function forPlannerCollection(PlannerCollection $plannerCollection): self
    {
        return $this->for($plannerCollection, 'plannerCollection')
            ->afterMaking(function (Record $record) use ($plannerCollection): void {
                $record->setCollectionRelation($plannerCollection);
            });
    }

    public function forActivatedCollection(ActivatedPlannerCollection $activatedPlannerCollection): self
    {
        return $this->for($activatedPlannerCollection, 'activatedPlannerCollection')
            ->afterMaking(function (Record $record) use ($activatedPlannerCollection): void {
                $record->setCollectionRelation($activatedPlannerCollection);
            });
    }

    public function forChapter(?Chapter $chapter): self
    {
        return $this->set('chapter_id', $chapter?->id);
    }

    public function setPurpose(RecordPurpose $purpose): self
    {
        return $this->set('purpose', $purpose);
    }

    public function setName(string $name): self
    {
        return $this->set('name', $name);
    }

    public function setOrder(int $order): self
    {
        return $this->set('order', $order);
    }

    public function setZill(?bool $zill): self
    {
        return $this->set('zill', $zill);
    }

    public function setIsDurationApplicable(bool $isDurationApplicable): self
    {
        return $this->set('is_duration_applicable', $isDurationApplicable);
    }

    public function setIsTemplate(?bool $template): self
    {
        return $this->set('is_template', $template);
    }

    public function setDeletedAt(?Carbon $deletedAt): self
    {
        return $this->set('deleted_at', $deletedAt);
    }

    public function setCurriculumNodes(?array $curriculumNodes): self
    {
        return $this->set('curriculumnodes', $curriculumNodes);
    }

    public function setPublishedParent(?Record $record): self
    {
        return $this->set('published_parent_id', $record?->id);
    }

    public function setTeacherGoals(?string $teacherGoals): self
    {
        return $this->set('teacher_goals', $teacherGoals);
    }

    #[Override]
    public function definition(): array
    {
        return [
            'uid' => uuid(),
            'name' => $this->faker->name(),
            'order' => $this->faker->numberBetween(0, 100),
            'is_template' => false,
            'is_duration_applicable' => false,
            'purpose' => 1,
        ];
    }

    public function createMultiple(int $times): Collection
    {
        return $this
            ->count($times)
            ->sequence(fn(Sequence $sequence): array => ['order' => $sequence->index])
            ->create();
    }
}
