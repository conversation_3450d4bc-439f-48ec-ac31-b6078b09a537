<?php

namespace Cfa\Planner\Domain\CollectionV2\Chapter\Record\Material;

use App\Services\Security\SanitizeStringService;
use Cfa\Planner\Domain\Record\Material\MaterialTarget;
use Illuminate\Contracts\Support\Arrayable;
use JsonSerializable;
use Override;

use function app;

abstract class AbstractMaterial implements Arrayable, JsonSerializable
{
    public const DESCRIPTION = 'description';
    public const TARGET = 'target';

    protected ?string $description;

    protected ?MaterialTarget $target = null;

    public function __construct(?string $description, MaterialTarget|string|null $target)
    {
        $this->description = app()->make(SanitizeStringService::class)->sanitize($description);

        if ($target instanceof MaterialTarget) {
            $this->target = $target;
        }

        if (is_string($target)) {
            $this->target = MaterialTarget::tryFromName($target);
        }
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getTarget(): ?MaterialTarget
    {
        return $this->target;
    }

    abstract public function getMaterialType(): string;

    #[Override]
    public function toArray(): array
    {
        return [
            self::DESCRIPTION => $this->description,
            self::TARGET => isset($this->target) ? $this->target->name : null,
        ];
    }

    abstract public static function fromArray(array $attributes): self;

    #[Override]
    public function jsonSerialize(): array
    {
        return $this->toArray();
    }
}
