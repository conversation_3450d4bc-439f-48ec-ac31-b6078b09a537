<?php

namespace Cfa\Planner\Domain\CollectionV2\Chapter\Record;

use App\Casts\CurriculumNodes;
use App\Casts\EncodedString;
use App\Casts\ExperienceSituationTypes;
use App\Casts\MaterialFiles;
use App\Casts\MaterialLinks;
use App\Casts\MaterialPhysicals;
use App\Casts\RichTextField;
use App\Casts\SanitizedUrl;
use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\DisableLazyLoading;
use Cfa\Common\Application\Traits\DisableQueryingOnModel;
use Cfa\Common\Application\Traits\PruneSoftDeletes;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Planner\Application\Traits\CollectionV2\HasOverwrites;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\Chapter\Chapter;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Material\File;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Material\Link;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Material\Physical;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\Record\RecordExperienceSituationType\ExperienceSituationType;
use Cfa\Planner\Domain\Record\RecordPurpose;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Illuminate\Validation\Rule;
use Override;

use function array_merge;

/**
 * Cfa\Planner\Domain\CollectionV2\Chapter\Record\Record
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property string|null $publisher_record_uid Used to store data needed for mapping after import.
 * @property int|null $chapter_id
 * @property int|null $published_parent_id
 * @property int|null $previous_record_id Points to the previous version of this record.
 * @property int|null $planner_collection_id
 * @property int|null $activated_planner_collection_id
 * @property bool $is_duration_applicable If true, the user can change duration_minutes.
 * @property int|null $duration_minutes
 * @property string $name
 * @property int $order
 * @property string|null $manual
 * @property RecordPurpose $purpose
 * @property string|null $start_situation
 * @property string|null $teacher_goals
 * @property string|null $differentiation
 * @property string|null $lesson_steps
 * @property string|null $tips
 * @property string|null $work_methods
 * @property bool|null $zill
 * @property bool $is_template
 * @property string|null $boardbook_url
 * @property string|null $boardlesson_url
 * @property string|null $instruction_movie_url_1
 * @property string|null $instruction_movie_url_2
 * @property string|null $instruction_movie_url_3
 * @property array|null $curriculumnodes
 * @property File[]|Collection|null $material_files
 * @property Physical[]|Collection|null $material_physicals
 * @property Link[]|Collection|null $material_links
 * @property ExperienceSituationType[]|Collection|null $experience_situation_types
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property-read Chapter|null $chapter
 * @property-read array $validation_rules
 * @property-read Collection<int,RecordOverwrite> $overwrites
 * @property-read int|null $overwrites_count
 * @method static RecordRepositoryInterface getRepository()
 * @method static Builder|Record newModelQuery()
 * @method static Builder|Record newQuery()
 * @method static Builder|Record onlyTrashed()
 * @method static Builder|Record query()
 * @method static int|null randomActivatedPlannerCollectionId()
 * @method static string|null randomBoardbookUrl()
 * @method static int|null randomChapterId()
 * @method static Carbon|null randomCreatedAt()
 * @method static array|null randomCurriculumnodes()
 * @method static Carbon|null randomDeletedAt()
 * @method static string|null randomDifferentiation()
 * @method static int|null randomDurationMinutes()
 * @method static ExperienceSituationType[]|Collection|null randomExperienceSituationTypes()
 * @method static int randomId()
 * @method static string|null randomInstructionMovieUrl1()
 * @method static string|null randomInstructionMovieUrl2()
 * @method static string|null randomInstructionMovieUrl3()
 * @method static bool randomIsDurationApplicable()
 * @method static bool randomIsTemplate()
 * @method static string|null randomLessonSteps()
 * @method static string|null randomManual()
 * @method static File[]|Collection|null randomMaterialFiles()
 * @method static Link[]|Collection|null randomMaterialLinks()
 * @method static Physical[]|Collection|null randomMaterialPhysicals()
 * @method static string randomName()
 * @method static int randomOrder()
 * @method static int|null randomPlannerCollectionId()
 * @method static int|null randomPreviousRecordId()
 * @method static string|null randomPublisherRecordUid()
 * @method static RecordPurpose randomPurpose()
 * @method static string|null randomStartSituation()
 * @method static string|null randomTeacherGoals()
 * @method static string|null randomTips()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static string|null randomWorkMethods()
 * @method static bool|null randomZill()
 * @method static Builder|Record whereActivatedPlannerCollectionId($value)
 * @method static Builder|Record whereBoardbookUrl($value)
 * @method static Builder|Record whereChapterId($value)
 * @method static Builder|Record whereCreatedAt($value)
 * @method static Builder|Record whereCurriculumnodes($value)
 * @method static Builder|Record whereDeletedAt($value)
 * @method static Builder|Record whereDifferentiation($value)
 * @method static Builder|Record whereDurationMinutes($value)
 * @method static Builder|Record whereExperienceSituationTypes($value)
 * @method static Builder|Record whereId($value)
 * @method static Builder|Record whereInstructionMovieUrl1($value)
 * @method static Builder|Record whereInstructionMovieUrl2($value)
 * @method static Builder|Record whereInstructionMovieUrl3($value)
 * @method static Builder|Record whereIsDurationApplicable($value)
 * @method static Builder|Record whereIsTemplate($value)
 * @method static Builder|Record whereLessonSteps($value)
 * @method static Builder|Record whereManual($value)
 * @method static Builder|Record whereMaterialFiles($value)
 * @method static Builder|Record whereMaterialLinks($value)
 * @method static Builder|Record whereMaterialPhysicals($value)
 * @method static Builder|Record whereName($value)
 * @method static Builder|Record whereOrder($value)
 * @method static Builder|Record wherePlannerCollectionId($value)
 * @method static Builder|Record wherePreviousRecordId($value)
 * @method static Builder|Record wherePublisherRecordUid($value)
 * @method static Builder|Record wherePurpose($value)
 * @method static Builder|Record whereStartSituation($value)
 * @method static Builder|Record whereTeacherGoals($value)
 * @method static Builder|Record whereTips($value)
 * @method static Builder|Record whereUid($value)
 * @method static Builder|Record whereUpdatedAt($value)
 * @method static Builder|Record whereWorkMethods($value)
 * @method static Builder|Record whereZill($value)
 * @method static Builder|Record withTrashed()
 * @method static Builder|Record withoutTrashed()
 * @method static RecordFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 *
 * @SuppressWarnings(PHPMD.TooManyPublicMethods)
 * @SuppressWarnings(PHPMD.ExcessiveClassComplexity)
 */
class Record extends Model
{
    use HasOverwrites;
    use PruneSoftDeletes;
    use DisableQueryingOnModel;
    use DisableLazyLoading;
    use Uid;
    use SoftDeletes;

    /** @var string */
    protected $table = 'records_v2';

    private const MAX_RECORD_NAME_SIZE = 145;

    /** @var array */
    protected $fillable = [
        'duration_minutes',
        'is_duration_applicable',
        'name',
        'order',
        'purpose',
        'start_situation',
        'teacher_goals',
        'differentiation',
        'lesson_steps',
        'tips',
        'work_methods',
        'boardbook_url',
        'boardlesson_url',
        'instruction_movie_url_1',
        'instruction_movie_url_2',
        'instruction_movie_url_3',
    ];

    #[Override]
    protected static function boot(): void
    {
        parent::boot();

        static::addDisabledRelation('overwrites');
        static::addDisabledRelation('chapter');
    }

    /** @SuppressWarnings(PHPMD.ExcessiveMethodLength) */
    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'chapter_id' => [
                    'nullable',
                    'integer',
                    Rule::exists('chapters_v2', 'id')->whereNull('deleted_at'),
                ],
                'previous_record_id' => [
                    'nullable',
                    'integer',
                    Rule::exists('records_v2', 'id')->whereNull('deleted_at'),
                ],
                'planner_collection_id' => [
                    'nullable',
                    'integer',
                    'required_without:activated_planner_collection_id',
                    Rule::nullWith('activated_planner_collection_id'),
                    Rule::exists('planner_collections_v2', 'id')->whereNull('deleted_at'),
                ],
                'activated_planner_collection_id' => [
                    'nullable',
                    'integer',
                    Rule::nullWith('planner_collection_id'),
                    'required_without:planner_collection_id',
                    Rule::exists('activated_planner_collections', 'id')->whereNull('deleted_at'),
                ],
                'name' => [
                    'required',
                    'string',
                    'max:255',
                ],
                'is_template' => [
                    'boolean',
                    Rule::unique('records_v2')
                        ->ignore($this->id)
                        ->where('is_template', 1)
                        ->where('chapter_id', $this->chapter_id),
                ],
                'instruction_movie_url_1' => [
                    'nullable',
                    'string',
                    'max:500',
                ],
                'instruction_movie_url_2' => [
                    'nullable',
                    'string',
                    'max:500',
                ],
                'instruction_movie_url_3' => [
                    'nullable',
                    'string',
                    'max:500',
                ],
                'boardbook_url' => [
                    'nullable',
                    'string',
                    'max:500',
                ],
                'boardlesson_url' => [
                    'nullable',
                    'string',
                    'max:500',
                ],
                'curriculumnodes' => [
                    'nullable',
                    'array',
                ],
                'material_files' => [
                    'nullable',
                    'array',
                ],
                'material_physicals' => [
                    'nullable',
                    'array',
                ],
                'material_links' => [
                    'nullable',
                    'array',
                ],
                'experience_situation_types' => [
                    'nullable',
                    'array',
                ],
                'order' => [
                    'nullable',
                    'integer',
                ],
                'duration_minutes' => [
                    'nullable',
                    'integer',
                ],
                'is_duration_applicable' => [
                    'required',
                    'boolean',
                ],
                'purpose' => [
                    'required',
                    'enumValue:' . RecordPurpose::class,
                ],
                'start_situation' => [
                    'nullable',
                    'string',
                    'max:65535',
                ],
                'teacher_goals' => [
                    'nullable',
                    'string',
                    'max:65535',
                ],
                'differentiation' => [
                    'nullable',
                    'string',
                    'max:65535',
                ],
                'lesson_steps' => [
                    'nullable',
                    'string',
                    'max:65535',
                ],
                'tips' => [
                    'nullable',
                    'string',
                    'max:65535',
                ],
                'work_methods' => [
                    'nullable',
                    'string',
                    'max:65535',
                ],
                'deleted_at' => [
                    'nullable',
                    'date',
                ],
                'zill' => [
                    'nullable',
                    'boolean',
                ],
            ],
        );
    }

    protected array $enums = [
        'purpose' => RecordPurpose::class,
    ];

    /** @var array */
    protected $casts = [
        'activated_planner_collection_id' => 'integer',
        'boardbook_url' => SanitizedUrl::class,
        'boardlesson_url' => SanitizedUrl::class,
        'chapter_id' => 'integer',
        'curriculumnodes' => CurriculumNodes::class,
        'differentiation' => RichTextField::class,
        'duration_minutes' => 'integer',
        'experience_situation_types' => ExperienceSituationTypes::class,
        'instruction_movie_url_1' => SanitizedUrl::class,
        'instruction_movie_url_2' => SanitizedUrl::class,
        'instruction_movie_url_3' => SanitizedUrl::class,
        'is_duration_applicable' => 'boolean',
        'is_template' => 'boolean',
        'lesson_steps' => RichTextField::class,
        'material_files' => MaterialFiles::class,
        'material_links' => MaterialLinks::class,
        'material_physicals' => MaterialPhysicals::class,
        'name' => EncodedString::class,
        'order' => 'integer',
        'parent_record_id' => 'integer',
        'planner_collection_id' => 'integer',
        'start_situation' => RichTextField::class,
        'teacher_goals' => RichTextField::class,
        'tips' => RichTextField::class,
        'work_methods' => RichTextField::class,
        'zill' => 'boolean',
    ];

    #[Override]
    protected function validationData(): array
    {
        $data = parent::validationData();

        if (array_key_exists('experience_situation_types', $data)) {
            $data['experience_situation_types'] = json_decode('experience_situation_types', true);
        }

        if (array_key_exists('material_files', $data)) {
            $data['material_files'] = json_decode('material_files', true);
        }

        if (array_key_exists('material_physicals', $data)) {
            $data['material_physicals'] = json_decode('material_physicals', true);
        }

        if (array_key_exists('material_links', $data)) {
            $data['material_links'] = json_decode('material_links', true);
        }

        if (array_key_exists('curriculumnodes', $data)) {
            $data['curriculumnodes'] = json_decode('curriculumnodes', true);
        }

        return $data;
    }

    public function plannerCollection(): BelongsTo
    {
        return $this->belongsTo(PlannerCollection::class, 'planner_collection_id');
    }

    public function activatedPlannerCollection(): BelongsTo
    {
        return $this->belongsTo(ActivatedPlannerCollection::class, 'activated_planner_collection_id');
    }

    public function chapter(): BelongsTo
    {
        return $this->belongsTo(Chapter::class, 'chapter_id');
    }

    public function overwrites(): HasMany
    {
        return $this->hasMany(RecordOverwrite::class, 'record_id');
    }
}
