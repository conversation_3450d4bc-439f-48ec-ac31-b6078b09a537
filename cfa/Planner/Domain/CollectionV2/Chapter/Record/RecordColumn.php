<?php

namespace Cfa\Planner\Domain\CollectionV2\Chapter\Record;

class RecordColumn
{
    public const UID = 'uid';
    public const DURATION_MINUTES = 'duration_minutes';
    public const IS_DURATION_APPLICABLE = 'is_duration_applicable';
    public const NAME = 'name';
    public const CHAPTER_ID = 'chapter_id';
    public const ORDER = 'order';
    public const PURPOSE = 'purpose';
    public const START_SITUATION = 'start_situation';
    public const TEACHER_GOALS = 'teacher_goals';
    public const DIFFERENTIATION = 'differentiation';
    public const LESSON_STEPS = 'lesson_steps';
    public const TIPS = 'tips';
    public const WORK_METHODS = 'work_methods';
    public const BOARDBOOK_URL = 'boardbook_url';
    public const BOARDLESSON_URL = 'boardlesson_url';
    public const INSTRUCTION_MOVIE_URL_1 = 'instruction_movie_url_1';
    public const INSTRUCTION_MOVIE_URL_2 = 'instruction_movie_url_2';
    public const INSTRUCTION_MOVIE_URL_3 = 'instruction_movie_url_3';
    public const MANUAL = 'manual';
    public const CURRICULUM_NODES = 'curriculumnodes';
    public const MATERIAL_FILES = 'material_files';
    public const MATERIAL_PHYSICALS = 'material_physicals';
    public const MATERIAL_LINKS = 'material_links';
    public const EXPERIENCE_SITUATION_TYPES = 'experience_situation_types';
    public const DELETED_AT = 'deleted_at';
    public const IS_TEMPLATE = 'is_template';
    public const ZILL = 'zill';
}
