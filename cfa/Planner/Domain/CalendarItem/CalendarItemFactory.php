<?php

namespace Cfa\Planner\Domain\CalendarItem;

use App\Factories\Factory;
use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\Settings\Activity\ActivityType;
use Illuminate\Support\Collection;
use Override;

/**
 * CalendarItemFactory
 *
 * @codingStandardsIgnoreStart
 * @method Collection|CalendarItem[]|CalendarItem create($attributes = [], Model|null $parent = null)
 * @method Collection|CalendarItem[]|CalendarItem createWithEvents(array $attributes = [], Model|null $parent = null)
 * @codingStandardsIgnoreEnd
 */
class CalendarItemFactory extends Factory
{
    /** @var string */
    protected $model = CalendarItem::class;

    public function forUserItem(): self
    {
        return $this->state(fn(): array => [
            'school_id' => School::randomId(),
            'owner_id' => User::randomId(),
        ]);
    }

    public function forSchoolItem(): self
    {
        return $this->state(fn(): array => [
            'school_id' => School::randomId(),
            'owner_id' => null,
        ]);
    }

    public function forSystemItem(): self
    {
        return $this->state(fn(): array => [
            'school_id' => null,
            'owner_id' => null,
        ]);
    }

    public function setItemWithEnd(): self
    {
        return $this->state(fn(): array => [
            'end' => $this->faker->dateTimeBetween('now', '+1 year'),
            'duration' => null,
        ]);
    }

    public function setItemWithDuration(): self
    {
        return $this->state(fn(): array => [
            'end' => null,
            'duration' => $this->faker->numberBetween(25, 75),
        ]);
    }

    public function setIsFirstPartOfSchoolYear(): self
    {
        $schoolyearRepository = Schoolyear::getRepository();

        return $this->state(fn(): array => [
            'start' => $this->faker->dateTimeBetween($schoolyearRepository->getCurrent()->start, Carbon::now()),
        ]);
    }

    public function setCreator(User $user): self
    {
        return $this->state(fn(): array => ['creator_id' => $user->id]);
    }

    public function setSubjectId(int $subject): self
    {
        return $this->state(fn(): array => ['subject_id' => $subject]);
    }

    public function setStartDate(?Carbon $startDate): self
    {
        return $this->state(fn(): array => ['start' => $startDate]);
    }

    public function setEndDate(?Carbon $endDate): self
    {
        return $this->state(fn(): array => ['end' => $endDate]);
    }

    public function forOwner(?User $owner): self
    {
        return $this->state(fn(): array => ['owner_id' => $owner?->id]);
    }

    public function forSchool(?School $school): self
    {
        return $this->state(fn(): array => ['school_id' => $school?->id]);
    }

    public function forType(CalendarItemType $calendarItemType): self
    {
        return $this->state(fn(): array => ['type' => $calendarItemType]);
    }

    public function setUpdatedAt(Carbon $updatedAt): self
    {
        return $this->state(fn(): array => ['updated_at' => $updatedAt]);
    }

    public function setDuration(?int $duration): self
    {
        return $this->state(fn(): array => ['duration' => $duration]);
    }

    public function setRepetitionPeriod(?Period $period): self
    {
        return $this->state(fn(): array => ['repetition_period' => $period]);
    }

    public function setMasterItem(?CalendarItem $calendarItem): self
    {
        return $this->state(fn(): array => ['master_item_id' => $calendarItem?->id]);
    }

    /** @SuppressWarnings(PHPMD.CyclomaticComplexity) */
    #[Override]
    public function definition(): array
    {
        $type = $this->faker->randomElement(CalendarItemType::cases());
        $isCourse = CalendarItemType::Course === $type;
        $isOneDayItem = $isCourse ? true : $this->faker->boolean();
        $start = Carbon::instance($this->faker->dateTimeBetween('-1 year', 'now'));
        $startEndOfDay = $start->copy()->endOfDay();
        $maxDuration = $startEndOfDay->diffInMinutes($start, absolute: true);
        $end = $isOneDayItem ? null : $this->faker->dateTimeBetween('now', '+1 year');
        $duration = $isOneDayItem ? $this->faker->numberBetween(5, $maxDuration) : null;

        $isSystemItem = $this->faker->boolean(10);
        $isSchoolItem = $isSystemItem ? false : $this->faker->boolean(25);

        return [
            'uid' => uuid(),
            'subject_id' => $isCourse ? Subject::randomId() : null,
            'school_id' => $isSystemItem ? null : School::randomId(),
            'owner_id' => $isSystemItem || $isSchoolItem ? null : User::randomId(),
            'title' => $this->faker->optional(0.8)->sentence,
            'type' => $type,
            'start' => $start,
            'end' => $end,
            'duration' => $duration,
            'location' => $this->faker->sentence(),
            'notes' => $this->faker->text(),
            'activity_type' => $isCourse ? null : $this->faker->randomElement(ActivityType::cases()),
        ];
    }
}
