<?php

namespace Cfa\Care\Application\ViewComposers;

use Cfa\Care\Domain\CareInput\CareType\CareType;
use Cfa\Common\Application\Exceptions\NoActiveSchoolsException;
use Illuminate\View\View;
use Route;

class AddNoteComposer
{
    /**
     * Provide the options to select a care type when adding a note.
     *
     * @param View $view The view to provide with the user and apps.
     *
     * @throws NoActiveSchoolsException The current user has no active schools.
     */
    public function compose(View $view): void
    {
        $careTypes = CareType::getRepository()->getAllBySchoolWithoutArchived(school());

        $pupil = optional(Route::getCurrentRoute())->parameter('pupil');

        $careTypeOptions = $careTypes->map(function (CareType $careType) use ($pupil) {
            $parameters = array_merge(
                ['careType' => $careType->uid],
                isset($pupil) ? ['pupil' => $pupil->uid] : [],
            );

            return [
                'title' => $careType->name,
                'href' => route('web.care.overview.notes.create', $parameters),
            ];
        });

        $view->with('careTypeOptions', $careTypeOptions);
    }
}
