<?php

namespace Cfa\Care\Application\Services\Statistics\Graphs\Pupil;

use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score\PredefinedFollowUpSystemScore;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

use function collect;
use function trans;

abstract class PredefinedFollowUpSystemTable
{
    protected bool $onlyTotals = false;

    private bool $hasDutchAsHomeLanguage = false;

    abstract protected function getGroupMapping(Collection $followUpSystems): array;

    abstract protected function getTitle(Collection $followUpSystems): string;

    abstract protected function processFollowUpSystems(
        Collection $followUpSystems,
        callable $processFollowUpSystemChunk,
    ): Collection;

    public function getDataForPupil(Pupil $pupil, School $school, ?FollowUpSystemType $followUpSystemType = null): array
    {
        if ($followUpSystemType === null) {
            $this->onlyTotals = true;
        }

        $this->hasDutchAsHomeLanguage = $pupil->careInfos
            ->where('school_id', $school->id)
            ->first()
            ->has_dutch_as_home_language;

        $followUpSystems = $this->getFollowUpSystems($school, $followUpSystemType);

        if ($followUpSystems->isEmpty()) {
            return [];
        }

        return $this
            ->processFollowUpSystems(
                $followUpSystems,
                fn(Collection $followUpSystems): array => $this->getDataForFollowUpSystems($followUpSystems, $pupil),
            )
            ->filter()
            ->values()
            ->all();
    }

    protected function getFollowUpSystems(School $school, ?FollowUpSystemType $followUpSystemType): Collection
    {
        return FollowUpSystem::query()
            ->where('school_id', $school->id)
            ->when(
                $followUpSystemType !== null,
                fn(Builder $builder): Builder => $builder->where('type', $followUpSystemType),
            )
            ->when(
                $followUpSystemType === null,
                fn(Builder $builder): Builder => $builder->whereNotNull('type'),
            )
            ->get();
    }

    protected function getDataForFollowUpSystems(Collection $followUpSystems, Pupil $pupil): array
    {
        $result = [];
        [$vclbFollowUpSystems, $nonVclbFollowUpSystems] = $followUpSystems->partition(
            fn(FollowUpSystem $followUpSystem): bool => $followUpSystem->type->isLvsVclb(),
        );

        if ($vclbFollowUpSystems->isNotEmpty()) {
            $groupMapping = $this->getGroupMapping($vclbFollowUpSystems);
            $headers = $this->getHeaders(
                collect($groupMapping)->values()->unique()->values(),
                FollowUpSystemType::LvsVclbMaths,
            );
            $headerGroups = $this->getHeaderGroups($headers);
            $vclbItems = $this->getItems($vclbFollowUpSystems, $pupil, $groupMapping);

            if ($vclbItems->isNotEmpty()) {
                $result[] = [
                    'title' => $this->getTitle($vclbFollowUpSystems),
                    'headerGroups' => $headerGroups,
                    'headers' => $headers,
                    'items' => $vclbItems,
                ];
            }
        }

        $nonVclbTables = $nonVclbFollowUpSystems
            ->map(
                function (FollowUpSystem $followUpSystem) use ($pupil): ?array {
                    $followUpSystems = collect([$followUpSystem]);
                    $groupMapping = $this->getGroupMapping(collect([$followUpSystem]));
                    $groupMapping = empty($groupMapping)
                        ? [$followUpSystem->type->value => $followUpSystem->type->getHumanReadableName()]
                        : $groupMapping;
                    $headers = $this->getHeaders(collect($groupMapping)->values()->unique(), $followUpSystem->type);
                    $headerGroups = $this->getHeaderGroups($headers);
                    $items = $this->getItems($followUpSystems, $pupil, $groupMapping);

                    if ($items->isNotEmpty()) {
                        return [
                            'title' => $followUpSystem->type->getHumanReadableName(),
                            'headerGroups' => count($headerGroups) === 1 ? collect() : $headerGroups,
                            'headers' => $headers,
                            'items' => $items,
                            'secondaryHeaderGroups' => collect(),
                        ];
                    }

                    return null;
                },
            )->filter()->all();

        return [...$result, ...$nonVclbTables];
    }

    private function getHeaderGroups(Collection $headers): Collection
    {
        return $headers->slice(1)
            ->groupBy('headerGroup')
            ->map(fn(Collection $headers): array => [
                'name' => $headers->first()['headerGroup'],
                'count' => $headers->count(),
            ])
            ->values();
    }

    private function getScoresForFollowUpSystems(
        Collection $followUpSystems,
        Pupil $pupil,
    ): Collection {
        return $this->getBaseScoresBuilder('predefined_follow_up_system_scores', $followUpSystems, $pupil)
            ->addSelect(['predefined_follow_up_system_scores.*'])
            ->get();
    }

    private function getBaseScoresBuilder(
        string $table,
        Collection $followUpSystems,
        Pupil $pupil,
    ): Builder {
        return PredefinedFollowUpSystemScore::query()
            ->select([
                $table . '.repeating_year',
                $table . '.test_audience',
                $table . '.test_moment',
                $table . '.subtype',
                $table . '.predefined_follow_up_system_id',
                $table . '.schoolyear_id',
            ])
            ->addSelect(
                DB::raw(
                    'CASE ' . $table . '.test_moment ' .
                    'WHEN "' . TestMoment::Begin->value . '" THEN 1 ' .
                    'WHEN "' . TestMoment::Middle->value . '" THEN 2 ' .
                    'WHEN "' . TestMoment::End->value . '" THEN 3 ' .
                    'END AS test_moment_sort',
                ),
            )
            ->with(['predefinedFollowUpSystem'])
            ->whereIn(
                $table . '.predefined_follow_up_system_id',
                $followUpSystems->pluck('id'),
            )
            ->when(
                $this->onlyTotals,
                fn(Builder $builder): Builder => $builder->where(
                    $table . '.subtype',
                    FollowUpSystemSubType::Default,
                ),
            )
            ->where($table . '.pupil_id', $pupil->id)
            ->orderBy($table . '.schoolyear_id')
            ->orderByRaw(
                'CASE ' . $table . '.test_moment ' .
                'WHEN "' . TestMoment::Begin->value . '" THEN 1 ' .
                'WHEN "' . TestMoment::Middle->value . '" THEN 2 ' .
                'WHEN "' . TestMoment::End->value . '" THEN 3 ' .
                'END',
            );
    }

    private function getCommentsForFollowUpSystems(
        Collection $followUpSystems,
        Pupil $pupil,
    ): Collection {
        return $this->getBaseScoresBuilder('predefined_follow_up_system_comments', $followUpSystems, $pupil)
            ->addSelect(['predefined_follow_up_system_comments.comment'])
            ->rightJoin(
                'predefined_follow_up_system_comments',
                fn(JoinClause $joinClause): JoinClause => $joinClause
                    ->whereNull(
                        'predefined_follow_up_system_comments.deleted_at',
                    )
                    ->whereColumn([
                        [
                            'predefined_follow_up_system_scores.predefined_follow_up_system_id',
                            '=',
                            'predefined_follow_up_system_comments.predefined_follow_up_system_id',
                        ],
                        [
                            'predefined_follow_up_system_scores.test_audience',
                            '=',
                            'predefined_follow_up_system_comments.test_audience',
                        ],
                        [
                            'predefined_follow_up_system_scores.pupil_id',
                            '=',
                            'predefined_follow_up_system_comments.pupil_id',
                        ],
                        [
                            'predefined_follow_up_system_scores.schoolyear_id',
                            '=',
                            'predefined_follow_up_system_comments.schoolyear_id',
                        ],
                        [
                            'predefined_follow_up_system_scores.test_moment',
                            '=',
                            'predefined_follow_up_system_comments.test_moment',
                        ],
                        [
                            'predefined_follow_up_system_scores.subtype',
                            '=',
                            'predefined_follow_up_system_comments.subtype',
                        ],
                    ]),
            )
            ->get();
    }

    private function getHeaders(Collection $headerGroups, FollowUpSystemType $type): Collection
    {
        $headerValues = collect(['score', 'percentile', 'zone']);
        $firstElement = collect([['title' => '', 'key' => 'moment', 'indexInGroup' => -1]]);
        if ($headerGroups->count() === 1) {
            if ($type->isAvi()) {
                $headerValues = collect(['zone']);
            }
            $headerValues->push('comment');

            return $firstElement
                ->merge(
                    $headerValues->map(fn($header, int $index): array => [
                        'headerGroup' => $headerGroups->first(),
                        'indexInGroup' => $index,
                        'title' => $this->getHeaderTitle($type, $header),
                        'key' => $header . '_' . $headerGroups->first(),
                        'tooltip' => $this->getHeaderTooltip($type, $header),
                    ]),
                );
        }

        if ($this->hasDutchAsHomeLanguage === false) {
            $headerValues->push('percentile_nn', 'zone_nn');
        }

        $headerValues->push('comment');

        $headerNames = $headerValues
            ->flatMap(fn(string $value): array => [
                [
                    'title' => $this->getHeaderTitle($type, $value, '_short'),
                    'key' => $value,
                    'tooltip' => $this->getHeaderTooltip($type, $value),
                ],
            ]);

        return $firstElement
            ->merge(
                $headerGroups->flatMap(
                    fn($headerGroup): Collection => $headerNames->map(fn($header, int $index): array => [
                        'headerGroup' => $headerGroup,
                        'indexInGroup' => $index,
                        'title' => $header['title'],
                        'key' => $header['key'] . '_' . $headerGroup,
                        'tooltip' => $header['tooltip'],
                    ]),
                ),
            );
    }

    private function getHeaderTitle(FollowUpSystemType $type, string $header, string $short = ''): string
    {
        if ($type === FollowUpSystemType::Koala && $header === 'percentile') {
            return trans('labels.follow-up-systems.predefined.listening_score_short');
        }
        if ($type === FollowUpSystemType::Koala && $header === 'percentile_nn') {
            return trans('labels.follow-up-systems.predefined.listening_score_nn_short');
        }

        return trans('labels.follow-up-systems.predefined.' . $header . $short);
    }

    private function getHeaderTooltip(FollowUpSystemType $type, string $header): string
    {
        if ($type === FollowUpSystemType::Koala && $header === 'percentile') {
            return trans('labels.follow-up-systems.predefined.listening_score');
        }
        if ($type === FollowUpSystemType::Koala && $header === 'percentile_nn') {
            return trans('labels.follow-up-systems.predefined.listening_score_nn');
        }

        return trans('labels.follow-up-systems.predefined.' . $header);
    }

    private function getItems(Collection $followUpSystems, Pupil $pupil, array $groupMapping): Collection
    {
        $followUpSystemsById = $followUpSystems->keyBy('id');

        return $this->getScoresForFollowUpSystems($followUpSystems, $pupil)
            ->concat($this->getCommentsForFollowUpSystems($followUpSystems, $pupil)->all())
            ->groupBy(
                fn(PredefinedFollowUpSystemScore $score): string => $score->test_audience->getHumanReadableName()
                    . ' '
                    . $score->test_moment->getHumanReadableName(),
            )
            ->sortBy(
                fn(Collection $scores): string => implode(
                    '#',
                    [$scores->first()->schoolyear_id, $scores->first()->test_moment_sort],
                ),
            )
            ->flatMap(fn(Collection $scores, string $testMoment): Collection => $scores
                ->sortBy('created_at')
                ->map(fn(PredefinedFollowUpSystemScore $score): array => $this
                    ->getItemForScore(
                        $score,
                        $followUpSystemsById,
                        $groupMapping,
                        $testMoment,
                    )))
            ->sortBy('schoolYear')
            ->groupBy(
                fn(array $item): string => $item['schoolYear'] . ' ' . $item['moment'],
            )
            ->map
            ->mapWithKeys(fn(array $item): array => $item)
            ->values();
    }

    private function getItemForScore(
        PredefinedFollowUpSystemScore $score,
        Collection $followUpSystemsById,
        array $groupMapping,
        string $testMoment,
    ): array {
        $type = $followUpSystemsById[$score->predefined_follow_up_system_id]->type;
        $group = $this->onlyTotals ? $groupMapping[$type->value] : $groupMapping[$score->subtype->value];

        $zoneName = config(
            'follow-up-systems.' .
            $score->predefinedFollowUpSystem->type->value
            . '.general.zoneName',
        );

        $results = [];
        if (!is_null($score->zone)) {
            $results = [
                ...$results,
                ...[
                    'zone_' . $group => trans('enums.' . $zoneName . '.' . $score->zone),
                ],
            ];
        }

        if (!is_null($score->percentile) && !is_null($score->score)) {
            $results = [
                ...$results,
                ...[
                    'score_' . $group => $score->score,
                    'percentile_' . $group => $score->percentile,
                ],
            ];
        }

        if (!is_null($score->comment)) {
            $results = [
                ...$results,
                ...[
                    'comment_' . $group => $score->comment,
                ],
            ];
        }

        if ($this->hasDutchAsHomeLanguage === false) {
            $results['percentile_nn_' . $group] = $score->percentile_nn;
            $results['zone_nn_' . $group] = $score->zone_nn;
        }

        return [
            ...$results,
            'moment' => $testMoment . ($score->repeating_year > 0 ? '**' : ''),
            'schoolYear' => $score->schoolyear->start->year,
        ];
    }
}
