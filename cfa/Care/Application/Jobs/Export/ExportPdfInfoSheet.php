<?php

namespace Cfa\Care\Application\Jobs\Export;

use App\Jobs\Export;
use App\Services\Browsershot\Browsershot;
use App\Services\Lambda\Domain\HasZipPath;
use App\Services\Lambda\Domain\ZipPath;
use Cfa\Care\Domain\CareInfo\CareInfo;
use Override;
use Throwable;

use function route;
use function trans;
use function uuid;

class ExportPdfInfoSheet extends Export implements HasZipPath
{
    protected CareInfo $careInfo;

    public function __construct(CareInfo $careInfo)
    {
        parent::__construct('exports/' . trans('labels.export-filenames.info-sheet') . '-' . uuid() . '.pdf');
        $this->careInfo = $careInfo;
    }

    /**
     * Create a PDF export for the info sheet and set the filename.
     *
     * @throws Throwable Thrown when rendering fails.
     */
    #[Override]
    protected function generateExport(): void
    {
        $url = route('api.cluster-internal.care-infos.print', ['careInfo' => $this->careInfo->uid]);

        $fileContents = Browsershot::url($url)
            ->showBrowserHeaderAndFooter()
            ->showBackground()
            ->hideHeader()
            ->timeout(60 * 5)
            ->footerHtml(view('print.pagination')->render())
            ->pdf();
        $this->storeFileContents($fileContents);
    }

    #[Override]
    public function getZipPath(): ZipPath
    {
        return new ZipPath($this->getFilePath(), trans('labels.export-filenames.info-sheet') . '.pdf');
    }
}
