<?php

namespace Cfa\Care\Application\Repositories;

use App\Models\Model;
use App\Repositories\Repository;
use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\CareInfo\Redicodi\Redicodi;
use Cfa\Care\Domain\CareInfo\Redicodi\RedicodiRepositoryInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;
use Override;

class RedicodiRepository extends Repository implements RedicodiRepositoryInterface
{
    #[Override]
    public function getModel(): Model
    {
        return new Redicodi();
    }

    #[Override]
    public function getTouchedRedicodiUids(Collection $redicodis): array
    {
        $flatMapUids = [];

        $redicodis->each(function (Redicodi $redicodi) use (&$flatMapUids): void {
            $this->processRedicodiFlatMap($redicodi, $flatMapUids);
        });

        return array_values(array_unique($flatMapUids));
    }

    #[Override]
    public function getRedicodisForCareInfo(CareInfo $careInfo, int $schoolId): Collection
    {
        $uidsInUse = $this->getTouchedRedicodiUids($careInfo->redicodis);

        return Redicodi::whereSchoolId($schoolId)
            ->whereNull('parent_redicodi_id')
            ->where(function (Builder $query) use ($uidsInUse): void {
                $query->whereIn('redicodis.uid', $uidsInUse)
                    ->orWhereNull('archived_at');
            })
            ->with([
                'children.children.children' => fn($query): HasMany =>
                    $this->childrenQuery($query, $uidsInUse),
            ])
            ->with([
                'children.children' => fn($query): HasMany => $this->childrenQuery($query, $uidsInUse),
            ])
            ->with([
                'children' => fn($query): HasMany => $this->childrenQuery($query, $uidsInUse),
            ])
            ->get();
    }

    private function childrenQuery(HasMany $query, array $uidsInUse): HasMany
    {
        return $query->where(
            fn(Builder $subQuery): Builder =>
                $subQuery->whereIn('redicodis.uid', $uidsInUse)->orWhereNull('archived_at'),
        );
    }

    private function processRedicodiFlatMap(Redicodi $redicodi, array &$flatMapUids): array
    {
        // Add yourself.
        $flatMapUids[] = $redicodi->uid;

        if ($redicodi->parent_redicodi_id === null) {
            return $flatMapUids;
        }

        // Process your parent.
        return $this->processRedicodiFlatMap($redicodi->parent, $flatMapUids);
    }
}
