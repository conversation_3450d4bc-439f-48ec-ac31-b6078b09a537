<?php

namespace Cfa\Care\Application\Controllers\Dashboard;

use Cfa\Care\Application\Services\Statistics\Graphs\Group\CareLevelCharts;
use Cfa\Care\Application\Services\Statistics\Graphs\Group\CareThemeCharts;
use Cfa\Care\Application\Services\Statistics\Graphs\Group\CareTypeCharts;
use Cfa\Care\Application\Services\Statistics\Graphs\Group\DiagnosisCharts;
use Cfa\Common\Application\Controllers\Dashboard\AbstractGroupDashboardController;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Override;

use function app;
use function array_filter;

class GroupDashboardCareController extends AbstractGroupDashboardController
{
    #[Override]
    protected function getGraphData(array $groupIds, School $school, User $user): array
    {
        return array_filter(array_merge(
            app(CareThemeCharts::class)->getGraphForGroups($groupIds, $school, $user),
            app(CareTypeCharts::class)->getGraphForGroups($groupIds, $school, $user),
            app(CareLevelCharts::class)->getGraphForGroups($groupIds, $school, $user),
            app(DiagnosisCharts::class)->getGraphForGroups($groupIds, $school, $user),
        ));
    }

    #[Override]
    protected function getTableData(array $groupIds, School $school): array
    {
        return [];
    }

    #[Override]
    protected function getRouteIdentifier(): string
    {
        return 'care';
    }
}
