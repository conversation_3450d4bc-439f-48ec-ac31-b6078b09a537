<?php

declare(strict_types=1);

namespace Cfa\Care\Application\Controllers\Settings\PupilManagement\Photo;

use App\Controllers\Controller;
use App\Models\File\FileUsage;
use App\Services\File\FileUploadService;
use Cfa\Care\Domain\PhotoImportJob\PhotoImportJob;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

final class PhotoStoreController extends Controller
{
    public function __invoke(FileUploadService $fileUploadService, PhotoImportJobRequest $request): JsonResponse
    {
        $school = school();
        $user = $request->user();

        $attachments = $request->get(PhotoImportJobRequest::ATTACHMENTS, [])[0]['file'];

        $attachmentsZip = $fileUploadService->uploadFile(
            $user,
            FileUsage::PhotoImportAttachments,
            urldecode((string) $attachments),
        );

        $photoImportJob = new PhotoImportJob();
        $photoImportJob->attachments = $attachmentsZip;
        $photoImportJob->creator_id = Auth::user()->id;
        $photoImportJob->school()->associate($school);
        $photoImportJob->save();

        return $this->respondJsonRedirectWriteConnection(
            route('web.settings.care.pupil-management.upload-photos.result', $photoImportJob),
        );
    }
}
