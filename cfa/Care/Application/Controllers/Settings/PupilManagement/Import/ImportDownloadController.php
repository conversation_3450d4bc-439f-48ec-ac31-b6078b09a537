<?php

namespace Cfa\Care\Application\Controllers\Settings\PupilManagement\Import;

use Cfa\Common\Application\Exports\InactivePupilExport;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class ImportDownloadController
{
    public function __invoke(): BinaryFileResponse
    {
        return Excel::download(
            new InactivePupilExport(school()),
            trans('labels.inactive-pupils') . '.xlsx',
        )->setPrivate();
    }
}
