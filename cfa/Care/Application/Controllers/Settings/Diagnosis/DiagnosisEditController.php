<?php

namespace Cfa\Care\Application\Controllers\Settings\Diagnosis;

use App\Controllers\Controller;
use Cfa\Care\Domain\CareInfo\Diagnosis\Diagnosis;
use Illuminate\Http\Request;
use Illuminate\View\View;

class DiagnosisEditController extends Controller
{
    public function __invoke(Diagnosis $diagnosis): View
    {
        $title = trans('titles.diagnoses.edit', ['name' => $diagnosis->name]);
        $action = route('web.settings.care.diagnoses.update', ['diagnosis' => $diagnosis->uid]);
        $cancelUrl = route('web.settings.care.diagnoses.index');
        $method = Request::METHOD_PUT;
        $confirmToSaveMessage = null;
        if ($diagnosis->loadCount('careInfos')->has_relations) {
            $confirmToSaveMessage = trans('labels.confirm-save-linked-items');
        }

        return view('settings.pages.form', [
            'school' => school(),
            'model' => $diagnosis,
            'title' => $title,
            'action' => $action,
            'cancelUrl' => $cancelUrl,
            'method' => $method,
            'confirmToSaveMessage' => $confirmToSaveMessage,
        ]);
    }
}
