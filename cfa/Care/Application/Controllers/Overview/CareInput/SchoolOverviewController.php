<?php

namespace Cfa\Care\Application\Controllers\Overview\CareInput;

use App\Controllers\Controller;
use Cfa\Care\Application\Services\Statistics\GraphService;
use Cfa\Common\Application\Handlers\GroupSwitcherDataHandler;
use Cfa\Common\Application\Services\Permission\PermissionService;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

use function app;
use function compact;
use function school;

class SchoolOverviewController extends Controller
{
    public function __construct(private readonly PermissionService $permissionService) {}

    public function __invoke(): View
    {
        app(GroupSwitcherDataHandler::class)
            ->setRouteName('web.care.overview.groups.overview');

        $user = Auth::user();
        $school = school();

        $groups = $this->permissionService->getGroupsWithCareDataAccess($user, $school);

        $optionsArray = app(GraphService::class)
            ->getDashboardDataByGroups($groups->pluck('id')->toArray(), $school, $user);
        $overviewPageRoute = 'web.care.overview.school.overview';

        // We need to initialize these vars as overview.school extends overview.group and it uses these in compact.
        $redicodisGroupChartData = null;
        $drillDownUrl = null;

        return view(
            'care.groups.overview.school',
            compact('optionsArray', 'overviewPageRoute', 'drillDownUrl', 'redicodisGroupChartData', 'groups'),
        );
    }
}
