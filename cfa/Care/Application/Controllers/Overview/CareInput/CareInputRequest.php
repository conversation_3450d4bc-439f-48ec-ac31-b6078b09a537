<?php

namespace Cfa\Care\Application\Controllers\Overview\CareInput;

use App\Constants\DateFormats;
use App\Http\Requests\FormRequest;
use Override;

class CareInputRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'sort_field' => 'in:date',
                'sort_direction' => 'in:asc,desc',
                'careTheme' => 'string',
                'careType' => 'string',
                'start' => 'date_format:' . DateFormats::DATE,
                'end' => 'date_format:' . DateFormats::DATE,
                'description' => 'string',
            ],
        );
    }
}
