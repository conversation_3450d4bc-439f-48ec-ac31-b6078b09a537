<?php

namespace Cfa\Care\Application\Controllers\Overview\CareInfo;

use App\Http\Requests\FormRequest;
use Auth;
use Cfa\Care\Application\Controllers\CareInfo\CareInfoController;
use Cfa\Common\Application\Handlers\GroupSwitcherDataHandler;
use Cfa\Common\Application\Policies\UserPolicy;
use Cfa\Common\Domain\User\Pupil;
use Illuminate\Http\JsonResponse;

use function app;
use function array_merge;
use function response;
use function trans;
use function view;

class CareInfoShowModalController extends CareInfoController
{
    public function __invoke(FormRequest $request, Pupil $pupil, UserPolicy $userPolicy): JsonResponse
    {
        if (!$userPolicy->viewPupil(Auth::user(), $pupil) || !$userPolicy->viewCareData(Auth::user(), $pupil)) {
            return response()->json([
                'editUrl' => '',
                'pupilName' => trans('errors.fallback.care-info-show.title'),
                'html' => view(
                    'common.care-info.no-access',
                    [
                        'title' => trans('errors.fallback.care-info-show.title'),
                        'body' => trans('errors.fallback.care-info-show.body'),
                    ],
                )->render(),
            ]);
        }
        app(GroupSwitcherDataHandler::class)->setRouteName('web.care.overview.groups.overview');

        $editUrl = auth()->user()->can('updatePupilCareInfo', $pupil) ?
            route('web.common.care-info.edit', ['pupil' => $pupil->uid]) : '';

        return response()->json([
            'editUrl' => $editUrl,
            'pupilName' => $pupil->fullname,
            'html' => view(
                'common.care-info.show-content',
                array_merge(
                    $this->getSharedShowViewData($pupil),
                    ['showFilters' => false],
                ),
            )->render(),
        ]);
    }
}
