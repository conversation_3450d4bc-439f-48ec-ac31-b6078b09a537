<?php

namespace Cfa\Care\Application\Middleware;

use App\Models\Session\Flash;
use Cfa\Common\Domain\School\SchoolRepositoryInterface;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class ShowMessageForInactivePupils
{
    private const IMPACTED_DATA_ROUTES = [
        'web.care.overview.school.notes',
        'web.care.overview.groups.notes',
        'web.care.overview.pupils.notes',
    ];

    public function handle(Request $request, Closure $next): mixed
    {
        $routeName = $request->route()->getName();
        $routeMessageShown = Flash::InactivePupilsDataLimitation->value . $routeName;

        if (
            Session::has(Flash::InactivePupils->value) &&
            Session::has($routeMessageShown)
        ) {
            return $next($request);
        }

        $hasInactivePupils = app(SchoolRepositoryInterface::class)->hasInactivePupils(school());

        if ($hasInactivePupils === false) {
            return $next($request);
        }

        if (!Session::has(Flash::InactivePupils->value)) {
            Session::flash('snackbar', Flash::InactivePupils);
            Session::put(Flash::InactivePupils->value, true);

            // We only show one snackbar at a time, next time we visit the page another snackbar can be shown.
            return $next($request);
        }

        if (!in_array($routeName, self::IMPACTED_DATA_ROUTES)) {
            return $next($request);
        }

        if (!Session::has($routeMessageShown)) {
            Session::flash('snackbar', Flash::InactivePupilsDataLimitation);
            Session::put($routeMessageShown, true);
        }

        return $next($request);
    }
}
