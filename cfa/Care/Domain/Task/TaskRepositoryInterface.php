<?php

namespace Cfa\Care\Domain\Task;

use App\Repositories\RepositoryInterface;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Illuminate\Pagination\LengthAwarePaginator;

interface TaskRepositoryInterface extends RepositoryInterface
{
    /**
     * Gets the paginated list of all tasks for a user.
     *
     * @param array $filters The array of possible filter options.
     */
    public function getPaginatedForSchoolAndUser(
        School $school,
        User $taskUser,
        array $filters = [],
        int $pageSize = 20,
    ): LengthAwarePaginator;

    /**
     * Gets the paginated list of all tasks for owner.
     *
     * @param array $filters The array of possible filter options.
     */
    public function getPaginatedForSchoolAndOwner(
        School $school,
        User $taskUser,
        array $filters = [],
        int $pageSize = 20,
    ): LengthAwarePaginator;
}
