<?php

namespace Cfa\Care\Domain\CareInfo;

use App\Factories\Factory;
use Cfa\Care\Domain\CareInfo\Language\Language;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Override;

/**
 * CareInfoFactory
 *
 * @codingStandardsIgnoreStart
 * @method Collection|CareInfo[]|CareInfo create($attributes = [], Model|null $parent = null)
 * @method Collection|CareInfo[]|CareInfo createWithEvents(array $attributes = [], Model|null $parent = null)
 * @codingStandardsIgnoreEnd
 */
class CareInfoFactory extends Factory
{
    /** @var string */
    protected $model = CareInfo::class;

    public function forPupil(User $pupil): self
    {
        return $this->state(fn(): array => [
            'pupil_id' => $pupil->id,
        ]);
    }

    public function inSchool(School $school): self
    {
        return $this->state(fn(): array => [
            'school_id' => $school->id,
        ]);
    }

    public function withStatus(PupilStatus $pupilStatus): self
    {
        return $this->state(fn(): array => [
            'pupil_status' => $pupilStatus,
        ]);
    }

    public function withoutData(): self
    {
        return $this->state(fn(): array => [
            'home_language_id' => null,
            'home_situation' => null,
            'physical_situation' => null,
            'social_emotional' => null,
        ]);
    }

    #[Override]
    public function definition(): array
    {
        return [
            'uid' => uuid(),
            'home_language_id' => Language::randomId(),
            'home_situation' => $this->faker->text(191),
            'physical_situation' => $this->faker->text(191),
            'social_emotional' => $this->faker->word,
            'has_dutch_as_home_language' => true,
        ];
    }
}
