<?php

namespace Cfa\Care\Domain\CareInfo\Redicodi;

use Cfa\Common\Domain\Icon\IconResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

/**
 * @mixin Redicodi
 * @extends JsonResource<Redicodi>
 */
class RedicodiReportResource extends JsonResource
{
    /**
     * {@inheritdoc}
     *
     * @param Request $request The request.
     */
    #[Override]
    public function toArray($request): array
    {
        return [
            'uid' => $this->uid,
            'name' => $this->name,
            'show_on_report' => $this->getAttribute('show_on_report') ?? $this->whenPivotLoaded(
                'redicodi_report_item',
                fn() => $this->getRelation('pivot')->show_on_report,
            ),
            'icon' => $this->icon ? new IconResource($this->icon)->toArray($request) : null,
        ];
    }
}
