<?php

namespace Cfa\Care\Domain\CareInput;

use App\Casts\RichTextField;
use App\Constants\DateFormats;
use App\Models\Model;
use Carbon\Carbon;
use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\CareInput\CareFile\CareFile;
use Cfa\Care\Domain\CareInput\CareGiver\CareGiver;
use Cfa\Care\Domain\CareInput\CareTheme\CareTheme;
use Cfa\Care\Domain\CareInput\CareType\CareType;
use Cfa\Care\Domain\Task\Task;
use Cfa\Common\Application\Traits\PruneSoftDeletes;
use Cfa\Common\Application\Traits\TouchedByUser;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Illuminate\Contracts\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Override;

/**
 * Cfa\Care\Domain\CareInput\CareInput
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property int|null $care_type_id The type of the care input
 * @property Carbon $date Date of the care input
 * @property string $description Description of the care input
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property int|null $creator_id
 * @property int|null $updater_id
 * @property VisibilityType $visibility Visibility setting of the care input.
 * @property string|null $imported_at
 * @property int $school_id
 * @property string|null $creator_name
 * @property-read Collection<int, CareFile> $careFiles
 * @property-read int|null $care_files_count
 * @property-read Collection<int, CareGiver> $careGivers
 * @property-read int|null $care_givers_count
 * @property-read Collection<int, CareInfo> $careInfos
 * @property-read int|null $care_infos_count
 * @property-read Collection<int, CareInfo> $careInfosWithTrashed
 * @property-read int|null $care_infos_with_trashed_count
 * @property-read Collection<int, CareTheme> $careThemes
 * @property-read int|null $care_themes_count
 * @property-read CareType|null $careType
 * @property-read User|null $creator
 * @property-read Collection|array $pupils
 * @property-read array $validation_rules
 * @property-read School $school
 * @property-read Collection<int, Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read User|null $updater
 * @method static CareInputFactory factory($count = null, $state = [])
 * @method static Builder|CareInput filtered(array $filters = [])
 * @method static CareInputRepositoryInterface getRepository()
 * @method static Builder|CareInput newModelQuery()
 * @method static Builder|CareInput newQuery()
 * @method static Builder|CareInput onlyTrashed()
 * @method static Builder|CareInput query()
 * @method static int|null randomCareTypeId()
 * @method static Carbon|null randomCreatedAt()
 * @method static int|null randomCreatorId()
 * @method static string|null randomCreatorName()
 * @method static Carbon randomDate()
 * @method static Carbon|null randomDeletedAt()
 * @method static string randomDescription()
 * @method static int randomId()
 * @method static string|null randomImportedAt()
 * @method static int randomSchoolId()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static int|null randomUpdaterId()
 * @method static VisibilityType randomVisibility()
 * @method static Builder|CareInput visible()
 * @method static Builder|CareInput whereCareTypeId($value)
 * @method static Builder|CareInput whereCreatedAt($value)
 * @method static Builder|CareInput whereCreatorId($value)
 * @method static Builder|CareInput whereCreatorName($value)
 * @method static Builder|CareInput whereDate($value)
 * @method static Builder|CareInput whereDeletedAt($value)
 * @method static Builder|CareInput whereDescription($value)
 * @method static Builder|CareInput whereId($value)
 * @method static Builder|CareInput whereImportedAt($value)
 * @method static Builder|CareInput whereSchoolId($value)
 * @method static Builder|CareInput whereUid($value)
 * @method static Builder|CareInput whereUpdatedAt($value)
 * @method static Builder|CareInput whereUpdaterId($value)
 * @method static Builder|CareInput whereVisibility($value)
 * @method static Builder|CareInput withTrashed()
 * @method static Builder|CareInput withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 * @SuppressWarnings(PHPMD.TooManyPublicMethods)
 */
class CareInput extends Model
{
    use PruneSoftDeletes;
    use Uid;
    use SoftDeletes;
    use TouchedByUser;

    /**
     * Number of days to keep soft deleted care inputs.
     */
    public const DAYS_TO_KEEP_SOFT_DELETES = 365;
    /** @var array */
    protected $fillable = [
        'date',
        'description',
        'visibility',
    ];

    /** @var array */
    protected $casts = [
        'date' => 'datetime',
        'description' => RichTextField::class,
    ];
    /** @var array */
    protected $appends = [
        'pupils',
    ];
    /** @var array */
    protected $visible = [
        'careType',
        'careInfosWithTrashed',
        'careThemes',
        'careFiles',
        'careGivers',
        'creator',
        'visibility',
        'tasks',
    ];

    protected array $enums = [
        'visibility' => VisibilityType::class,
    ];
    /** @var array */
    protected $attributes = [
        'visibility' => VisibilityType::Everyone->value,
    ];

    #[Override]
    protected function bootIfNotBooted(): void
    {
        // Set the default date to today.
        if (!$this->date) {
            $this->attributes['date'] = Carbon::now()->startOfDay();
        }

        parent::bootIfNotBooted();
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'care_type_id' => [
                    'required',
                    'int',
                ],
                'date' => [
                    'required',
                    'date',
                ],
                'visibility' => [
                    'required',
                    'enumValue:' . VisibilityType::class,
                ],
                'description' => [
                    'required',
                    'string',
                    'max:500000',
                ],
            ],
        );
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    protected static function boot(): void
    {
        parent::boot();

        static::addGlobalScope('visibility', static function (Builder $builder): Builder {
            $user = Auth::user();

            return $builder->visible($user ? school() : null, $user);
        });
    }

    /**
     * Get the care type linked to the care input.
     */
    public function careType(): BelongsTo
    {
        return $this->belongsTo(CareType::class);
    }

    /**
     * Get the school linked to the care input.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the care infos linked to the care input.
     */
    public function careInfos(): BelongsToMany
    {
        return $this->belongsToMany(CareInfo::class);
    }

    /**
     * Get the care infos linked to the care input.
     */
    public function careInfosWithTrashed(): BelongsToMany
    {
        return $this
            ->belongsToMany(CareInfo::class, 'care_info_care_input', 'care_input_id', 'care_info_id')
            ->withTrashed();
    }

    /**
     * Get the care themes linked to the care input.
     */
    public function careThemes(): BelongsToMany
    {
        return $this->belongsToMany(CareTheme::class);
    }

    /**
     * Get the care files of the care input.
     */
    public function careFiles(): HasMany
    {
        return $this->hasMany(CareFile::class);
    }

    /**
     * Get the tasks for the care input.
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }

    /**
     * Get the care givers linked with the care input.
     */
    public function careGivers(): HasMany
    {
        return $this->hasMany(CareGiver::class);
    }

    public function scopeVisible(QueryBuilder $query, ?School $school, ?User $user): QueryBuilder
    {
        return $query->where(
            fn(QueryBuilder $builder): QueryBuilder => $builder
                ->where('visibility', VisibilityType::Everyone->value)
                ->orWhere('care_inputs.creator_id', $user?->id)
                ->when(
                    $school && $user?->hasPermission(
                        PermissionName::HasAccessToConfidentialCareInputs,
                        $school,
                    ),
                    fn(QueryBuilder $builder): QueryBuilder => $builder
                        ->orWhere('visibility', VisibilityType::Confidential->value),
                ),
        );
    }

    /**
     * Apply note filters to query.
     *
     * @param Builder $query The query builder.
     * @param array $filters The array of filter parameters.
     */
    public function scopeFiltered(Builder $query, array $filters = []): Builder
    {
        $query->select('care_inputs.*')->distinct();

        if (isset($filters['careTheme'])) {
            $query
                ->join(
                    'care_input_care_theme',
                    'care_input_care_theme.care_input_id',
                    '=',
                    'care_inputs.id',
                )
                ->join(
                    'care_themes',
                    'care_input_care_theme.care_theme_id',
                    '=',
                    'care_themes.id',
                )
                ->where('care_themes.uid', $filters['careTheme']);
        }

        if (isset($filters['careType'])) {
            $query
                ->join('care_types', 'care_types.id', '=', 'care_inputs.care_type_id')
                ->where('care_types.uid', $filters['careType']);
        }

        if (isset($filters['careInfoStatusNotIn'])) {
            $query->whereNotIn('pupil_status', $filters['careInfoStatusNotIn']);
        }

        if (isset($filters['start'])) {
            $date = Carbon::createFromFormat(DateFormats::DATE, $filters['start'])->startOfDay();
            $query->where('care_inputs.date', '>=', $date);
        }

        if (isset($filters['end'])) {
            $date = Carbon::createFromFormat(DateFormats::DATE, $filters['end'])->endOfDay();
            $query->where('care_inputs.date', '<=', $date);
        }

        if (isset($filters['description'])) {
            $query->where(
                'care_inputs.description',
                'like',
                '%' . $filters['description'] . '%',
            );
        }

        $orderByField = $filters['sort_field'] ?? 'date';
        $orderByDirection = $filters['sort_direction'] ?? 'desc';

        $query->orderBy('care_inputs.' . $orderByField, $orderByDirection);

        if ($orderByField === 'date') {
            // Add secondary sort on id for notes on same date.
            $query->orderBy('care_inputs.id', $orderByDirection);
        }

        return $query;
    }

    /**
     * Get a collection of the pupils of the linked care infos.
     */
    public function getPupilsAttribute(): Collection|array
    {
        return $this->careInfosWithTrashed->map->pupilWithTrashed;
    }
}
