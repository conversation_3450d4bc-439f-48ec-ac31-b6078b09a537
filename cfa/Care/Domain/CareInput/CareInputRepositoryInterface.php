<?php

namespace Cfa\Care\Domain\CareInput;

use App\Repositories\RepositoryInterface;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface CareInputRepositoryInterface extends RepositoryInterface
{
    /**
     * Get a paginator for the care inputs in the given school, limited to the given pupil ids and given filters.
     *
     * @param int $schoolId The school for which to get the care inputs.
     * @param Collection $pupilIds The pupils for who to get the care inputs.
     * @param array $filters The array of possible filter options.
     * @param int $pageSize The number of care inputs per page.
     */
    public function getPaginatedCareInputsBySchoolIdAndPupilIds(
        int $schoolId,
        Collection $pupilIds,
        array $filters = [],
        int $pageSize = 20,
    ): LengthAwarePaginator;

    /**
     * Get all the care inputs in the given school, limited to the given pupil ids and given filters.
     *
     * @param int $schoolId The school for which to get the care inputs.
     * @param Collection $pupilIds The pupils for who to get the care inputs.
     * @param array $filters The array of possible filter options.
     */
    public function getAllCareInputsBySchoolIdAndPupilIds(
        int $schoolId,
        Collection $pupilIds,
        array $filters = [],
    ): Collection;
}
