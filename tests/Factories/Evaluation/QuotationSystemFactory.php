<?php

namespace Tests\Factories\Evaluation;

use Cfa\Common\Domain\School\School;
use Cfa\Evaluation\Domain\QuotationSystem\Quotation\Quotation;
use Cfa\Evaluation\Domain\QuotationSystem\QuotationSystem;
use Override;
use Tests\Factories\Factory;

class QuotationSystemFactory extends Factory
{
    /** @var string */
    protected $uid;

    /** @var string */
    protected $name;

    /** @var School */
    protected $school;

    /** @var int */
    protected $quotationsAmount;

    public function setUid(string $uid): self
    {
        $this->uid = $uid;

        return $this;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function withQuotations(int $amount = 1): self
    {
        $this->quotationsAmount = $amount;

        return $this;
    }

    public function setSchool(School $school): self
    {
        $this->school = $school;

        return $this;
    }

    /***/
    #[Override]
    public function create(): QuotationSystem
    {
        if ($this->school === null) {
            $this->school = School::first();
        }

        $quotationSystem = QuotationSystem::factory()
            ->setUid($this->uid ?? $this->faker->uuid())
            ->setSchoolId($this->school->id)
            ->setName($this->name ?? $this->faker->word)
            ->create();

        if ($this->quotationsAmount > 0) {
            Quotation::factory()
                ->count($this->quotationsAmount)
                ->setQuotationSystemId($quotationSystem->id)
                ->create();
        }

        return $quotationSystem->load('quotations');
    }
}
