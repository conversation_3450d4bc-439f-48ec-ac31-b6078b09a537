<?php

namespace Tests\Factories\Planner;

use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionOverwrite;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionOverwriteColumn;
use Override;
use Tests\Factories\Factory;

class PlannerCollectionOverwriteFactory extends Factory
{
    private ?User $user = null;
    private ?PlannerCollection $plannerCollection = null;
    private ?ActivatedPlannerCollection $activatedPlannerCollection = null;
    private ?PlannerCollectionOverwriteColumn $column = null;
    private mixed $value = null;

    public function forUser(User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function forPlannerCollection(PlannerCollection $plannerCollection): self
    {
        $this->plannerCollection = $plannerCollection;

        return $this;
    }

    public function forActivatedPlannerCollection(ActivatedPlannerCollection $activatedPlannerCollection): self
    {
        $this->activatedPlannerCollection = $activatedPlannerCollection;

        return $this;
    }

    public function forColumn(PlannerCollectionOverwriteColumn $column): self
    {
        $this->column = $column;

        return $this;
    }

    public function withValue(mixed $value): self
    {
        $this->value = $value;

        return $this;
    }

    /***/
    #[Override]
    public function create(): PlannerCollectionOverwrite
    {
        PlannerCollection::setQueryingEnabled();
        PlannerCollectionOverwrite::setQueryingEnabled();
        $plannerCollection = factory(PlannerCollectionOverwrite::class)->create([
            'user_id' => optional($this->user)->id,
            'planner_collection_id' => optional($this->plannerCollection)->id,
            'activated_planner_collection_id' => optional($this->activatedPlannerCollection)->id,
            'column' => $this->column->value,
            'value' => $this->value,
        ]);
        PlannerCollectionOverwrite::setQueryingDisabled();
        PlannerCollection::setQueryingDisabled();

        return $plannerCollection;
    }
}
