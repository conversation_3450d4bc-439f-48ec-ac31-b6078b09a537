<?php

namespace Tests\Traits\Settings;

use Illuminate\Http\Request;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;

trait SettingsArchiveTests
{
    /**
     * Setup specific for the archive tests.
     */
    protected function setUpSettingsArchiveTests(): void
    {
        $this->method = Request::METHOD_PATCH;
    }

    /**
     * Test Model is archived correctly.
     *
     * @param string $routeName The name of the route we want to test.
     * @param string $redirectName The name of the route we want to get as redirect response.
     */
    #[DataProvider('routeNameDataProvider')]
    #[Test]
    public function it_archives_the_model(string $routeName, ?string $redirectName): void
    {
        $response = $this->setRouteName($routeName)
            ->setRedirectRouteName($redirectName)
            ->jsonWithRequiredHeaders()
            ->assertResponseOk();

        $model = $this->model->refresh();
        $this->assertTrue($model->archived());

        $response->assertJsonRedirectWithForceWriteConnection($this->getRedirectUrl());
    }
}
