<?php

namespace Tests\Traits;

use Carbon\Carbon;
use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\CareInput\CareInput;
use Cfa\Care\Domain\CareInput\CareTheme\CareTheme;
use Cfa\Care\Domain\CareInput\CareType\CareType;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\SchoolUserAccess\SchoolUserAccess;
use Cfa\Common\Domain\User\User;
use Illuminate\Support\Collection;

trait WithSeeding
{
    /**
     * Authenticated user.
     *
     * @var User
     */
    protected $owner;

    /**
     * The current school.
     *
     * @var School
     */
    protected $school;

    /**
     * The current career.
     *
     * @var Career
     */
    protected $career;

    /**
     * A pupil to test with.
     *
     * @var User
     */
    protected $pupil;

    /**
     * The pupils to test with.
     *
     * @var User[]|Collection
     */
    protected $pupils;

    /**
     * A group to test with.
     *
     * @var Group
     */
    protected $group;

    /**
     * The groups to test with.
     *
     * @var Group[]|Collection
     */
    protected $groups;

    /**
     * The current schoolyear.
     *
     * @var Schoolyear
     */
    protected $schoolyear;

    /**
     * Care infos to test with.
     *
     * @var CareInfo[]|Collection
     */
    protected $careInfos;

    /**
     * Care inputs to test with.
     *
     * @var CareInput[]|Collection
     */
    protected $careInputs;

    /** @var Collection<int, CareType> */
    protected Collection $careTypes;

    /**
     * Care themes to test with.
     *
     * @var CareTheme[]|Collection
     */
    protected $careThemes;

    /**
     * Set up a teacher and a school the teacher belongs to.
     */
    protected function setUpTeacher(): void
    {
        $this->school = School::factory()->inKathOndVla()->create([
            'created_at' => Carbon::create(2015, 8, 15),
        ]);
        $this->owner = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $this->career = $this->owner->career($this->school->id);
    }

    /**
     * Set up groups for the given school in the current schoolyear.
     *
     * @param School $school The school the groups should be created in.
     * @param int $numberOfGroups The number of groups to create.
     *
     * @return Collection|Group[]
     */
    protected function setUpGroups(School $school, int $numberOfGroups): Collection
    {
        return Group::factory()->count($numberOfGroups)->inSchool($school)->create();
    }

    /**
     * Set up a teacher that has a group and a pupil in the current schoolyear.
     * Use the existing teacher if it has been set up before.
     *
     * @param int $numberOfGroups Number of groups to create (default: 1).
     * @param int $numberOfPupils Number of pupils to create (default: 1).
     */
    protected function setUpTeacherWithGroupsAndPupilsInCurrentSchoolyear(
        int $numberOfGroups = 1,
        int $numberOfPupils = 1,
    ): void {
        if ($this->owner === null && $this->school === null) {
            $this->setUpTeacher();
        }

        $this->schoolyear = app(SchoolyearRepositoryInterface::class)->getCurrent();

        $this->pupils = Collection::wrap(
            Pupil::factory()
                ->count($numberOfPupils)
                ->withActiveCareer($this->school)
                ->create(),
        );

        $this->groups = Collection::wrap(Group::factory()->count($numberOfGroups)->inSchool($this->school)->create());

        $this->pupils->each(function (User $pupil, int $index) use ($numberOfGroups): void {
            $group = $this->groups->get($index % $numberOfGroups);

            Career::factory()->inGroup($group)->forUser($pupil)->withRole(RoleName::Pupil)->create();
        });

        $this->groups->each(function (Group $group, int $index): void {
            $group->update([
                'natural_study_year' => $index + 1,
                'target_audience_type' => TargetAudienceType::Lo,
                'is_classgroup' => true,
            ]);

            Career::factory()->inGroup($group)->forUser($this->owner)->create();
        });
        $this->owner->refresh();

        $this->pupil = $this->pupils->first();
        $this->group = $this->groups->first();
        $this->giveCareDataAccessToUserInSchool([$this->group->id], $this->owner, $this->school);
    }

    protected function giveCareDataAccessToUserInSchool(array $groupIds, User $user, School $school): void
    {
        $schoolUserAccess = SchoolUserAccess::whereSchoolId($school->id)->whereUserId($user->id)->firstOrFail();
        $schoolUserAccess->careDataAccessGroups()->sync($groupIds);
    }

    /**
     * Set up care infos for the pupils.
     */
    protected function setUpCareInfos(): void
    {
        if ($this->pupils === null) {
            $this->setUpTeacherWithGroupsAndPupilsInCurrentSchoolyear();
        }

        $this->careInfos = collect();
        $this->addCareInfos($this->pupils);
    }

    /**
     * Add care infos for the given users.
     *
     * @param Collection $users The users to add a care info for.
     */
    protected function addCareInfos(Collection $users): void
    {
        $users->each(function (User $pupil): void {
            $this->careInfos->push(
                CareInfo::factory()->forPupil($pupil)->inSchool($this->school)->create(),
            );
        });
    }

    /**
     * Add care types to the care inputs.
     *
     * @param int $numberOfCareTypes Number of care types to create.
     */
    protected function setUpCareTypes(int $numberOfCareTypes = 1): void
    {
        if ($this->careInfos === null) {
            $this->setUpCareInfos();
        }

        $this->careTypes = CareType::factory()
            ->count($numberOfCareTypes)
            ->forSchool($this->school)
            ->setArchivedAt(null)
            ->create();
    }

    /**
     * Set up care inputs for the pupils.
     *
     * @param int $careInputsPerPupil Number of care inputs per pupil (default: 1).
     */
    protected function setUpCareInputs(int $careInputsPerPupil = 1): void
    {
        $this->careInputs = collect();
        $this->careInfos->each(function (CareInfo $careInfo) use ($careInputsPerPupil): void {
            $careInputs = CareInput::factory()->count($careInputsPerPupil)->forSchool($this->school)->create();
            $careInputs->each(function (CareInput $careInput) use ($careInfo): void {
                $careInput->creator_id = $this->owner->id;
                $careInput->careInfos()->save($careInfo);
            });
            $this->careInputs = $this->careInputs->merge($careInputs);
        });

        $this->linkCareTypesToCareInputs();
    }

    /**
     * Link care types to the care inputs.
     */
    protected function linkCareTypesToCareInputs(): void
    {
        $this->careInputs->each(function (CareInput $careInput, int $index): void {
            $careType = $this->careTypes->get($index % $this->careTypes->count());
            $careInput->care_type_id = $careType->id;
            $careInput->save();
        });
    }

    /**
     * Add care themes to the care inputs.
     *
     * @param int $numberOfCareThemes Number of care themes to create.
     */
    protected function setUpCareThemes(int $numberOfCareThemes): void
    {
        if ($this->careInputs === null) {
            $this->setUpCareInputs();
        }

        $this->careThemes = CareTheme::factory()
            ->count($numberOfCareThemes)
            ->forSchool($this->school)
            ->setArchivedAt(null)
            ->create();

        $this->careInputs->each(function (CareInput $careInput, int $index) use ($numberOfCareThemes): void {
            $careTheme = $this->careThemes->get($index % $numberOfCareThemes);
            $careInput->careThemes()->sync([$careTheme->id]);
        });
    }

    /**
     * Create a user with the given role.
     *
     * @param School $school The School the user should be linked to.
     * @param RoleName $roleName Role that needs to be assigned to the new user.
     * @param int $amount Number of users to create.
     */
    protected function createUsersWithRole(School $school, RoleName $roleName, int $amount = 1): User|Collection
    {
        $users = Collection::wrap(User::factory()->times($amount)->create());

        $users->each(function (User $user) use ($school, $roleName): void {
            Career::factory()
                ->inSchool($school)
                ->forUser($user)
                ->withRole($roleName)
                ->create();
        });

        return $users->count() === 1 ? $users->first() : $users;
    }

    /**
     * Create a user with the given role and link it to a specified group.
     *
     * @param School $school The School the user should be linked to.
     * @param RoleName $roleName Role that needs to be assigned to the new user.
     * @param Group $group Group that needs to be linked to the user.
     * @param array $userOverrides Data to pass to the user factory.
     * @param array $careerOverrides Data to pass to the career factory.
     */
    protected function createUserWithRoleAndGroup(
        School $school,
        RoleName $roleName,
        Group $group,
        array $userOverrides = [],
        array $careerOverrides = [],
    ): User|Collection {
        $user = User::factory()->create($userOverrides);

        Career::factory()
            ->forUser($user)
            ->inGroup($group)
            ->withRole($roleName)
            ->create($careerOverrides);

        return $user;
    }

    /**
     * Create a user with the given role.
     *
     * @param School $school The School the user should be linked to.
     * @param RoleName $roleName Role that needs to be assigned to the new user.
     * @param Collection|PermissionName[] $permissions Permissions that needs to be assigned to the new user.
     * @param int $amount Number of users to create.
     */
    protected function createUsersWithRoleAndPermissions(
        School $school,
        RoleName $roleName,
        Collection $permissions,
        int $amount = 1,
    ): User|Collection {
        if ($amount === 1) {
            /* @var User $user */
            $user = $this->createUsersWithRole($school, $roleName);

            return $this->addPermissionsToUser($user, $permissions);
        }
        $users = Collection::wrap($this->createUsersWithRole($school, $roleName, $amount));
        $users->each(function (User $user) use ($permissions): void {
            $this->addPermissionsToUser($user, $permissions);
        });

        return $users;
    }

    /**
     * Assign permissions to a User his career.
     * *WARNING* We assume this user has only one career!
     *
     * @param User $user The user we want to assign permissions.
     * @param Collection|PermissionName[] $permissions Permissions that needs to be assigned to the new user.
     */
    protected function addPermissionsToUser(User $user, Collection $permissions): User
    {
        /* @var SchoolUserAccess $schoolUserAccess */
        $schoolUserAccess = $user->schoolUserAccess->first();
        $permissions->each(function (PermissionName $permissionName) use ($schoolUserAccess): void {
            $schoolUserAccess->addPermission($permissionName);
        });

        return $user;
    }

    /**
     * Removes all the careers containing groups for the given user.
     *
     * @param User|null $user The user we want to clear the groups for.
     * @param School|null $school The school of the user.
     */
    protected function clearCareerGroups(?User $user = null, ?School $school = null): void
    {
        $user ??= $this->owner;
        $school ??= $this->school;

        Career::whereSchoolId($school->id)
            ->whereUserId($user->id)
            ->whereNotNull('group_id')
            ->delete();
    }

    protected function createGroup(): Group
    {
        if (!$this->schoolyear) {
            $this->schoolyear = app(SchoolyearRepositoryInterface::class)->getCurrent();
        }

        return Group::factory()->inSchool($this->school)->create();
    }

    /**
     * Create a group with pupils.
     *
     * @param int $amountOfPupils The amount of pupils to include in this group.
     */
    protected function createGroupWithPupils(int $amountOfPupils = 1): Group
    {
        $group = $this->createGroup();

        $pupils = Collection::wrap($this->createUsersWithRole($this->school, RoleName::Pupil, $amountOfPupils));
        $pupils->each(function (User $pupil, int $index) use ($group): void {
            Career::factory()
                ->inGroup($group)
                ->forUser($pupil)
                ->withRole(RoleName::Pupil)
                ->create();
        });

        return $group;
    }
}
