<?php

namespace Tests\Unit\Database\Migrations;

use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\User;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\Database\MigrationTestCase;

class RestoreSoftDeletePupilsAndCareersTest extends MigrationTestCase
{
    protected string $migration = '2023_10_24_173218_restore_soft_deleted_pupils_and_careers.php';

    private User $user;
    private School $school;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();
        $this->school = School::factory()->create();
        $this->user = User::factory()->create();
    }

    #[Test]
    public function it_restores_a_pupil_career_from_this_school_year(): void
    {
        $career = Career::factory()
            ->forUser($this->user)
            ->withRole(RoleName::Pupil)
            ->inSchool($this->school)
            ->create([
                'enddate' => null,
                'deleted_at' => '2023-09-05 23:22:11',
            ]);
        $this->runMigration();
        $this->assertDatabaseHas('careers', [
            'id' => $career->id,
            'enddate' => '2023-09-05 23:22:11',
            'deleted_at' => null,
        ]);
    }

    #[Test]
    public function it_keeps_the_existing_enddate(): void
    {
        $career = Career::factory()
            ->forUser($this->user)
            ->withRole(RoleName::Pupil)
            ->inSchool($this->school)
            ->create([
                'enddate' => '2024-09-05 23:22:11',
                'deleted_at' => '2023-09-05 23:22:11',
            ]);
        $this->runMigration();
        $this->assertDatabaseHas('careers', [
            'id' => $career->id,
            'enddate' => '2024-09-05 00:00:00',
            'deleted_at' => null,
        ]);
    }

    #[Test]
    public function it_doesnt_restore_a_pupil_career_from_last_schoolyear(): void
    {
        $career = Career::factory()
            ->forUser($this->user)
            ->withRole(RoleName::Pupil)
            ->inSchool($this->school)
            ->create([
                'enddate' => null,
                'deleted_at' => '2023-07-30 23:22:11',
            ]);
        $this->runMigration();
        $this->assertDatabaseHas('careers', [
            'id' => $career->id,
            'enddate' => null,
            'deleted_at' => '2023-07-30 23:22:11',
        ]);
    }

    #[Test]
    public function it_doesnt_restore_a_teacher_career(): void
    {
        $career = Career::factory()
            ->forUser($this->user)
            ->withRole(RoleName::Teacher)
            ->inSchool($this->school)
            ->create([
                'enddate' => null,
                'deleted_at' => '2023-09-05 23:22:11',
            ]);
        $this->runMigration();
        $this->assertDatabaseHas('careers', [
            'id' => $career->id,
            'enddate' => null,
            'deleted_at' => '2023-09-05 23:22:11',
        ]);
    }

    #[Test]
    public function it_restores_a_user_model_deleted_this_school_year(): void
    {
        $user = User::factory()->create(['deleted_at' => '2023-09-05 23:22:11']);
        $career = Career::factory()
            ->forUser($user)
            ->withRole(RoleName::Pupil)
            ->inSchool($this->school)
            ->create(['enddate' => null]);
        $this->runMigration();
        $this->assertDatabaseHas('users', ['id' => $user->id, 'deleted_at' => null]);
        $this->assertDatabaseHas('careers', [
            'id' => $career->id,
            'enddate' => '2023-09-05 23:22:11',
            'deleted_at' => null,
        ]);
    }

    #[Test]
    public function it_does_not_restore_a_user_model_deleted_last_school_year(): void
    {
        $user = User::factory()->create(['deleted_at' => '2023-07-30 23:22:11']);
        $career = Career::factory()
            ->forUser($user)
            ->withRole(RoleName::Pupil)
            ->inSchool($this->school)
            ->create(['enddate' => null]);
        $this->runMigration();
        $this->assertDatabaseHas('users', ['id' => $user->id, 'deleted_at' => '2023-07-30 23:22:11']);
        $this->assertDatabaseHas('careers', [
            'id' => $career->id,
            'enddate' => null,
            'deleted_at' => null,
        ]);
    }

    #[Test]
    public function it_restores_a_user_model_and_careers_deleted_this_school_year(): void
    {
        $user = User::factory()->create(['deleted_at' => '2023-09-05 23:22:11']);
        $career = Career::factory()
            ->forUser($user)
            ->withRole(RoleName::Pupil)
            ->inSchool($this->school)
            ->create([
                'enddate' => null,
                'deleted_at' => '2023-08-01 22:22:22',
            ]);
        $this->runMigration();
        $this->assertDatabaseHas('users', ['id' => $user->id, 'deleted_at' => null]);
        $this->assertDatabaseHas('careers', [
            'id' => $career->id,
            'enddate' => '2023-08-01 22:22:22',
            'deleted_at' => null,
        ]);
    }
}
