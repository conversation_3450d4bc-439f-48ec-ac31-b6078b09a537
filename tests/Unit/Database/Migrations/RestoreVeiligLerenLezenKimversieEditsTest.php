<?php

namespace Tests\Unit\Database\Migrations;

use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\CalendarItem\CalendarItem;
use Cfa\Planner\Domain\CalendarItem\Row\CalendarItemRow;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\Chapter\Chapter;
use Cfa\Planner\Domain\CollectionV2\Chapter\ChapterOverwrite;
use Cfa\Planner\Domain\CollectionV2\Chapter\ChapterOverwriteColumn;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Record;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordOverwrite;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordOverwriteColumn;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\Database\MigrationTestCase;

class RestoreVeiligLerenLezenKimversieEditsTest extends MigrationTestCase
{
    protected string $migration = '2024_11_19_160044_restore_veilig_leren_lezen_kimversie_edits.php';

    private Chapter $oldChapter;
    private Chapter $newChapter;
    private Record $oldRecord;
    private Record $newRecord;
    private ChapterOverwrite $chapterOverwite;
    private RecordOverwrite $recordOverwite;
    private PlannerCollection $collection;
    private ActivatedPlannerCollection $activatedPlannerCollection;
    private User $user;

    #[Override]
    public function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
        $this->collection = PlannerCollection::factory()
            ->create(['id' => 444]);
        $this->activatedPlannerCollection = ActivatedPlannerCollection::factory()
            ->forPlannerCollection($this->collection)
            ->forUser($this->user)
            ->create();

        $this->oldChapter = Chapter::factory()
            ->forPlannerCollection($this->collection)
            ->create(['name' => 'Chapter 1']);
        $this->newChapter = Chapter::factory()
            ->forPlannerCollection($this->collection)
            ->create(['name' => 'Chapter 1']);

        $this->chapterOverwite = ChapterOverwrite::factory()
            ->forUser($this->user)
            ->forChapter($this->oldChapter)
            ->forActivatedPlannerCollection($this->activatedPlannerCollection)
            ->forColumn(ChapterOverwriteColumn::Name)
            ->withValue('Chapter overwrite')
            ->create();

        $this->oldRecord = Record::factory()
            ->forPlannerCollection($this->collection)
            ->forChapter($this->oldChapter)
            ->create(['name' => 'Record 1']);
        $this->newRecord = Record::factory()
            ->forPlannerCollection($this->collection)
            ->forChapter($this->newChapter)
            ->create(['name' => 'Record 1']);
        $this->recordOverwite = RecordOverwrite::factory()
            ->forUser($this->user)
            ->forRecord($this->oldRecord)
            ->forActivatedPlannerCollection($this->activatedPlannerCollection)
            ->forColumn(RecordOverwriteColumn::Name)
            ->withValue('Record overwrite')
            ->create();
    }

    #[Test]
    public function it_moves_the_overwrites_from_the_old_chapter_and_records_to_the_new_ones(): void
    {
        $this->deleteOldData();
        $this->runMigration();

        $this->chapterOverwite->refresh();
        $this->recordOverwite->refresh();

        $this->assertEquals($this->chapterOverwite->chapter_id, $this->newChapter->id);
        $this->assertEquals($this->recordOverwite->record_id, $this->newRecord->id);
    }

    #[Test]
    public function it_does_nothing_when_the_there_are_no_matching_names(): void
    {
        PlannerCollection::setQueryingEnabled();
        Chapter::setQueryingEnabled();
        Record::setQueryingEnabled();
        $this->oldChapter->name = 'Blabla';
        $this->oldRecord->name = 'Blabla';
        $this->oldChapter->save();
        $this->oldRecord->save();

        $this->deleteOldData();
        $this->runMigration();

        $this->chapterOverwite->refresh();
        $this->recordOverwite->refresh();

        $this->assertEquals($this->chapterOverwite->chapter_id, $this->oldChapter->id);
        $this->assertEquals($this->recordOverwite->record_id, $this->oldRecord->id);
    }

    #[Test]
    public function it_does_nothing_when_there_are_multiple_matching_chapters(): void
    {
        Chapter::factory()
            ->forPlannerCollection($this->collection)
            ->create(['name' => 'Chapter 1']);

        $ownRecord = Record::factory()
            ->forActivatedCollection($this->activatedPlannerCollection)
            ->forChapter($this->oldChapter)
            ->create(['name' => 'Record 1']);

        $calendarItemRow = CalendarItemRow::factory()
            ->forCalendarItem(CalendarItem::factory()->create())
            ->forRecord($this->oldRecord)
            ->create();

        $this->deleteOldData();
        $this->runMigration();

        $this->chapterOverwite->refresh();
        $this->recordOverwite->refresh();

        $this->assertEquals($this->chapterOverwite->chapter_id, $this->oldChapter->id);
        $this->assertEquals($this->recordOverwite->record_id, $this->oldRecord->id);
        $this->assertEquals($ownRecord->chapter_id, $this->oldChapter->id);
        $this->assertEquals($calendarItemRow->record_v2_id, $this->oldRecord->id);
    }

    #[Test]
    public function it_does_nothing_to_record_overwrites_when_there_are_multiple_matching_records(): void
    {
        Record::factory()
            ->forPlannerCollection($this->collection)
            ->forChapter($this->newChapter)
            ->create(['name' => 'Record 1']);

        $this->deleteOldData();
        $this->runMigration();

        $this->chapterOverwite->refresh();
        $this->recordOverwite->refresh();

        $this->assertEquals($this->chapterOverwite->chapter_id, $this->newChapter->id);
        $this->assertEquals($this->recordOverwite->record_id, $this->oldRecord->id);
    }

    #[Test]
    public function it_does_nothing_when_a_matching_overwrite_already_exists_for_the_new_chapter(): void
    {
        ChapterOverwrite::factory()
            ->forUser($this->user)
            ->forChapter($this->newChapter)
            ->forActivatedPlannerCollection($this->activatedPlannerCollection)
            ->forColumn(ChapterOverwriteColumn::Name)
            ->withValue('Chapter overwrite')
            ->create();

        $this->deleteOldData();
        $this->runMigration();

        $this->chapterOverwite->refresh();
        $this->recordOverwite->refresh();

        $this->assertEquals($this->chapterOverwite->chapter_id, $this->oldChapter->id);
        $this->assertEquals($this->recordOverwite->record_id, $this->newRecord->id);
    }

    #[Test]
    public function it_moves_the_overwrites_when_non_matching_overwrites_exists_for_the_chapter(): void
    {
        $otherUser = User::factory()->create();
        $otherActivatedCollection = ActivatedPlannerCollection::factory()
            ->forPlannerCollection($this->collection)
            ->forUser($this->user)
            ->create();

        ChapterOverwrite::factory()
            ->forUser($otherUser)
            ->forChapter($this->newChapter)
            ->forActivatedPlannerCollection($otherActivatedCollection)
            ->forColumn(ChapterOverwriteColumn::Name)
            ->withValue('Chapter overwrite')
            ->create();

        ChapterOverwrite::factory()
            ->forUser($this->user)
            ->forChapter($this->newChapter)
            ->forActivatedPlannerCollection($this->activatedPlannerCollection)
            ->forColumn(ChapterOverwriteColumn::Order)
            ->withValue(2)
            ->create();

        $this->deleteOldData();
        $this->runMigration();

        $this->chapterOverwite->refresh();
        $this->recordOverwite->refresh();

        $this->assertEquals($this->chapterOverwite->chapter_id, $this->newChapter->id);
        $this->assertEquals($this->recordOverwite->record_id, $this->newRecord->id);
    }

    #[Test]
    public function it_does_nothing_to_record_overwrites_when_an_overwrite_already_exists_on_the_new_record(): void
    {
        RecordOverwrite::factory()
            ->forUser($this->user)
            ->forRecord($this->newRecord)
            ->forActivatedPlannerCollection($this->activatedPlannerCollection)
            ->forColumn(RecordOverwriteColumn::Name)
            ->withValue('Record overwrite')
            ->create();

        $this->deleteOldData();
        $this->runMigration();

        $this->chapterOverwite->refresh();
        $this->recordOverwite->refresh();

        $this->assertEquals($this->chapterOverwite->chapter_id, $this->newChapter->id);
        $this->assertEquals($this->recordOverwite->record_id, $this->oldRecord->id);
    }

    #[Test]
    public function it_moves_the_overwrites_when_non_matching_overwrites_exists_for_the_record(): void
    {
        $otherUser = User::factory()->create();
        $otherActivatedCollection = ActivatedPlannerCollection::factory()
            ->forPlannerCollection($this->collection)
            ->forUser($this->user)
            ->create();

        RecordOverwrite::factory()
            ->forUser($otherUser)
            ->forRecord($this->newRecord)
            ->forActivatedPlannerCollection($otherActivatedCollection)
            ->forColumn(RecordOverwriteColumn::Name)
            ->withValue('Record overwrite')
            ->create();

        RecordOverwrite::factory()
            ->forUser($this->user)
            ->forRecord($this->newRecord)
            ->forActivatedPlannerCollection($this->activatedPlannerCollection)
            ->forColumn(RecordOverwriteColumn::Order)
            ->withValue(2)
            ->create();

        $this->deleteOldData();
        $this->runMigration();

        $this->chapterOverwite->refresh();
        $this->recordOverwite->refresh();

        $this->assertEquals($this->chapterOverwite->chapter_id, $this->newChapter->id);
        $this->assertEquals($this->recordOverwite->record_id, $this->newRecord->id);
    }

    #[Test]
    public function it_does_nothing_when_the_chapter_was_deleted_before_the_disaster_day(): void
    {
        PlannerCollection::setQueryingEnabled();
        Chapter::setQueryingEnabled();
        Record::setQueryingEnabled();
        ChapterOverwrite::setQueryingEnabled();
        RecordOverwrite::setQueryingEnabled();
        $this->oldChapter->deleted_at = '2024-11-14 09:00:00';
        $this->oldRecord->deleted_at = '2024-11-15 09:00:00';
        $this->oldRecord->save();
        $this->oldChapter->save();

        $this->runMigration();

        $this->chapterOverwite->refresh();
        $this->recordOverwite->refresh();

        $this->assertEquals($this->chapterOverwite->chapter_id, $this->oldChapter->id);
        $this->assertEquals($this->recordOverwite->record_id, $this->oldRecord->id);
    }

    #[Test]
    public function it_does_nothing_to_the_record_overwrite_when_the_record_was_deleted_before_the_disaster_day(): void
    {
        PlannerCollection::setQueryingEnabled();
        Chapter::setQueryingEnabled();
        Record::setQueryingEnabled();
        ChapterOverwrite::setQueryingEnabled();
        RecordOverwrite::setQueryingEnabled();
        $this->oldChapter->deleted_at = '2024-11-15 09:00:00';
        $this->oldRecord->deleted_at = '2024-11-14 09:00:00';
        $this->oldRecord->save();
        $this->oldChapter->save();

        $this->runMigration();

        $this->chapterOverwite->refresh();
        $this->recordOverwite->refresh();

        $this->assertEquals($this->chapterOverwite->chapter_id, $this->newChapter->id);
        $this->assertEquals($this->recordOverwite->record_id, $this->oldRecord->id);
    }

    #[Test]
    public function it_moves_the_own_record_to_the_new_chapter(): void
    {
        Record::factory()
            ->forActivatedCollection($this->activatedPlannerCollection)
            ->forChapter($this->newChapter)
            ->create(['name' => 'Record 2']);

        $ownRecord = Record::factory()
            ->forActivatedCollection($this->activatedPlannerCollection)
            ->forChapter($this->oldChapter)
            ->create(['name' => 'Record 1']);

        $this->deleteOldData();
        $this->runMigration();

        $ownRecord->refresh();

        $this->assertEquals($ownRecord->chapter_id, $this->newChapter->id);
    }

    #[Test]
    public function it_does_nothing_when_the_new_chapter_already_has_the_same_record(): void
    {
        Record::factory()
            ->forActivatedCollection($this->activatedPlannerCollection)
            ->forChapter($this->newChapter)
            ->create(['name' => 'Record 1']);

        $ownRecord = Record::factory()
            ->forActivatedCollection($this->activatedPlannerCollection)
            ->forChapter($this->oldChapter)
            ->create(['name' => 'Record 1']);

        $this->deleteOldData();
        $this->runMigration();

        $ownRecord->refresh();

        $this->assertEquals($ownRecord->chapter_id, $this->oldChapter->id);
    }

    #[Test]
    public function it_updates_the_old_record_id_on_calendar_item_rows_to_the_new_one(): void
    {
        $calendarItemRow = CalendarItemRow::factory()
            ->forCalendarItem(CalendarItem::factory()->create())
            ->forRecord($this->oldRecord)
            ->create();

        $this->deleteOldData();
        $this->runMigration();

        $calendarItemRow->refresh();

        $this->assertEquals($calendarItemRow->record_v2_id, $this->newRecord->id);
    }

    #[Test]
    public function it_does_nothing_to_calendar_items_when_there_are_multiple_matching_records(): void
    {
        Record::factory()
            ->forPlannerCollection($this->collection)
            ->forChapter($this->newChapter)
            ->create(['name' => 'Record 1']);

        $calendarItemRow = CalendarItemRow::factory()
            ->forCalendarItem(CalendarItem::factory()->create())
            ->forRecord($this->oldRecord)
            ->create();

        $this->deleteOldData();
        $this->runMigration();

        $calendarItemRow->refresh();

        $this->assertEquals($calendarItemRow->record_v2_id, $this->oldRecord->id);
    }

    private function deleteOldData()
    {
        PlannerCollection::setQueryingEnabled();
        Chapter::setQueryingEnabled();
        Record::setQueryingEnabled();
        ChapterOverwrite::setQueryingEnabled();
        RecordOverwrite::setQueryingEnabled();
        $this->oldChapter->deleted_at = '2024-11-15 09:00:00';
        $this->oldRecord->deleted_at = '2024-11-15 09:00:00';
        $this->oldRecord->save();
        $this->oldChapter->save();
    }
}
