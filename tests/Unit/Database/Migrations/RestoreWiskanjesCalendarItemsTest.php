<?php

namespace Tests\Unit\Database\Migrations;

use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\CalendarItem\CalendarItem;
use Cfa\Planner\Domain\CalendarItem\Row\CalendarItemRow;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\Chapter\Chapter;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Record;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\Database\MigrationTestCase;

class RestoreWiskanjesCalendarItemsTest extends MigrationTestCase
{
    protected string $migration = '2024_10_03_131144_restore_wiskanjer_calendar_items.php';

    private Chapter $oldChapter;
    private Chapter $newChapter;
    private Record $oldRecord;
    private Record $newRecord;
    private PlannerCollection $collection;
    private ActivatedPlannerCollection $activatedPlannerCollection;
    private User $user;
    private CalendarItemRow $calendarItemRow;

    #[Override]
    public function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
        $this->collection = PlannerCollection::factory()
            ->create(['id' => 236]);
        $this->activatedPlannerCollection = ActivatedPlannerCollection::factory()
            ->forPlannerCollection($this->collection)
            ->forUser($this->user)
            ->create();

        $this->oldChapter = Chapter::factory()
            ->forPlannerCollection($this->collection)
            ->create(['name' => 'Chapter 1']);
        $this->newChapter = Chapter::factory()
            ->forPlannerCollection($this->collection)
            ->create(['name' => 'Chapter 1']);

        $this->oldRecord = Record::factory()
            ->forPlannerCollection($this->collection)
            ->forChapter($this->oldChapter)
            ->create(['name' => 'Record 1']);
        $this->newRecord = Record::factory()
            ->forPlannerCollection($this->collection)
            ->forChapter($this->newChapter)
            ->create(['name' => 'Record 1']);

        $this->calendarItemRow = CalendarItemRow::factory()
            ->forCalendarItem(CalendarItem::factory()->create())
            ->forRecord($this->oldRecord)
            ->create();
    }

    #[Test]
    public function it_updates_the_old_record_id_to_the_new_one(): void
    {
        $this->deleteOldData();
        $this->runMigration();

        $this->calendarItemRow->refresh();

        $this->assertEquals($this->calendarItemRow->record_v2_id, $this->newRecord->id);
    }

    #[Test]
    public function it_does_nothing_when_there_are_multiple_matching_chapters(): void
    {
        Chapter::factory()
            ->forPlannerCollection($this->collection)
            ->create(['name' => 'Chapter 1']);

        $this->deleteOldData();
        $this->runMigration();

        $this->calendarItemRow->refresh();

        $this->assertEquals($this->calendarItemRow->record_v2_id, $this->oldRecord->id);
    }

    #[Test]
    public function it_does_nothing_to_calendar_items_when_there_are_multiple_matching_records(): void
    {
        Record::factory()
            ->forPlannerCollection($this->collection)
            ->forChapter($this->newChapter)
            ->create(['name' => 'Record 1']);

        $this->deleteOldData();
        $this->runMigration();

        $this->calendarItemRow->refresh();

        $this->assertEquals($this->calendarItemRow->record_v2_id, $this->oldRecord->id);
    }

    private function deleteOldData()
    {
        PlannerCollection::setQueryingEnabled();
        Chapter::setQueryingEnabled();
        Record::setQueryingEnabled();
        $this->oldChapter->deleted_at = '2024-09-12 09:00:00';
        $this->oldRecord->deleted_at = '2024-09-12 09:00:00';
        $this->oldRecord->save();
        $this->oldChapter->save();
    }
}
