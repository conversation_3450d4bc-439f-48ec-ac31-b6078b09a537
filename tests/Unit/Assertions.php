<?php

namespace Tests\Unit;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;
use Illuminate\View\View;
use PHPUnit\Framework\Assert as PHPUnit;

trait Assertions
{
    /**
     * Asserts that two collections have the same contents, not necessarily in the same order.
     *
     * @param Collection|JsonResource|array $expected Expected collection.
     * @param Collection|array $actual Actual collection.
     */
    public function assertEqualContents(Collection|JsonResource|array $expected, Collection|array $actual): void
    {
        if ($expected instanceof JsonResource) {
            $expected = $expected->resolve();
        }

        // This is used to make an array of every object within your resource, collection or array of $expected.
        // The array_multisort would not work if $expected contains objects.
        $expected = json_decode(json_encode($expected), true);
        $actual = json_decode(json_encode($actual), true);

        array_multisort($expected);
        array_multisort($actual);

        $this->assertEqualContentsArray($expected, $actual);
    }

    /**
     * Asserts that two arrays have the same contents.
     *
     * @param array $expected Expected array.
     * @param array $actual Actual array.
     */
    private function assertEqualContentsArray(array $expected, array $actual): void
    {
        PHPUnit::assertEquals(count($expected), count($actual));

        foreach ($expected as $key => $value) {
            is_array($value) ?
                $this->assertEqualContentsArray($value, $actual[$key]) :
                PHPUnit::assertTrue(
                    array_key_exists($key, $actual) && $this->valuesAreEqual($actual[$key], $value),
                    sprintf(
                        'Value %s for key %s does not match expected value %s.',
                        $actual[$key] ?? 'null',
                        $key,
                        $value,
                    ),
                );
        }
    }

    /**
     * Check if two values should be considered the same.
     *
     * @param mixed $valueOne The first value.
     * @param mixed $valueTwo The second value.
     */
    private function valuesAreEqual(mixed $valueOne, mixed $valueTwo): bool
    {
        if ($valueOne === $valueTwo) {
            return true;
        }

        // Allow a small time difference for Carbon, because the slowness of testing can occasionally result in some
        // seconds difference between expected and actual (especially when testing with coverage).
        $datePattern = '/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/';
        if (preg_match($datePattern, (string) $valueOne) === 1 && preg_match($datePattern, (string) $valueTwo) === 1) {
            $allowedDifferenceInSeconds = 30;
            $diffInSeconds = Carbon::parse($valueOne)->diffInSeconds(Carbon::parse($valueTwo), absolute: true);

            return $diffInSeconds < $allowedDifferenceInSeconds;
        }

        return false;
    }

    /**
     * Asserts if the View contains the correct data.
     *
     * @param View $view The $view we want to validate.
     * @param array $expectedData Key value pairs of wat we expect to see in the data of the view.
     */
    public function assertViewData(View $view, array $expectedData): void
    {
        $viewData = $view->getData();
        $this->assertEqualContents($expectedData, $viewData);
    }

    /**
     * Assert that two timestamps are almost the same.
     *
     * @param Carbon $timestamp1 The first timestamp to compare.
     * @param Carbon $timestamp2 The second timestamp to compare.
     * @param int $allowedDifference The allowed difference between the timestamps in seconds (2 by default).
     */
    protected function assertAlmostMatchingTimestamps(
        Carbon $timestamp1,
        Carbon $timestamp2,
        int $allowedDifference = 15,
    ): void {
        $this->assertLessThan($allowedDifference, $timestamp1->diffInSeconds($timestamp2, absolute: true));
    }
}
