<?php

namespace Tests\Unit\Cfa\Evaluation\Repositories;

use Carbon\Carbon;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\EvaluationTest\EvaluationTest;
use Cfa\Evaluation\Domain\EvaluationTest\EvaluationTestRepositoryInterface;
use Cfa\Evaluation\Domain\EvaluationTest\Score\EvaluationTestScore;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Illuminate\Support\Collection;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Traits\WithSeeding;
use Tests\Unit\UnitTestCase;

use function factory;

class EvaluationTestRepositoryTest extends UnitTestCase
{
    use WithSeeding;

    protected EvaluationTestRepositoryInterface $repository;

    protected Subject $subject;

    protected ReportPeriod $reportPeriod;

    protected Collection $evaluationTests;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->repository = app(EvaluationTestRepositoryInterface::class);

        $this->setUpTeacher();

        $this->group = Group::factory()->inSchool($this->school)->create();
        $this->pupils = $this->createUsersWithRole($this->school, RoleName::Pupil, 5)->pluck('id')->toArray();
        $this->subject = Subject::factory()->create([
            'school_id' => $this->school->id,
        ]);
        $this->reportPeriod = ReportPeriod::factory()
            ->forReportSettings(ReportSettings::factory()->forGroup($this->group)->create())
            ->create([
                'school_id' => $this->school->id,
                'schoolyear_id' => app(SchoolyearRepositoryInterface::class)->getCurrent()->id,
            ]);

        $this->evaluationTests = factory(EvaluationTest::class, 3)->create([
            'school_id' => $this->school->id,
            'group_id' => $this->group->id,
            'subject_id' => $this->subject->id,
            'report_period_id' => $this->reportPeriod->id,
        ]);
    }

    #[Test]
    public function it_returns_all_correct_evaluation_tests(): void
    {
        $tests = $this->repository->getAllForGroupAndSubjectInReportPeriod(
            [$this->group->id],
            $this->pupils,
            $this->subject,
            $this->reportPeriod,
        );

        $this->assertCount(3, $tests);
        $this->assertEquals($this->evaluationTests->sortBy('id')->pluck('uid'), $tests->sortBy('id')->pluck('uid'));
    }

    #[Test]
    public function it_does_not_return_evaluation_test_of_other_group(): void
    {
        $otherGroup = Group::factory()->inSchool($this->school)->create();

        $otherEvaluationTest = factory(EvaluationTest::class)->create([
            'school_id' => $this->school->id,
            'group_id' => $otherGroup->id,
            'subject_id' => $this->subject->id,
            'report_period_id' => $this->reportPeriod->id,
        ]);

        $tests = $this->repository->getAllForGroupAndSubjectInReportPeriod(
            [$this->group->id],
            $this->pupils,
            $this->subject,
            $this->reportPeriod,
        );

        $this->assertCount(3, $tests);
        $this->assertNotContains($otherEvaluationTest->uid, $tests->pluck('uid'));
    }

    #[Test]
    public function it_does_not_return_evaluation_test_of_other_subject(): void
    {
        $otherSubject = Subject::factory()->create([
            'school_id' => $this->school->id,
        ]);

        $otherEvaluationTest = factory(EvaluationTest::class)->create([
            'school_id' => $this->school->id,
            'group_id' => $this->group->id,
            'subject_id' => $otherSubject->id,
            'report_period_id' => $this->reportPeriod->id,
        ]);

        $tests = $this->repository->getAllForGroupAndSubjectInReportPeriod(
            [$this->group->id],
            $this->pupils,
            $this->subject,
            $this->reportPeriod,
        );

        $this->assertCount(3, $tests);
        $this->assertNotContains($otherEvaluationTest->uid, $tests->pluck('uid'));
    }

    #[Test]
    public function it_does_not_return_evaluation_test_of_other_report_period(): void
    {
        $otherReportPeriod = ReportPeriod::factory()
            ->forReportSettings(ReportSettings::factory()->forGroup($this->group)->create())
            ->create([
                'schoolyear_id' => app(SchoolyearRepositoryInterface::class)->getCurrent()->id,
            ]);

        $otherEvaluationTest = factory(EvaluationTest::class)->create([
            'school_id' => $this->school->id,
            'group_id' => $this->group->id,
            'subject_id' => $this->subject->id,
            'report_period_id' => $otherReportPeriod->id,
        ]);

        $tests = $this->repository->getAllForGroupAndSubjectInReportPeriod(
            [$this->group->id],
            $this->pupils,
            $this->subject,
            $this->reportPeriod,
        );

        $this->assertCount(3, $tests);
        $this->assertNotContains($otherEvaluationTest->uid, $tests->pluck('uid'));
    }

    #[Test]
    public function it_does_not_return_deleted_evaluation_test(): void
    {
        $otherEvaluationTest = factory(EvaluationTest::class)->create([
            'school_id' => $this->school->id,
            'group_id' => $this->group->id,
            'subject_id' => $this->subject->id,
            'report_period_id' => $this->reportPeriod->id,
            'deleted_at' => Carbon::now(),
        ]);

        $tests = $this->repository->getAllForGroupAndSubjectInReportPeriod(
            [$this->group->id],
            $this->pupils,
            $this->subject,
            $this->reportPeriod,
        );

        $this->assertCount(3, $tests);
        $this->assertNotContains($otherEvaluationTest->uid, $tests->pluck('uid'));
    }

    #[Test]
    public function it_does_not_return_scores_of_deleted_pupils(): void
    {
        $evaluationTest = $this->evaluationTests->first();
        $deletedPupil = Pupil::factory()->withActiveCareer($this->group)->create();
        EvaluationTestScore::factory()
            ->forEvaluationTest($evaluationTest)
            ->forPupil($deletedPupil)
            ->create(['score' => 3]);
        $pupil = Pupil::factory()->withActiveCareer($this->group)->create();
        EvaluationTestScore::factory()
            ->forEvaluationTest($evaluationTest)
            ->forPupil($pupil)
            ->create(['score' => 4]);
        EvaluationTest::where('id', '!=', $evaluationTest->id)->delete();
        $deletedPupil->delete();

        $tests = $this->repository->getAllForGroupAndSubjectInReportPeriod(
            [$this->group->id],
            [$deletedPupil->id, $pupil->id],
            $this->subject,
            $this->reportPeriod,
        );

        $this->assertCount(1, $tests);
        $scores = $tests->first()->evaluationTestScores;
        $this->assertCount(1, $scores);
        $this->assertSame(4.0, $scores->first()->score);
    }
}
