<?php

namespace Tests\Unit\Cfa\Evaluation\Services\Evaluation\EvaluationTest;

use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Application\Services\Evaluation\EvaluationTest\SetAverageAndMedianService;
use Cfa\Evaluation\Domain\EvaluationTest\EvaluationTest;
use Cfa\Evaluation\Domain\EvaluationTest\Score\EvaluationTestScore;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Override;
use PHPUnit\Framework\Attributes\Group;
use PHPUnit\Framework\Attributes\Test;
use Tests\Traits\WithSeeding;
use Tests\Unit\ServiceTestCase;

use function collect;
use function factory;
use function number_format;

class SetAverageAndMedianServiceTest extends ServiceTestCase
{
    use WithSeeding;

    protected string $serviceName = SetAverageAndMedianService::class;

    private EvaluationTest $evaluationTest;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->setUpTeacherWithGroupsAndPupilsInCurrentSchoolyear(1, 7);

        $this->subject = Subject::factory()->create(['school_id' => $this->school->id]);
        $this->group->natural_study_year = 3;
        $this->group->target_audience_type = TargetAudienceType::Lo;
        $this->group->save();
        $this->reportPeriod = ReportPeriod::factory()
            ->withYearReportAndReportSettings(
                ReportSettings::factory()->forGroup($this->group)->create(),
            )
            ->create();
        $this->evaluationTest = factory(EvaluationTest::class)->create([
            'min' => 0,
            'max' => 10,
        ]);
    }

    #[Group('mysql')]
    #[Test]
    public function it_set_the_average_and_median_for_the_given_evaluation_test(): void
    {
        $scores = collect([1, 5, 8.55, 10, 9.25, 3.1, null]);
        $this->pupils->each(
            fn(Pupil $pupil, int $index): EvaluationTestScore => $this->createScore($scores->get($index), $pupil),
        );

        $this->service->setAverageAndMedianForEvaluationTestId($this->evaluationTest->id);
        $this->evaluationTest->refresh();
        $this->assertEquals(number_format((float) $scores->avg(), 2), $this->evaluationTest->average);
        $this->assertEquals(number_format((float) $scores->median(), 2), $this->evaluationTest->median);
    }

    #[Group('mysql')]
    #[Test]
    public function it_set_the_average_and_median_for_the_given_evaluation_test_without_scores(): void
    {
        $scores = collect([null, null, null, null, null, null, null]);
        $this->pupils->each(
            fn(Pupil $pupil, int $index): EvaluationTestScore => $this->createScore($scores->get($index), $pupil),
        );

        $this->service->setAverageAndMedianForEvaluationTestId($this->evaluationTest->id);
        $this->evaluationTest->refresh();
        $this->assertNull($this->evaluationTest->average);
        $this->assertNull($this->evaluationTest->median);
    }

    private function createScore(
        ?float $score,
        Pupil $pupil,
        ?EvaluationTest $evaluationTest = null,
    ): EvaluationTestScore {
        return EvaluationTestScore::factory()
            ->forEvaluationTest($evaluationTest ?: $this->evaluationTest)
            ->forPupil($pupil)
            ->setScore($score)
            ->setComment(null)
            ->forQuotation(null)
            ->create();
    }
}
