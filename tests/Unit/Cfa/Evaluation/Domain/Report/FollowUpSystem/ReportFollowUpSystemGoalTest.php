<?php

namespace Tests\Unit\Cfa\Evaluation\Domain\Report\FollowUpSystem;

use Carbon\Carbon;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Common\Domain\School\School;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSource;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoal;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoalRepositoryInterface;
use Cfa\Evaluation\Domain\FollowUpSystem\Level\FollowUpSystemLevel;
use Cfa\Evaluation\Domain\FollowUpSystem\StructureType;
use Cfa\Evaluation\Domain\QuotationSystem\QuotationSystem;
use Cfa\Evaluation\Domain\Report\FollowUpSystem\ReportFollowUpSystemGoal;
use Illuminate\Support\Collection;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\UnitTestCase;

class ReportFollowUpSystemGoalTest extends UnitTestCase
{
    private School $school;
    private Group $group;
    private QuotationSystem $quotationSystem;
    private FollowUpSystem $followUpSystem;
    private FollowUpSystemLevel $followUpSystemLevel;
    private FollowUpSystemGoal $rootGoal;

    #[Override]
    public function setUp(): void
    {
        parent::setUp();

        $this->school = School::factory()->inKathOndVla()->create();

        $this->group = Group::factory()
            ->inSchool($this->school)
            ->withNaturalStudyYear(3, TargetAudienceType::Lo)
            ->create();

        $this->quotationSystem = QuotationSystem::factory()
            ->setSchoolId($this->school->id)
            ->create();

        $this->followUpSystem = factory(FollowUpSystem::class)->create([
            'structure_type' => StructureType::Table,
            'school_id' => $this->school->id,
            'source' => FollowUpSystemSource::Free,
            'quotation_system_id' => $this->quotationSystem->id,
        ]);

        $this->followUpSystemLevel = factory(FollowUpSystemLevel::class)->create([
            'follow_up_system_id' => $this->followUpSystem->id,
            'order' => 0,
            'is_evaluable' => false,
            'is_commentable' => true,
        ]);

        $this->rootGoal = factory(FollowUpSystemGoal::class)->create([
            'follow_up_system_id' => $this->followUpSystem->id,
            'follow_up_system_level_id' => $this->followUpSystemLevel->id,
            'parent_id' => null,
            'order' => 0,
        ]);
    }

    #[Test]
    public function it_shows_the_goal_when_follow_up_system_is_unfiltered(): void
    {
        $shouldFilterFollowUpSystem = false;
        $usedGoalUids = null;

        $goal = new ReportFollowUpSystemGoal($this->rootGoal, $shouldFilterFollowUpSystem, $usedGoalUids);

        $this->assertTrue($goal->shouldShowGoal());
    }

    #[Test]
    public function it_shows_the_goal_when_follow_up_system_is_filtered_and_goal_is_used(): void
    {
        $shouldFilterFollowUpSystem = true;
        $usedGoalUids = collect([
            $this->rootGoal->uid => $this->rootGoal->uid,
        ]);

        $goal = new ReportFollowUpSystemGoal($this->rootGoal, $shouldFilterFollowUpSystem, $usedGoalUids);

        $this->assertTrue($goal->shouldShowGoal());
    }

    #[Test]
    public function it_does_not_show_the_goal_when_follow_up_system_is_filtered_and_goal_is_not_used(): void
    {
        $shouldFilterFollowUpSystem = true;
        $usedGoalUids = null;

        $goal = new ReportFollowUpSystemGoal($this->rootGoal, $shouldFilterFollowUpSystem, $usedGoalUids);

        $this->assertFalse($goal->shouldShowGoal());
    }

    #[Test]
    public function it_returns_children_of_unfiltered_follow_up_system(): void
    {
        $shouldFilterFollowUpSystem = false;
        $usedGoalUids = null;

        $firstChild = $this->createFollowUpSystemGoal($this->rootGoal);
        $secondChild = $this->createFollowUpSystemGoal($firstChild);

        $goal = new ReportFollowUpSystemGoal($this->getTree()->first(), $shouldFilterFollowUpSystem, $usedGoalUids);

        $this->assertEquals([
            'uid' => $this->rootGoal->uid,
            'description' => $this->rootGoal->description,
            'children' => [
                [
                    'uid' => $firstChild->uid,
                    'description' => $firstChild->description,
                    'children' => [
                        [
                            'uid' => $secondChild->uid,
                            'description' => $secondChild->description,
                            'children' => [],
                        ],
                    ],
                ],
            ],
        ], $goal->toArray());
    }

    #[Test]
    public function it_returns_all_children_of_filtered_follow_up_system_when_deepest_child_is_used(): void
    {
        $firstChild = $this->createFollowUpSystemGoal($this->rootGoal);
        $secondChild = $this->createFollowUpSystemGoal($firstChild);

        $shouldFilterFollowUpSystem = true;
        $usedGoalUids = collect([$secondChild->uid => $secondChild->uid]);

        $goal = new ReportFollowUpSystemGoal($this->getTree()->first(), $shouldFilterFollowUpSystem, $usedGoalUids);

        $this->assertEquals([
            'uid' => $this->rootGoal->uid,
            'description' => $this->rootGoal->description,
            'children' => [
                [
                    'uid' => $firstChild->uid,
                    'description' => $firstChild->description,
                    'children' => [
                        [
                            'uid' => $secondChild->uid,
                            'description' => $secondChild->description,
                            'children' => [],
                        ],
                    ],
                ],
            ],
        ], $goal->toArray());
    }

    #[Test]
    public function it_returns_children_of_filtered_follow_up_system_when_first_level_child_is_used(): void
    {
        $firstChild = $this->createFollowUpSystemGoal($this->rootGoal);
        $this->createFollowUpSystemGoal($firstChild);

        $shouldFilterFollowUpSystem = true;
        $usedGoalUids = collect([$firstChild->uid => $firstChild->uid]);

        $goal = new ReportFollowUpSystemGoal($this->getTree()->first(), $shouldFilterFollowUpSystem, $usedGoalUids);

        $this->assertEquals([
            'uid' => $this->rootGoal->uid,
            'description' => $this->rootGoal->description,
            'children' => [
                [
                    'uid' => $firstChild->uid,
                    'description' => $firstChild->description,
                    'children' => [],
                ],
            ],
        ], $goal->toArray());
    }

    #[Test]
    public function it_returns_only_used_children_on_same_level_of_filtered_follow_up_system(): void
    {
        $this->createFollowUpSystemGoal($this->rootGoal);
        $secondLevelChild = $this->createFollowUpSystemGoal($this->rootGoal);

        $shouldFilterFollowUpSystem = true;
        $usedGoalUids = collect([$secondLevelChild->uid => $secondLevelChild->uid]);

        $goal = new ReportFollowUpSystemGoal($this->getTree()->first(), $shouldFilterFollowUpSystem, $usedGoalUids);

        $this->assertEquals([
            'uid' => $this->rootGoal->uid,
            'description' => $this->rootGoal->description,
            'children' => [
                [
                    'uid' => $secondLevelChild->uid,
                    'description' => $secondLevelChild->description,
                    'children' => [],
                ],
            ],
        ], $goal->toArray());
    }

    #[Test]
    public function it_does_not_return_children(): void
    {
        $this->createFollowUpSystemGoal($this->rootGoal);
        $this->createFollowUpSystemGoal($this->rootGoal);

        $shouldFilterFollowUpSystem = true;
        $usedGoalUids = null;

        $goal = new ReportFollowUpSystemGoal($this->getTree()->first(), $shouldFilterFollowUpSystem, $usedGoalUids);

        $this->assertEquals([
            'uid' => $this->rootGoal->uid,
            'description' => $this->rootGoal->description,
            'children' => [],
        ], $goal->toArray());
    }

    #[Test]
    public function it_does_not_return_archived_goals(): void
    {
        $usedArchivedGoal = factory(FollowUpSystemGoal::class)->create([
            'follow_up_system_id' => $this->followUpSystem->id,
            'follow_up_system_level_id' => $this->followUpSystemLevel->id,
            'parent_id' => $this->rootGoal->id,
            'order' => 0,
            'archived_at' => Carbon::now(),
        ]);
        factory(FollowUpSystemGoal::class)->create([
            'follow_up_system_id' => $this->followUpSystem->id,
            'follow_up_system_level_id' => $this->followUpSystemLevel->id,
            'parent_id' => $this->rootGoal->id,
            'order' => 1,
            'archived_at' => Carbon::now(),
        ]);

        $usedGoalUids = collect([$usedArchivedGoal->uid => $usedArchivedGoal->uid]);
        $report = new ReportFollowUpSystemGoal($this->getTree()->first(), false, $usedGoalUids);

        $this->assertEquals([
            'uid' => $this->rootGoal->uid,
            'description' => $this->rootGoal->description,
            'children' => [
                [
                    'uid' => $usedArchivedGoal->uid,
                    'description' => $usedArchivedGoal->description,
                    'children' => [],
                ],
            ],
        ], $report->toArray());
    }

    private function getTree(): Collection
    {
        return app(FollowUpSystemGoalRepositoryInterface::class)
            ->getGoalTreeForFollowUpSystemWithArchived($this->followUpSystem);
    }

    private function createFollowUpSystemGoal(?FollowUpSystemGoal $parent = null): FollowUpSystemGoal
    {
        return factory(FollowUpSystemGoal::class)->create([
            'follow_up_system_id' => $this->followUpSystem->id,
            'follow_up_system_level_id' => $this->followUpSystemLevel->id,
            'parent_id' => optional($parent)->id,
            'order' => 0,
        ]);
    }
}
