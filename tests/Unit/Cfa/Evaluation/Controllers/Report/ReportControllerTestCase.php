<?php

namespace Tests\Unit\Cfa\Evaluation\Controllers\Report;

use Cfa\Care\Domain\CareInfo\PupilStatus;
use Cfa\Common\Application\Services\Helpers\GroupHelperService;
use Cfa\Common\Application\Services\Helpers\SchoolHelperService;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Override;
use Tests\Unit\ControllerTestCase;

abstract class ReportControllerTestCase extends ControllerTestCase
{
    protected ReportPeriod $reportPeriod;

    protected ReportPeriod $yearReportPeriod;

    protected ReportSettings $reportSettings;

    #[Override]
    public function setUp(): void
    {
        parent::setUp();

        $this->schoolyear = app(SchoolyearRepositoryInterface::class)->getCurrent();
        $this->group = Group::factory()
            ->inSchool($this->school)
            ->withNaturalStudyYear(1, TargetAudienceType::Ko)
            ->create([
                'name' => 'groupA',
            ]);

        $this->mock(GroupHelperService::class)->shouldReceive('group')->andReturn($this->group);
        $this->mock(SchoolHelperService::class)->shouldReceive('school')->andReturn($this->school);

        $this->pupil = Pupil::factory()
            ->withActiveCareer($this->group)
            ->withCareInfo($this->school, PupilStatus::Active)
            ->create();
        $this->reportSettings = ReportSettings::factory()->forGroup($this->group)->create([
            'recalculate_tests' => true,
            'recalculate_tests_to' => ReportSettings::DEFAULT_RECALCULATE_TESTS_TO,
            'recalculate_domains_to' => ReportSettings::DEFAULT_RECALCULATE_DOMAINS_TO,
            'recalculate_subjects_to' => ReportSettings::DEFAULT_RECALCULATE_SUBJECTS_TO,
            'recalculate_report_to' => ReportSettings::DEFAULT_RECALCULATE_REPORT_TO,
            'recalculate_synthesis_report_to' => ReportSettings::DEFAULT_RECALCULATE_SYNTHESIS_REPORT_TO,
        ]);
        $this->yearReportPeriod = ReportPeriod::factory()
            ->isYearReport()
            ->forReportSettings($this->reportSettings)
            ->create(['uid' => '36c39636-0ae7-46c4-b021-3ee34c4f047f']);
        $this->reportPeriod = ReportPeriod::factory()
            ->setYearReport($this->yearReportPeriod)
            ->forReportSettings($this->reportSettings)
            ->create(['uid' => '4e4eea96-9440-40d7-a9f5-cdccd462cd05']);

        Career::factory()->forUser($this->owner)->inGroup($this->group)->create();

        $this->setUrlParameters([
            'reportPeriod' => $this->reportPeriod->uid,
            'pupil' => $this->pupil->uid,
        ]);
    }

    protected function it_fails_if_the_text_field_is_too_long(string $attribute, int $limit = 255): void
    {
        $newValue = str_repeat('a', $limit + 1);

        $expectedMessage = ucfirst((string) trans('validation.attributes.' . $attribute))
            . ' mag niet uit meer dan ' . $limit . ' tekens bestaan';
        $this->postJsonWithRequiredHeaders([$attribute => $newValue])
            ->assertJsonValidationErrors([$attribute => $expectedMessage]);
    }

    protected function addPeriod(): ReportPeriod
    {
        return ReportPeriod::factory()
            ->forReportSettings(ReportSettings::factory()->create())
            ->create([
                'uid' => 'db25ea39-23d0-4bb0-9490-deffce80cf50',
                'name' => 'Jaarrapport',
                'is_period' => false,
                'parent_id' => null,
                'start' => '2017-09-01',
                'end' => '2017-12-31',
                'schoolyear_id' => app(SchoolyearRepositoryInterface::class)->getCurrent()->id,
            ]);
    }
}
