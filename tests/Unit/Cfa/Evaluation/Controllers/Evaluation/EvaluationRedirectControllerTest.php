<?php

namespace Tests\Unit\Cfa\Evaluation\Controllers\Evaluation;

use Cfa\Common\Application\Services\Helpers\GroupHelperService;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudience;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\FollowUpSystem\EvaluationPeriod\EvaluationPeriod;
use Cfa\Evaluation\Domain\Settings\EvaluationSubjectPermission\EvaluationSubjectPermission;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriodRepositoryInterface;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Cfa\Evaluation\Domain\Settings\Report\SubjectQuotation\SubjectQuotationSettings;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Factories\Evaluation\EvaluationSubjectFactory;
use Tests\Factories\Evaluation\SubjectPermissionFactory;
use Tests\Factories\Evaluation\SubjectQuotationSettingsFactory;
use Tests\Unit\ControllerTestCase;

use function app;
use function collect;
use function route;

class EvaluationRedirectControllerTest extends ControllerTestCase
{
    protected static ?string $routeName = 'web.evaluation.evaluate';

    protected Subject $subject;

    protected Group $lastSetGroup;

    protected ReportPeriod $reportPeriod;

    #[Override]
    public function setUp(): void
    {
        parent::setUp();

        $targetAudience = TargetAudience::whereNaturalStudyYear(3)->whereType(TargetAudienceType::Lo)->first();
        $this->group = Group::factory()
            ->inSchool($this->school)
            ->withNaturalStudyYear($targetAudience->natural_study_year, $targetAudience->type)
            ->create();
        Pupil::factory()->withActiveCareer($this->group)->create();
        Career::factory()
            ->withRole(RoleName::Teacher)
            ->inGroup($this->group)
            ->forUser($this->owner)
            ->create();

        $reportSettings = ReportSettings::factory()->forGroup($this->group)->create();
        $this->reportPeriod = ReportPeriod::factory()
            ->withYearReportAndReportSettings($reportSettings)
            ->create();

        // Mock the group helper.
        $this->mock(GroupHelperService::class)
            ->makePartial()
            ->shouldReceive('group')
            ->andReturnUsing(function (?Group $group = null) {
                $this->lastSetGroup = $group ?? $this->group;

                return $this->lastSetGroup;
            });

        $this->subject = app(EvaluationSubjectFactory::class)
            ->withReportSettings($reportSettings)
            ->setUid('d6b870a7-ec2c-479b-b020-9defbec091cf')
            ->setName('Maths')
            ->forGroup($this->group)
            ->evaluatedByTeacher($this->owner)
            ->evaluatedWithComment()
            ->create();

        $this->schoolyear = app(SchoolyearRepositoryInterface::class)->getCurrent();
    }

    #[Test]
    public function it_gives_forbidden_when_no_report_periods_are_found(): void
    {
        EvaluationPeriod::withoutEvents(fn(): ?bool => $this->reportPeriod->forceDelete());
        $this->getJsonWithRequiredHeaders()
            ->assertRedirect(route('web.evaluation.evaluate.fallback.report-periods'));
    }

    #[Test]
    public function it_uses_the_report_period_repository_to_get_the_current_or_last_report_period(): void
    {
        $otherReportPeriod = ReportPeriod::factory()
            ->withYearReportAndReportSettings(ReportSettings::factory()->forGroup($this->group)->create())
            ->create([
                'schoolyear_id' => $this->schoolyear->id,
            ]);

        $this->mock(ReportPeriodRepositoryInterface::class)
            ->shouldReceive('getCurrentOrLastReportPeriodForGroup')
            ->once()
            ->andReturn($otherReportPeriod);

        $this->getJsonWithRequiredHeaders()
            ->assertRedirect(route('web.evaluation.evaluate.show', [
                'group' => $this->group->uid,
                'subject' => $this->subject->uid,
                'reportPeriod' => $otherReportPeriod->uid,
            ]));
    }

    #[Test]
    public function it_uses_the_given_report_period(): void
    {
        $reportPeriod = ReportPeriod::factory()
            ->forReportSettings(ReportSettings::factory()->create())
            ->create([
                'school_id' => $this->school->id,
                'schoolyear_id' => $this->schoolyear->id,
            ]);

        $this->setUrlParameters([
            'reportPeriod' => $reportPeriod->uid,
        ]);

        $this->mock(ReportPeriodRepositoryInterface::class)
            ->shouldReceive('getReportPeriodsForGroup')
            ->once()
            ->andReturn(collect([$reportPeriod]))
            ->shouldReceive('getCurrentOrLastReportPeriodForGroup')
            ->never();

        $this->getJsonWithRequiredHeaders()->assertRedirect(route('web.evaluation.evaluate.show', [
            'group' => $this->group->uid,
            'subject' => $this->subject->uid,
            'reportPeriod' => $reportPeriod->uid,
        ]));
    }

    #[Test]
    public function it_does_not_use_the_given_report_period_if_it_is_not_compatible_with_the_group(): void
    {
        $reportPeriod = ReportPeriod::factory()
            ->forReportSettings(ReportSettings::factory()->create())
            ->create([
                'schoolyear_id' => $this->schoolyear->id,
            ]);
        $otherReportPeriod = ReportPeriod::factory()
            ->forReportSettings(ReportSettings::factory()->create())
            ->create([
                'school_id' => $this->school->id,
                'schoolyear_id' => $this->schoolyear->id,
            ]);

        $this->setUrlParameters([
            'reportPeriod' => $reportPeriod->uid,
        ]);

        $this->mock(ReportPeriodRepositoryInterface::class)
            ->shouldReceive('getReportPeriodsForGroup')
            ->once()
            ->andReturn(collect([$otherReportPeriod]))
            ->shouldReceive('getCurrentOrLastReportPeriodForGroup')
            ->once()
            ->andReturn($otherReportPeriod);

        $this->getJsonWithRequiredHeaders()->assertRedirect(route('web.evaluation.evaluate.show', [
            'group' => $this->group->uid,
            'subject' => $this->subject->uid,
            'reportPeriod' => $otherReportPeriod->uid,
        ]));
    }

    #[Test]
    public function it_uses_the_current_group_if_user_has_permission(): void
    {
        $this->getJsonWithRequiredHeaders();

        $this->assertEquals($this->group->id, $this->lastSetGroup->id);
    }

    #[Test]
    public function it_changes_the_group_and_period_when_user_has_no_evaluation_permissions_to_current_group(): void
    {
        EvaluationSubjectPermission::query()->delete();

        $otherGroup = Group::factory()
            ->inSchool($this->school)
            ->withNaturalStudyYear(1, TargetAudienceType::Ko)
            ->create();
        Pupil::factory()->withActiveCareer($otherGroup)->create();
        Career::factory()
            ->withRole(RoleName::Teacher)
            ->inGroup($otherGroup)
            ->forUser($this->owner)
            ->create();

        $newReportSettings = ReportSettings::factory()->forGroup($otherGroup)->create();
        $newReportPeriod = ReportPeriod::factory()->withYearReportAndReportSettings($newReportSettings)->create();

        app(SubjectPermissionFactory::class)
            ->setGroup($otherGroup)
            ->setSubject($this->subject)
            ->forTeacher($this->owner)
            ->create();
        app(SubjectQuotationSettingsFactory::class)
            ->setReportSettings($newReportSettings)
            ->setGroup($otherGroup)
            ->setSubject($this->subject)
            ->setCommentable()
            ->create();

        $this->getJsonWithRequiredHeaders()
            ->assertRedirect(route('web.evaluation.evaluate.show', [
                'group' => $otherGroup->uid,
                'subject' => $this->subject->uid,
                'reportPeriod' => $newReportPeriod->uid,
            ]));

        $this->assertEquals($otherGroup->id, $this->lastSetGroup->id);
    }

    #[Test]
    public function it_changes_the_group_and_period_when_the_group_has_no_valid_quotation_settings(): void
    {
        SubjectQuotationSettings::query()->forceDelete();

        $otherGroup = Group::factory()
            ->inSchool($this->school)
            ->withNaturalStudyYear(2, TargetAudienceType::Lo)
            ->create();
        Pupil::factory()->withActiveCareer($otherGroup)->create();
        Career::factory()
            ->withRole(RoleName::Teacher)
            ->inGroup($otherGroup)
            ->forUser($this->owner)
            ->create();
        $reportSettings = ReportSettings::factory()->forGroup($otherGroup)->create();
        $newReportPeriod = ReportPeriod::factory()
            ->withYearReportAndReportSettings($reportSettings)
            ->create();

        app(SubjectPermissionFactory::class)
            ->setGroup($otherGroup)
            ->setSubject($this->subject)
            ->forTeacher($this->owner)
            ->create();

        app(SubjectQuotationSettingsFactory::class)
            ->setReportSettings(
                ReportSettings::factory()
                    ->forGroup($this->group)
                    ->forSchoolyear(Schoolyear::getRepository()->getPrevious())
                    ->create(),
            )
            ->setGroup($this->group)
            ->setSubject($this->subject)
            ->setCommentable()
            ->create();
        app(SubjectQuotationSettingsFactory::class)
            ->setReportSettings($reportSettings)
            ->setGroup($otherGroup)
            ->setSubject($this->subject)
            ->setCommentable()
            ->create();

        $this->getJsonWithRequiredHeaders()
            ->assertRedirect(route(
                'web.evaluation.evaluate.show',
                [
                    'group' => $otherGroup->uid,
                    'subject' => $this->subject->uid,
                    'reportPeriod' => $newReportPeriod->uid,
                ],
            ));

        $this->assertEquals($otherGroup->id, $this->lastSetGroup->id);
    }

    #[Test]
    public function it_gives_forbidden_when_user_has_no_permissions_to_evaluate(): void
    {
        EvaluationSubjectPermission::query()->delete();

        $this->getJsonWithRequiredHeaders()
            ->assertRedirect(route('web.evaluation.evaluate.fallback.permissions'));
    }

    #[Test]
    public function it_gives_forbidden_when_user_has_no_settings_to_evaluate(): void
    {
        SubjectQuotationSettings::query()->delete();

        $this->getJsonWithRequiredHeaders()
            ->assertRedirect(route('web.evaluation.evaluate.fallback.permissions'));
    }

    #[Test]
    public function it_gives_forbidden_when_user_has_no_settings_to_evaluate_in_the_current_year(): void
    {
        SubjectQuotationSettings::query()
            ->update([
                'report_settings_id' => ReportSettings::factory()
                    ->forGroup($this->group)
                    ->forSchoolyear(Schoolyear::getRepository()->getPrevious())
                    ->create()
                    ->id,
            ]);

        $this->getJsonWithRequiredHeaders()
            ->assertRedirect(route('web.evaluation.evaluate.fallback.permissions'));
    }
}
