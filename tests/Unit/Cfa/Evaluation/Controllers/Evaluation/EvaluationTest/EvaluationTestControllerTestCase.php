<?php

namespace Tests\Unit\Cfa\Evaluation\Controllers\Evaluation\EvaluationTest;

use Cfa\Common\Application\Services\Helpers\GroupHelperService;
use Cfa\Common\Application\Services\Helpers\SchoolHelperService;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Evaluation\Domain\Settings\EvaluationSubjectPermission\EvaluationSubjectPermission;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Cfa\Evaluation\Domain\Settings\Report\SubjectQuotation\SubjectQuotationSettings;
use Override;
use Tests\Traits\WithSeeding;
use Tests\Unit\ControllerTestCase;

class EvaluationTestControllerTestCase extends ControllerTestCase
{
    use WithSeeding;

    /**
     * The subject.
     *
     * @var Subject
     */
    protected $subject;

    /**
     * The report period.
     *
     * @var ReportPeriod
     */
    protected $reportPeriod;

    /** @var SubjectQuotationSettings */
    protected $quotationSettings;

    protected int $numberOfPupils = 1;

    /**
     * {@inheritdoc}
     */
    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->setUpTeacherWithGroupsAndPupilsInCurrentSchoolyear(1, $this->numberOfPupils);

        $this->subject = Subject::factory()->create([
            'school_id' => $this->school->id,
        ]);
        $this->group->natural_study_year = 3;
        $this->group->target_audience_type = TargetAudienceType::Lo;
        $this->group->save();
        $reportSettings = ReportSettings::factory()->forGroup($this->group)->create();
        $this->reportPeriod = ReportPeriod::factory()
            ->withYearReportAndReportSettings($reportSettings)
            ->create();

        EvaluationSubjectPermission::factory()
            ->setIsClassTeacher(true)
            ->forSubject($this->subject)
            ->forGroup($this->group)
            ->create();

        $this->quotationSettings = factory(SubjectQuotationSettings::class)->create([
            'subject_id' => $this->subject->id,
            'school_id' => $this->school->id,
            'group_id' => $this->group->id,
            'report_settings_id' => $reportSettings->id,
            'weight_percentage' => 90,
            'has_score' => true,
        ]);

        $this->mock(GroupHelperService::class)->makePartial()
            ->shouldReceive('group')
            ->andReturn($this->group);

        $this->mock(SchoolHelperService::class)->makePartial()
            ->shouldReceive('school')
            ->andReturn($this->school);

        $this->setUrlParameters([
            'subject' => $this->subject,
            'reportPeriod' => $this->reportPeriod,
        ]);
    }
}
