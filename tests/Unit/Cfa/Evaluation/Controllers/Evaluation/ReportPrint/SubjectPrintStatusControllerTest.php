<?php

namespace Tests\Unit\Cfa\Evaluation\Controllers\Evaluation\ReportPrint;

use App\Services\File\FilePathService;
use Carbon\Carbon;
use Cfa\Common\Application\Exports\ExportJobStatus;
use Cfa\Common\Application\Services\CloudFront\CloudFrontUrlService;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Evaluation\Domain\Report\EvaluationPrintJob\EvaluationPrintJob;
use Cfa\Evaluation\Domain\Report\EvaluationPrintJob\EvaluationPrintJobType;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\Cfa\Evaluation\Controllers\Report\ReportControllerTestCase;

class SubjectPrintStatusControllerTest extends ReportControllerTestCase
{
    protected static ?string $routeName = 'web.evaluation.print.subject-print-status';

    private Subject $subject;

    #[Override]
    public function setUp(): void
    {
        parent::setUp();

        $this->subject = Subject::factory()->inSchool($this->school)->create();
        $this->setUrlParameters([
            'reportPeriod' => $this->reportPeriod->uid,
            'subject' => $this->subject->uid,
        ]);
    }

    #[Test]
    public function it_returns_non_existing_when_no_job_is_registered_yet(): void
    {
        $response = $this->getJsonWithRequiredHeaders();

        $response->assertJson(
            [
                'status' => ExportJobStatus::NotExisting->value,
                'file' => null,
            ],
        );
    }

    #[Test]
    public function it_returns_queued_when_a_job_is_registered(): void
    {
        $this->createEvaluationPrintJob([
            'group_id' => $this->group->id,
            'report_period_id' => $this->reportPeriod->id,
            'subject_id' => $this->subject->id,
            'type' => EvaluationPrintJobType::subjectOverview,
        ]);

        $response = $this->getJsonWithRequiredHeaders();

        $response->assertJson(
            [
                'status' => ExportJobStatus::Queued->value,
                'file' => null,
            ],
        );
    }

    #[Test]
    public function it_returns_the_report_through_cloudfront_if_the_file_has_been_set(): void
    {
        $filePathService = $this->mock(FilePathService::class);
        $filePathService->shouldReceive('getStoragePathForFile')->andReturnValues(
            [
                'mySubjectReport.pdf',
            ],
        );

        $this->createEvaluationPrintJob([
            'group_id' => $this->group->id,
            'report_period_id' => $this->reportPeriod->id,
            'subject_id' => $this->subject->id,
            'type' => EvaluationPrintJobType::subjectOverview,
            'status' => ExportJobStatus::Completed,
            'file' => 'mySubjectReport.pdf',
        ]);

        $this->mock(CloudFrontUrlService::class)
            ->shouldReceive('getUrl')
            ->andReturnUsing(
                fn(?string $path): ?string => $path ? 'https://tms-cdn.int.tms.bingel.be/' . trim($path, '/') : $path,
            )
            ->atLeast()
            ->once();

        $response = $this->getJsonWithRequiredHeaders();

        $response->assertJson(
            [
                'status' => ExportJobStatus::Completed->value,
                'file' => 'https://tms-cdn.int.tms.bingel.be/mySubjectReport.pdf',
            ],
        );
    }

    private function createEvaluationPrintJob(array $attributes = []): void
    {
        EvaluationPrintJob::forceCreate(
            [
                'pupil_id' => $this->pupil->id,
                'school_id' => $this->school->id,
                'group_id' => $this->group->id,
                'report_period_id' => $this->reportPeriod->id,
                'creator_id' => $this->owner->id,
                'created_at' => Carbon::now(),
                'status' => ExportJobStatus::Queued,
                'type' => EvaluationPrintJobType::default,
                ...$attributes,
            ],
        );
    }
}
