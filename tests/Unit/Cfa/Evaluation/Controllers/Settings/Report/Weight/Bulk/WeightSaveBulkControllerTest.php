<?php

namespace Tests\Unit\Cfa\Evaluation\Controllers\Settings\Report\Weight\Bulk;

use Carbon\Carbon;
use Cfa\Common\Application\Repositories\SettingsGroupRepository;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudience;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Evaluation\Application\Controllers\Settings\Report\Weight\WeightSaveRequest;
use Cfa\Evaluation\Application\Services\Report\EvaluationReportJobsService;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Cfa\Evaluation\Domain\Settings\Report\SubjectQuotation\SubjectQuotationSettings;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Factories\Evaluation\SubjectQuotationSettingsFactory;
use Tests\Unit\Cfa\Evaluation\Controllers\Settings\Report\Weight\WeightControllerTestCase;

use function factory;
use function uuid;

class WeightSaveBulkControllerTest extends WeightControllerTestCase
{
    protected static ?string $routeName =
        'web.settings.evaluation.reports.report-setting.quotation-settings.bulk.weight';

    /** @var Group */
    protected $secondGroup;

    protected ReportSettings $reportSettings;

    #[Override]
    protected function setUp(): void
    {
        $this->setTestNow('2020-06-22 15:30:00');

        parent::setUp();

        $this->secondGroup = Group::factory()->inSchool($this->school)->create();

        $group = Group::factory()->has(School::factory())->create();

        $this->reportSettings = ReportSettings::getRepository()->getForGroup($group);
        $this->reportSettings->save();

        $this->setUrlParameters([
            'subject' => $this->subject,
            'reportSettings' => $this->reportSettings->uid,
        ]);
    }

    #[Test]
    public function it_creates_new_subject_quotation_settings(): void
    {
        $this->assertEvaluationReportJobServiceIsCalled();

        $this->mock(SettingsGroupRepository::class)
            ->shouldReceive('getGroupsByTargetAudiences')
            ->once()
            ->andReturn(collect([$this->secondGroup, $this->group]));

        $this->assertEmpty(SubjectQuotationSettings::all());

        $attributes = [
            'subject_id' => $this->subject->id,
            'school_id' => $this->school->id,
            'group_id' => $this->group->id,
            'report_settings_id' => $this->reportSettings->id,
            'weight_percentage' => 20,
            'weight_fraction_numerator' => null,
            'weight_fraction_denominator' => null,
            'creator_id' => $this->ictAdmin->id,
            'updater_id' => $this->ictAdmin->id,
            'created_at' => '2020-06-22 15:30:00',
            'updated_at' => '2020-06-22 15:30:00',
        ];

        $reportSettingsLastYear = ReportSettings::factory()
            ->forGroup($this->group)
            ->forSchoolyear(Schoolyear::getRepository()->getPrevious())
            ->create();
        SubjectQuotationSettings::withoutEvents(
            fn() => SubjectQuotationSettings::forceCreate([
                ...$attributes,
                'uid' => uuid(),
                'report_settings_id' => $reportSettingsLastYear->id,
            ]),
        );

        $this->postJsonWithRequiredHeaders($this->getData())->assertNoContent();

        $this->assertDatabaseHas('subject_quotation_settings', $attributes);
        $this->assertDatabaseHas(
            'subject_quotation_settings',
            [...$attributes, 'group_id' => $this->secondGroup->id],
        );
        $this->assertDatabaseHas(
            'subject_quotation_settings',
            [...$attributes, 'report_settings_id' => $reportSettingsLastYear->id],
        );
    }

    #[Test]
    public function it_updates_existing_subject_quotation_setting(): void
    {
        $this->assertEvaluationReportJobServiceIsCalled();

        $this->mock(SettingsGroupRepository::class)
            ->shouldReceive('getGroupsByTargetAudiences')
            ->once()
            ->andReturn(collect([$this->group, $this->secondGroup]));

        $dispatcher = SubjectQuotationSettings::getEventDispatcher();
        SubjectQuotationSettings::unsetEventDispatcher();

        // Create one for the first group, that will be updated.
        factory(SubjectQuotationSettings::class)->create([
            'uid' => uuid(),
            'subject_id' => $this->subject->id,
            'group_id' => $this->group->id,
            'school_id' => $this->school->id,
            'report_settings_id' => $this->reportSettings->id,
            'weight_percentage' => 50,
            'weight_fraction_numerator' => null,
            'weight_fraction_denominator' => null,
            'creator_id' => $this->owner->id,
            'updater_id' => $this->owner->id,
            'created_at' => '2020-06-22 15:00:00',
        ]);

        SubjectQuotationSettings::setEventDispatcher($dispatcher);
        $this->postJsonWithRequiredHeaders($this->getData())->assertNoContent();

        $this->assertCount(2, SubjectQuotationSettings::all());
        $this->assertDatabaseHas('subject_quotation_settings', [
            'subject_id' => $this->subject->id,
            'school_id' => $this->school->id,
            'group_id' => $this->group->id,
            'report_settings_id' => $this->reportSettings->id,
            'weight_percentage' => 20,
            'weight_fraction_numerator' => null,
            'weight_fraction_denominator' => null,
            'creator_id' => $this->owner->id,
            'updater_id' => $this->ictAdmin->id,
            'created_at' => '2020-06-22 15:00:00',
            'updated_at' => '2020-06-22 15:30:00',
        ]);

        $this->assertDatabaseHas('subject_quotation_settings', [
            'subject_id' => $this->subject->id,
            'school_id' => $this->school->id,
            'group_id' => $this->secondGroup->id,
            'report_settings_id' => $this->reportSettings->id,
            'weight_percentage' => 20,
            'weight_fraction_numerator' => null,
            'weight_fraction_denominator' => null,
            'creator_id' => $this->ictAdmin->id,
            'updater_id' => $this->ictAdmin->id,
            'created_at' => '2020-06-22 15:30:00',
            'updated_at' => '2020-06-22 15:30:00',
        ]);
    }

    #[Test]
    public function it_allows_resetting_weight_to_null(): void
    {
        $this->assertEvaluationReportJobServiceIsCalled();

        $this->mock(SettingsGroupRepository::class)
            ->shouldReceive('getGroupsByTargetAudiences')
            ->once()
            ->andReturn(collect([$this->group, $this->secondGroup]));

        $dispatcher = SubjectQuotationSettings::getEventDispatcher();
        SubjectQuotationSettings::unsetEventDispatcher();

        // We set ALL Weight attributes to check if they are all reset. Outside testing only percentage
        // or fractions are supported, not both at the same time.
        factory(SubjectQuotationSettings::class)->create([
            'uid' => uuid(),
            'subject_id' => $this->subject->id,
            'group_id' => $this->group->id,
            'school_id' => $this->school->id,
            'report_settings_id' => $this->reportSettings->id,
            'weight_percentage' => 50,
            'weight_fraction_numerator' => 20,
            'weight_fraction_denominator' => 60,
            'creator_id' => $this->owner->id,
            'updater_id' => $this->owner->id,
            'created_at' => '2020-06-22 15:00:00',
        ]);

        SubjectQuotationSettings::setEventDispatcher($dispatcher);
        $this->postJsonWithRequiredHeaders([])->assertNoContent();

        $this->assertCount(2, SubjectQuotationSettings::all());
        $this->assertDatabaseHas('subject_quotation_settings', [
            'subject_id' => $this->subject->id,
            'school_id' => $this->school->id,
            'group_id' => $this->group->id,
            'report_settings_id' => $this->reportSettings->id,
            'weight_percentage' => null,
            'weight_fraction_numerator' => null,
            'weight_fraction_denominator' => null,
            'creator_id' => $this->owner->id,
            'updater_id' => $this->ictAdmin->id,
            'created_at' => '2020-06-22 15:00:00',
            'updated_at' => '2020-06-22 15:30:00',
        ]);

        $this->assertDatabaseHas('subject_quotation_settings', [
            'subject_id' => $this->subject->id,
            'school_id' => $this->school->id,
            'group_id' => $this->secondGroup->id,
            'report_settings_id' => $this->reportSettings->id,
            'weight_percentage' => null,
            'weight_fraction_numerator' => null,
            'weight_fraction_denominator' => null,
            'creator_id' => $this->ictAdmin->id,
            'updater_id' => $this->ictAdmin->id,
            'created_at' => '2020-06-22 15:30:00',
            'updated_at' => '2020-06-22 15:30:00',
        ]);
    }

    #[Test]
    public function it_does_not_update_subject_quotation_settings_of_other_subject(): void
    {
        $this->assertEvaluationReportJobServiceIsCalled();

        $this->mock(SettingsGroupRepository::class)
            ->shouldReceive('getGroupsByTargetAudiences')
            ->once()
            ->andReturn(collect([$this->group]));

        $otherSubject = Subject::factory()->create([
            'school_id' => $this->school->id,
        ]);

        $subjectQuotationSetting = factory(SubjectQuotationSettings::class)->create([
            'subject_id' => $otherSubject->id,
            'group_id' => $this->group->id,
            'school_id' => $this->school->id,
            'report_settings_id' => $this->reportSettings->id,
            'weight_percentage' => 50,
            'weight_fraction_numerator' => null,
            'weight_fraction_denominator' => null,
        ]);

        $this->postJsonWithRequiredHeaders($this->getData())->assertNoContent();

        $this->assertEquals(50, $subjectQuotationSetting->fresh()->weight_percentage);
    }

    #[Test]
    public function it_does_not_update_subject_quotation_settings_of_a_deleted_entity(): void
    {
        $this->assertEvaluationReportJobServiceIsCalled();

        $this->mock(SettingsGroupRepository::class)
            ->shouldReceive('getGroupsByTargetAudiences')
            ->once()
            ->andReturn(collect([$this->group]));

        $otherSubject = Subject::factory()->create([
            'school_id' => $this->school->id,
        ]);

        $deletedSubjectQuotationSetting = factory(SubjectQuotationSettings::class)->create([
            'subject_id' => $otherSubject->id,
            'group_id' => $this->group->id,
            'school_id' => $this->school->id,
            'report_settings_id' => $this->reportSettings->id,
            'weight_percentage' => 50,
            'weight_fraction_numerator' => null,
            'weight_fraction_denominator' => null,
            'deleted_at' => Carbon::now(),
        ]);

        $this->postJsonWithRequiredHeaders($this->getData())->assertNoContent();

        $this->assertEquals(2, SubjectQuotationSettings::withTrashed()->count());

        $this->assertEquals(50, $deletedSubjectQuotationSetting->refresh()->weight_percentage);
        $this->assertNotNull($deletedSubjectQuotationSetting->deleted_at);
    }

    #[Test]
    public function it_uses_the_group_repository_to_fetch_all_class_groups(): void
    {
        $this->mock(SettingsGroupRepository::class)
            ->shouldReceive('getGroupsByTargetAudiences')
            ->once()
            ->andReturn(collect([]));

        $this->postJsonWithRequiredHeaders($this->getData())->assertNoContent();
    }

    #[Test]
    public function it_uses_the_group_repository_to_fetch_class_groups_limited_by_target_audiences(): void
    {
        $this->mock(SettingsGroupRepository::class)
            ->shouldReceive('getGroupsByTargetAudiences')
            ->once()
            ->andReturn(collect([]));

        $this->postJsonWithRequiredHeaders($this->getData())->assertNoContent();
    }

    #[Test]
    public function it_does_update_learning_area_when_it_has_a_domain_with_evaluable_subject_quotation_settings(): void
    {
        $this->assertEvaluationReportJobServiceIsCalled();

        $this->mock(SettingsGroupRepository::class)
            ->shouldReceive('getGroupsByTargetAudiences')
            ->once()
            ->andReturn(collect([$this->group]));

        $domain = Subject::factory()->create([
            'school_id' => $this->school->id,
            'parent_id' => $this->subject->id,
            'archived_at' => null,
        ]);

        app(SubjectQuotationSettingsFactory::class)
            ->setReportSettings($this->reportSettings)
            ->setGroup($this->group)
            ->setSubject($domain)
            ->evaluateUsingScores()
            ->create();

        $this->postJsonWithRequiredHeaders($this->getData())->assertNoContent();

        $this->assertEquals(2, SubjectQuotationSettings::count());

        $newSubjectQuotationSetting = SubjectQuotationSettings::where('subject_id', $this->subject->id)->sole();
        $this->assertEquals(20, $newSubjectQuotationSetting->weight_percentage);
        $this->assertEquals($this->school->id, $newSubjectQuotationSetting->school_id);
        $this->assertEquals($this->group->id, $newSubjectQuotationSetting->group_id);
    }

    #[Test]
    public function it_updates_existing_subject_quotations_for_groups_limited_by_target_audiences(): void
    {
        $targetAudienceLo = TargetAudience::where('type', TargetAudienceType::Lo)->first();
        $this->reportSettings->targetAudiences()->sync([$targetAudienceLo->id]);

        $groupLO = Group::factory()->inSchool($this->school)->create([
            'target_audience_type' => $targetAudienceLo->type,
            'natural_study_year' => $targetAudienceLo->natural_study_year,
        ]);
        $this->group->target_audience_type = $targetAudienceLo->type;
        $this->group->natural_study_year = $targetAudienceLo->natural_study_year;
        $this->group->save();

        $targetAudienceKO = TargetAudience::where('type', TargetAudienceType::Ko)->first();
        $groupKO = Group::factory()->inSchool($this->school)->create([
            'target_audience_type' => $targetAudienceKO->type,
            'natural_study_year' => $targetAudienceKO->natural_study_year,
        ]);

        $schoolyear = Schoolyear::getRepository()->getCurrent();
        Career::factory()
            ->forUser($this->owner)
            ->inGroup($this->group)
            ->withRole(RoleName::Teacher)
            ->setStartDate($schoolyear->start)
            ->setEndDate(null)
            ->create();
        Career::factory()
            ->forUser($this->owner)
            ->inGroup($groupLO)
            ->withRole(RoleName::Teacher)
            ->setStartDate($schoolyear->start)
            ->setEndDate(null)
            ->create();
        Career::factory()
            ->forUser($this->owner)
            ->inGroup($groupKO)
            ->withRole(RoleName::Teacher)
            ->setStartDate($schoolyear->start)
            ->setEndDate(null)
            ->create();
        $this->owner->refresh();

        $this->assertEvaluationReportJobServiceIsCalled();

        $dispatcher = SubjectQuotationSettings::getEventDispatcher();
        SubjectQuotationSettings::unsetEventDispatcher();

        // Create one for the first group, that will be updated.
        factory(SubjectQuotationSettings::class)->create([
            'uid' => uuid(),
            'subject_id' => $this->subject->id,
            'group_id' => $this->group->id,
            'school_id' => $this->school->id,
            'report_settings_id' => $this->reportSettings->id,
            'weight_percentage' => 50,
            'weight_fraction_numerator' => null,
            'weight_fraction_denominator' => null,
            'creator_id' => $this->owner->id,
            'updater_id' => $this->owner->id,
            'created_at' => '2020-06-22 15:00:00',
        ]);

        SubjectQuotationSettings::setEventDispatcher($dispatcher);
        $this->postJsonWithRequiredHeaders($this->getData())->assertNoContent();

        $this->assertCount(2, SubjectQuotationSettings::all());
        $this->assertDatabaseHas('subject_quotation_settings', [
            'subject_id' => $this->subject->id,
            'school_id' => $this->school->id,
            'group_id' => $this->group->id,
            'report_settings_id' => $this->reportSettings->id,
            'weight_percentage' => 20,
            'weight_fraction_numerator' => null,
            'weight_fraction_denominator' => null,
            'creator_id' => $this->owner->id,
            'updater_id' => $this->ictAdmin->id,
            'created_at' => '2020-06-22 15:00:00',
            'updated_at' => '2020-06-22 15:30:00',
        ]);

        $this->assertDatabaseHas('subject_quotation_settings', [
            'subject_id' => $this->subject->id,
            'school_id' => $this->school->id,
            'report_settings_id' => $this->reportSettings->id,
            'group_id' => $groupLO->id,
            'weight_percentage' => 20,
            'weight_fraction_numerator' => null,
            'weight_fraction_denominator' => null,
            'creator_id' => $this->ictAdmin->id,
            'updater_id' => $this->ictAdmin->id,
            'created_at' => '2020-06-22 15:30:00',
            'updated_at' => '2020-06-22 15:30:00',
        ]);
    }

    private function assertEvaluationReportJobServiceIsCalled(): void
    {
        $this->mock(EvaluationReportJobsService::class)
            ->shouldReceive('createForSchool')
            ->withArgs(fn(School $school): bool => $school->uid === $this->school->uid)
            ->once();
    }

    #[Override]
    protected function getData(): array
    {
        return [
            WeightSaveRequest::WEIGHT_PERCENTAGE => 20,
            WeightSaveRequest::WEIGHT_FRACTION_NUMERATOR => null,
            WeightSaveRequest::WEIGHT_FRACTION_DENOMINATOR => null,
        ];
    }
}
