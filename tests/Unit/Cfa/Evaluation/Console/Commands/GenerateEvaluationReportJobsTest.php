<?php

namespace Tests\Unit\Cfa\Evaluation\Console\Commands;

use Cfa\Common\Application\Exports\ExportJobStatus;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Evaluation\Application\Console\Commands\GenerateEvaluationReportJobs;
use Cfa\Evaluation\Application\Jobs\ProcessEvaluationReportJob;
use Cfa\Evaluation\Domain\Report\Calculation\EvaluationReportJob;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Queue;
use Tests\Unit\CommandTestCase;

class GenerateEvaluationReportJobsTest extends CommandTestCase
{
    private Group $group;

    private School $school;

    private Schoolyear $schoolyear;

    private ReportPeriod $reportPeriod;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->school = School::factory()->create();
        $this->group = Group::factory()->inSchool($this->school)->create();
        $this->schoolyear = Schoolyear::getRepository()->getCurrent();
        $this->reportPeriod = ReportPeriod::factory()
            ->inSchool($this->school)
            ->forReportSettings(ReportSettings::factory()->forGroup($this->group)->create())
            ->create();
    }

    #[Test]
    public function it_does_not_create_a_process_evaluation_report_job_when_setting_is_not_active(): void
    {
        EvaluationReportJob::forceCreate(
            [
                'status' => ExportJobStatus::NotExisting,
                'school_id' => $this->school->id,
                'group_id' => $this->group->id,
                'report_period_id' => $this->reportPeriod->id,
            ],
        );
        Queue::fake();
        $this->artisan(GenerateEvaluationReportJobs::class, []);
        Queue::assertNotPushed(ProcessEvaluationReportJob::class);
    }

    #[Test]
    public function it_creates_a_process_evaluation_report_job_when_setting_is_active(): void
    {
        ReportSettings::factory()->inSchool($this->school)->create(['show_synthesis_average' => true]);

        EvaluationReportJob::forceCreate(
            [
                'status' => ExportJobStatus::NotExisting,
                'school_id' => $this->school->id,
                'group_id' => $this->group->id,
                'report_period_id' => $this->reportPeriod->id,
            ],
        );
        Queue::fake();
        $this->artisan(GenerateEvaluationReportJobs::class, []);
        Queue::assertPushed(ProcessEvaluationReportJob::class);
    }

    #[Test]
    public function it_updates_the_evaluation_report_job_status_after_queueing(): void
    {
        ReportSettings::factory()->inSchool($this->school)->create(['show_synthesis_average' => true]);

        $evaluationReportJob = EvaluationReportJob::forceCreate(
            [
                'status' => ExportJobStatus::NotExisting,
                'school_id' => $this->school->id,
                'group_id' => $this->group->id,
                'report_period_id' => $this->reportPeriod->id,
            ],
        );
        Queue::fake();
        $this->artisan(GenerateEvaluationReportJobs::class, []);
        Queue::assertPushed(ProcessEvaluationReportJob::class);
        $result = EvaluationReportJob::find($evaluationReportJob->id);
        $this->assertEquals(ExportJobStatus::Queued->value, $result->status->value);
    }

    #[Test]
    public function it_does_not_create_a_new_job_for_a_queued_job(): void
    {
        ReportSettings::factory()->inSchool($this->school)->create(['show_synthesis_average' => true]);

        EvaluationReportJob::forceCreate(
            [
                'status' => ExportJobStatus::Queued,
                'school_id' => $this->school->id,
                'group_id' => $this->group->id,
                'report_period_id' => $this->reportPeriod->id,
            ],
        );
        Queue::fake();
        $this->artisan(GenerateEvaluationReportJobs::class, []);
        Queue::assertNotPushed(ProcessEvaluationReportJob::class);
    }
}
