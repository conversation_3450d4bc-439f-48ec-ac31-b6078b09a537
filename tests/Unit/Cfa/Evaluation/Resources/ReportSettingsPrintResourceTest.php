<?php

namespace Tests\Unit\Cfa\Evaluation\Resources;

use Cfa\Common\Domain\School\School;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettingsPrintResource;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\UnitTestCase;

class ReportSettingsPrintResourceTest extends UnitTestCase
{
    protected School $school;

    protected ReportSettings $reportSettings;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->school = School::factory()->inKathOndVla()->create();

        $this->reportSettings = ReportSettings::factory()->inSchool($this->school)->create();
    }

    #[Test]
    public function it_returns_the_correct_items(): void
    {
        $resource = new ReportSettingsPrintResource($this->reportSettings)->resolve();

        $this->assertEquals('background-blue white', $resource['primary_background_color']);
        $this->assertEquals('background-green white', $resource['secondary_background_color']);
        $this->assertEquals('border-blue', $resource['primary_border_color']);
        $this->assertEquals('border-green', $resource['secondary_border_color']);
        $this->assertEquals('blue', $resource['primaryColor']);
        $this->assertEquals('green', $resource['secondaryColor']);
        $this->assertEquals('blue', $resource['icon_color']);
        $this->assertNull($resource['logo']);
    }

    #[Test]
    public function it_makes_primary_color_empty_when_it_is_a_light_color(): void
    {
        $this->reportSettings->primary_color = 'blue lighten-4';
        $resource = new ReportSettingsPrintResource($this->reportSettings)->resolve();

        $this->assertEquals('background-blue-lighten-4', $resource['primary_background_color']);
        $this->assertEquals('background-green white', $resource['secondary_background_color']);
        $this->assertEquals('border-blue-lighten-4', $resource['primary_border_color']);
        $this->assertEquals('border-green', $resource['secondary_border_color']);
        $this->assertEquals('', $resource['primaryColor']);
        $this->assertEquals('green', $resource['secondaryColor']);
        $this->assertEquals('blue-lighten-4', $resource['icon_color']);
    }

    #[Test]
    public function it_makes_secondary_color_empty_when_it_is_a_light_color(): void
    {
        $this->reportSettings->secondary_color = 'light-green';
        $resource = new ReportSettingsPrintResource($this->reportSettings)->resolve();

        $this->assertEquals('background-blue white', $resource['primary_background_color']);
        $this->assertEquals('background-light-green', $resource['secondary_background_color']);
        $this->assertEquals('border-blue', $resource['primary_border_color']);
        $this->assertEquals('border-light-green', $resource['secondary_border_color']);
        $this->assertEquals('blue', $resource['primaryColor']);
        $this->assertEquals('', $resource['secondaryColor']);
        $this->assertEquals('blue', $resource['icon_color']);
    }
}
