<?php

namespace Tests\Unit\Cfa\Evaluation\Policies;

use Carbon\Carbon;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Cfa\Evaluation\Application\Policies\FollowUpSystemInputMomentPolicy;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSource;
use Cfa\Evaluation\Domain\FollowUpSystem\InputMoment\FollowUpSystemInputMoment;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\PolicyTestCase;

class FollowUpSystemInputMomentQuotatePolicyTest extends PolicyTestCase
{
    protected string $policyClassName = FollowUpSystemInputMomentPolicy::class;

    private ReportPeriod $reportPeriod;

    private FollowUpSystem $followUpSystem;

    private FollowUpSystemInputMoment $followUpSystemInputMoment;

    #[Override]
    public function setUp(): void
    {
        parent::setUp();

        $this->reportPeriod = ReportPeriod::factory()
            ->forReportSettings(ReportSettings::factory()->create())
            ->create([
                'start' => Schoolyear::getRepository()->getCurrent()->start,
                'end' => Carbon::now(),
                'schoolyear_id' => app(SchoolyearRepositoryInterface::class)->getCurrent()->id,
            ]);

        $this->followUpSystem = factory(FollowUpSystem::class)->create([
            'school_id' => $this->school->id,
            'source' => FollowUpSystemSource::Free,
        ]);

        $this->followUpSystemInputMoment = factory(FollowUpSystemInputMoment::class)->create([
            'follow_up_system_id' => $this->followUpSystem->id,
            'group_id' => $this->group->id,
            'report_period_id' => $this->reportPeriod->id,
            'date' => $this->reportPeriod->start->addDays(5),
        ]);
    }

    #[Override]
    #[Test]
    public function it_returns_false_when_access_is_denied(): void
    {
        $this->followUpSystemInputMoment->date = $this->reportPeriod->start->subYear();
        $this->followUpSystemInputMoment->save();

        $this->assertFalse($this->policy->quotate($this->owner, $this->followUpSystemInputMoment));
    }

    #[Test]
    public function it_returns_false_when_report_period_is_expired(): void
    {
        $previousSchoolyear = Schoolyear::getRepository()->getPrevious();

        $this->reportPeriod->start = $previousSchoolyear->start;
        $this->reportPeriod->end = $previousSchoolyear->end;
        $this->reportPeriod->save();

        $this->assertFalse($this->policy->quotate($this->owner, $this->followUpSystemInputMoment));
    }

    #[Override]
    #[Test]
    public function it_returns_true_when_access_is_granted(): void
    {
        $this->assertTrue($this->policy->quotate($this->owner, $this->followUpSystemInputMoment));
    }
}
