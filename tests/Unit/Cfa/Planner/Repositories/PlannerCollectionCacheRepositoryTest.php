<?php

namespace Tests\Unit\Cfa\Planner\Repositories;

use App\Exceptions\System\NotImplementedException;
use Carbon\Carbon;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Application\Repositories\PlannerCollectionCacheRepository;
use Cfa\Planner\Application\Repositories\UserCollectionCacheRepository;
use Cfa\Planner\Domain\Collection\PlannerCollectionRepositoryInterface;
use Cfa\Planner\Domain\Collection\PublisherCollection\PublisherCollection;
use Cfa\Planner\Domain\Collection\UserCollection\UserCollection;
use Cfa\Planner\Domain\Collection\UserCollection\UserCollectionRepositoryInterface;
use Override;
use PHPUnit\Framework\Attributes\Test;
use ReflectionMethod;
use Tests\Cache\CacheTester;
use Tests\Unit\CacheRepositoryTestCase;

use function app;
use function factory;

class PlannerCollectionCacheRepositoryTest extends CacheRepositoryTestCase
{
    /**
     * {@inheritdoc}
     *
     * @var string.
     */
    protected $repositoryInterfaceClassName = PlannerCollectionRepositoryInterface::class;

    /**
     * {@inheritdoc}
     *
     * @var string.
     */
    protected $cacheRepositoryClassName = PlannerCollectionCacheRepository::class;

    /**
     * {@inheritdoc}
     *
     * @var PlannerCollectionRepositoryInterface
     */
    protected $repository;

    /**
     * The user collection to cache.
     *
     * @var UserCollection
     */
    private $usercollection;

    /**
     * The publisher collection to cache.
     *
     * @var PublisherCollection
     */
    private $publisherCollection;

    /**
     * {@inheritdoc}
     */
    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->usercollection = factory(UserCollection::class)
            ->states('nonArchived')
            ->create([
                'owner_id' => $this->owner->id,
                'parent_collection_id' => factory(UserCollection::class)->create()->id,
            ]);

        $this->publisherCollection = factory(PublisherCollection::class)
            ->states('nonArchived')
            ->create();
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    #[Test]
    public function it_flushes_the_cache_correctly_on_model_event(): void
    {
        $this->expectException(NotImplementedException::class);

        $method = new ReflectionMethod($this->repository, 'flush');
        $method->setAccessible(true);

        $method->invoke($this->repository);
    }

    /**
     * The cache of the user collections for the user is flushed on activate.
     */
    #[Test]
    public function it_flushes_the_user_collection_cache_on_activate(): void
    {
        $cacheTester = CacheTester::create(UserCollectionCacheRepository::class)
            ->addTag('userCollectionsForUser:' . $this->owner->id)
            ->setKey('userCollectionsByUserId:' . $this->owner->id);

        app(UserCollectionRepositoryInterface::class)->getAllByUser($this->owner);

        $cacheTester->assertFilled();

        $this->repository->activate($this->publisherCollection, $this->owner, Carbon::now());

        $cacheTester->assertEmpty();
    }

    /**
     * The cache for the user collections of user is not flushed on activate for other user.
     */
    #[Test]
    public function it_does_not_flush_user_collections_cache_of_other_user_on_activate(): void
    {
        $otherUser = User::factory()->create();

        $cacheTester = CacheTester::create(UserCollectionCacheRepository::class)
            ->addTag('userCollectionsForUser:' . $this->owner->id)
            ->setKey('userCollectionsByUserId:' . $this->owner->id);

        app(UserCollectionRepositoryInterface::class)->getAllByUser($this->owner);

        $cacheTester->assertFilled();

        $this->repository->activate($this->publisherCollection, $otherUser, Carbon::now());

        $cacheTester->assertFilled();
    }
}
