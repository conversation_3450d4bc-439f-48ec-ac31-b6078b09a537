<?php

namespace Tests\Unit\Cfa\Planner\Repositories;

use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\Collection\MigrationStatus;
use Cfa\Planner\Domain\Collection\UserCollection\UserCollection;
use Cfa\Planner\Domain\Collection\UserCollection\UserCollectionRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Override;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\Traits\WithSeeding;
use Tests\Unit\UnitTestCase;

use function app;
use function factory;
use function uuid;

class UserCollectionRepositoryHasUnmigratedChildrenTest extends UnitTestCase
{
    use WithSeeding;

    protected UserCollectionRepositoryInterface $repository;

    private UserCollection $usercollection;

    /**
     * {@inheritdoc}
     */
    #[Override]
    protected function setUp(): void
    {
        parent::setUp();
        $this->setUpTeacher();
        $this->repository = app(UserCollectionRepositoryInterface::class);
        $this->usercollection = factory(UserCollection::class)
            ->states('nonArchived')
            ->create([
                'owner_id' => $this->owner->id,
            ]);
    }

    #[Test]
    public function it_returns_false_if_no_one_has_a_child_collection(): void
    {
        $this->assertFalse($this->repository->hasUnmigratedChildren($this->usercollection));
    }

    #[Test]
    public function it_returns_false_if_no_one_has_an_unmigrated_child_collection(): void
    {
        factory(UserCollection::class)
            ->states('nonArchived')
            ->create([
                'owner_id' => User::factory()->create()->id,
                'parent_collection_id' => $this->usercollection->id,
                'migration_status' => MigrationStatus::COMPLETED,
            ]);

        $this->assertFalse($this->repository->hasUnmigratedChildren($this->usercollection));
    }

    #[DataProvider('getMigrationNonCompleteStatus')]
    #[Test]
    public function it_returns_true_if_there_are_unmigrated_not_completed_child_collections(
        ?MigrationStatus $migrationStatus,
    ): void {
        factory(UserCollection::class)
            ->states('nonArchived')
            ->create([
                'owner_id' => User::factory()->create()->id,
                'parent_collection_id' => $this->usercollection->id,
                'migration_status' => $migrationStatus,
            ]);

        $this->assertTrue($this->repository->hasUnmigratedChildren($this->usercollection));
    }

    #[DataProvider('getMigrationNonCompleteStatus')]
    #[Test]
    public function it_returns_true_if_there_are_unmigrated_not_completed_child_collections_using_a_v2_collection(
        ?MigrationStatus $migrationStatus,
    ): void {
        $v2UserCollection = $this->createPlannerCollectionV2($this->usercollection, 1337);
        factory(UserCollection::class)
            ->states('nonArchived')
            ->create([
                'owner_id' => User::factory()->create()->id,
                'parent_collection_id' => $this->usercollection->id,
                'migration_status' => $migrationStatus,
            ]);

        $this->assertTrue($this->repository->hasUnmigratedChildren($v2UserCollection));
    }

    #[Test]
    public function it_returns_false_if_no_one_has_an_unmigrated_child_collection_using_a_v2_collection(): void
    {
        $v2UserCollection = $this->createPlannerCollectionV2($this->usercollection, 1337);
        factory(UserCollection::class)
            ->states('nonArchived')
            ->create([
                'owner_id' => User::factory()->create()->id,
                'parent_collection_id' => $this->usercollection->id,
                'migration_status' => MigrationStatus::COMPLETED,
            ]);

        $this->assertFalse($this->repository->hasUnmigratedChildren($v2UserCollection));
    }

    #[Test]
    public function it_returns_false_if_no_collections_exits_for_v2_collection(): void
    {
        PlannerCollection::setQueryingEnabled();
        $plannerCollection = PlannerCollection::forceCreate([
            'uid' => uuid(),
            'name' => 'test',
            'id' => 666,
            'version' => 1,
        ]);
        PlannerCollection::setQueryingDisabled();

        $this->assertFalse($this->repository->hasUnmigratedChildren($plannerCollection));
    }

    private function createPlannerCollectionV2(UserCollection $usercollection, int $id): PlannerCollection
    {
        PlannerCollection::setQueryingEnabled();
        $plannerCollection = PlannerCollection::forceCreate([
            'uid' => $usercollection->uid,
            'name' => $usercollection->name,
            'id' => $id,
            'version' => 1,
        ]);
        PlannerCollection::setQueryingDisabled();

        return $plannerCollection;
    }

    public static function getMigrationNonCompleteStatus(): array
    {
        return [
            'empty' => [null],
            MigrationStatus::PROCESSING->value => [MigrationStatus::PROCESSING],
            MigrationStatus::FAILED->value => [MigrationStatus::FAILED],
            MigrationStatus::QUEUED->value => [MigrationStatus::QUEUED],
        ];
    }
}
