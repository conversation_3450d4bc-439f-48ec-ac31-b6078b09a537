<?php

namespace Tests\Unit\Cfa\Planner\Repositories\CollectionV2;

use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\Collection\PublisherCollection\Publisher\Publisher;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Illuminate\Support\Collection;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\UnitTestCase;

use function app;
use function factory;

class PlannerCollectionRepositoryGetAllByPublisherIdTest extends UnitTestCase
{
    private PlannerCollection $collectionVanIn;
    private PlannerCollection $collectionDeBoeck;
    private Publisher $publisherVanIn;
    private Publisher $publisherDeBoeck;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->publisherVanIn = factory(Publisher::class)->create(['uid' => 'van-in', 'name' => 'VAN IN']);
        $this->publisherDeBoeck = factory(Publisher::class)->create(['uid' => 'de-boeck', 'name' => '<PERSON> Boeck']);
        PlannerCollection::setQueryingEnabled();
        $this->collectionVanIn = factory(PlannerCollection::class)
            ->create(['user_id' => null, 'publisher_id' => $this->publisherVanIn->id]);
        $this->collectionDeBoeck = factory(PlannerCollection::class)
            ->create(['user_id' => null, 'publisher_id' => $this->publisherDeBoeck->id]);
        PlannerCollection::setQueryingDisabled();

        $user = User::factory()->create();
        PlannerCollection::factory()->forUser($user)->create();
        PlannerCollection::factory()->create();
        ActivatedPlannerCollection::factory()
            ->forUser($user)
            ->forPlannerCollection($this->collectionVanIn)
            ->create();

        $this->repository = app(PlannerCollectionRepositoryInterface::class);
    }

    #[Test]
    public function it_returns_all_publisher_collections_regardless_of_publisher(): void
    {
        $collections = $this->repository->getAllByPublisherId(null);

        $this->assertInstanceOf(Collection::class, $collections);
        $this->assertCount(2, $collections);
        PlannerCollection::setQueryingEnabled();
        $this->assertSame($this->collectionVanIn->refresh()->toArray(), $collections->get(0)->toArray());
        $this->assertSame($this->collectionVanIn->uid, $collections->get(0)['collectionUid']);
        $this->assertSame($this->collectionDeBoeck->refresh()->toArray(), $collections->get(1)->toArray());
        $this->assertSame($this->collectionDeBoeck->uid, $collections->get(1)['collectionUid']);
        PlannerCollection::setQueryingDisabled();
    }

    #[Test]
    public function it_returns_publisher_collections_for_a_specific_publisher(): void
    {
        $collections = $this->repository->getAllByPublisherId($this->publisherDeBoeck->id);

        $this->assertInstanceOf(Collection::class, $collections);
        $this->assertCount(1, $collections);
        PlannerCollection::setQueryingEnabled();
        $this->assertSame($this->collectionDeBoeck->refresh()->toArray(), $collections->first()->toArray());
        $this->assertSame($this->collectionDeBoeck->uid, $collections->first()['collectionUid']);
        PlannerCollection::setQueryingDisabled();
    }
}
