<?php

namespace Tests\Unit\Cfa\Planner\Repositories\CollectionV2;

use App\Constants\FilesystemDisks;
use App\Services\MediaLibrary\MediaCollections;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Application\Exceptions\UnsupportedMediaCollectionTypeException;
use Cfa\Planner\Application\Repositories\CollectionV2\Chapter\CopyMediaToPlannerCollectionRepository;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Storage;
use Tests\Factories\Common\Media\MediaFactory;
use Tests\Factories\Planner\PlannerCollectionFactory;
use Tests\Unit\UnitTestCase;

use function app;

class CopyMediaToPlannerCollectionRepositoryTest extends UnitTestCase
{
    private User $owner;
    private PlannerCollectionInterface $sourcePlannerCollection;
    private PlannerCollectionInterface $targetPlannerCollection;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();
        $this->owner = User::factory()->create();
        $this->sourcePlannerCollection = app(PlannerCollectionFactory::class)
            ->forUser($this->owner)
            ->create();

        $this->targetPlannerCollection = app(PlannerCollectionFactory::class)
            ->forUser($this->owner)
            ->create();

        Storage::fake(FilesystemDisks::S3);
        Storage::disk(FilesystemDisks::S3)->put(
            'sol-fl/media/planner_collection/cover/uuid-1/300x300.jpg',
            Storage::disk(FilesystemDisks::LOCAL_PROJECT_DIR)->get('/tests/resources/covers/300x300.jpg'),
        );

        app(MediaFactory::class)
            ->setModelType(new PlannerCollection()->getMorphClass())
            ->setModelId($this->sourcePlannerCollection->id)
            ->setUuid('uuid-1')
            ->setFileName('300x300.jpg')
            ->create();
    }

    #[Test]
    public function it_copies_media_to_target_planner_collection(): void
    {
        app(CopyMediaToPlannerCollectionRepository::class)
            ->copyMediaToPlannerCollection($this->sourcePlannerCollection, $this->targetPlannerCollection);

        $this->assertDatabaseHas('media', [
            'model_type' => new PlannerCollection()->getMorphClass(),
            'model_id' => $this->sourcePlannerCollection->id,
            'file_name' => '300x300.jpg',
        ]);
        $this->assertDatabaseHas('media', [
            'model_type' => new PlannerCollection()->getMorphClass(),
            'model_id' => $this->targetPlannerCollection->id,
            'file_name' => '300x300.jpg',
        ]);
    }

    #[Test]
    public function it_copies_media_from_and_to_activated_plannerCollections(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)
            ->forUser($this->owner)
            ->create();

        $this->sourcePlannerCollection = ActivatedPlannerCollection::factory()
            ->forPlannerCollection($plannerCollection)
            ->forUser($this->owner)
            ->create();

        $this->targetPlannerCollection = ActivatedPlannerCollection::factory()
            ->forPlannerCollection($plannerCollection)
            ->forUser($this->owner)
            ->create();

        Storage::disk(FilesystemDisks::S3)->put(
            'sol-fl/media/activated_planner_collection/cover/uuid-2/300x300.jpg',
            Storage::disk(FilesystemDisks::LOCAL_PROJECT_DIR)->get('/tests/resources/covers/300x300.jpg'),
        );
        app(MediaFactory::class)
            ->setModelType(new ActivatedPlannerCollection()->getMorphClass())
            ->setModelId($this->sourcePlannerCollection->id)
            ->setUuid('uuid-2')
            ->setFileName('300x300.jpg')
            ->create();

        app(CopyMediaToPlannerCollectionRepository::class)
            ->copyMediaToPlannerCollection($this->sourcePlannerCollection, $this->targetPlannerCollection);

        $this->assertDatabaseHas('media', [
            'model_type' => new ActivatedPlannerCollection()->getMorphClass(),
            'model_id' => $this->sourcePlannerCollection->id,
            'uuid' => 'uuid-2',
            'file_name' => '300x300.jpg',
        ]);
        $this->assertDatabaseHas('media', [
            'model_type' => new ActivatedPlannerCollection()->getMorphClass(),
            'model_id' => $this->targetPlannerCollection->id,
            'file_name' => '300x300.jpg',
        ]);
        $this->assertCount(
            2,
            Storage::disk(FilesystemDisks::S3)->directories('sol-fl/media/activated_planner_collection/cover'),
        );
    }

    #[Test]
    public function it_copies_overwritten_media_for_converted_activated_plannerCollections(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)
            ->forUser($this->owner)
            ->create();

        $this->sourcePlannerCollection = ActivatedPlannerCollection::factory()
            ->forPlannerCollection($plannerCollection)
            ->forUser($this->owner)
            ->create();

        $this->targetPlannerCollection = PlannerCollection::factory()
            ->forUser($this->owner)
            ->create();

        Storage::disk(FilesystemDisks::S3)->put(
            'sol-fl/media/planner_collection/cover/uuid-2/original.jpg',
            Storage::disk(FilesystemDisks::LOCAL_PROJECT_DIR)->get('/tests/resources/covers/300x300.jpg'),
        );
        app(MediaFactory::class)
            ->setModelType($plannerCollection->getMorphClass())
            ->setModelId($plannerCollection->id)
            ->setUuid('uuid-2')
            ->setFileName('original.jpg')
            ->create();

        Storage::disk(FilesystemDisks::S3)->put(
            'sol-fl/media/activated_planner_collection/cover/uuid-3/overwritten.jpg',
            Storage::disk(FilesystemDisks::LOCAL_PROJECT_DIR)->get('/tests/resources/covers/300x300.jpg'),
        );
        app(MediaFactory::class)
            ->setModelType($this->sourcePlannerCollection->getMorphClass())
            ->setModelId($this->sourcePlannerCollection->id)
            ->setUuid('uuid-3')
            ->setFileName('overwritten.jpg')
            ->create();

        $sourcePlannerCollectionWithData = app(PlannerCollectionRepositoryInterface::class)
            ->loadAllData($this->sourcePlannerCollection);

        app(CopyMediaToPlannerCollectionRepository::class)
            ->copyMediaToPlannerCollection($sourcePlannerCollectionWithData, $this->targetPlannerCollection);

        $this->assertDatabaseHas('media', [
            'model_type' => $this->sourcePlannerCollection->getMorphClass(),
            'model_id' => $this->sourcePlannerCollection->id,
            'uuid' => 'uuid-3',
            'file_name' => 'overwritten.jpg',
        ]);
        $this->assertDatabaseHas('media', [
            'model_type' => $this->targetPlannerCollection->getMorphClass(),
            'model_id' => $this->targetPlannerCollection->id,
            'file_name' => 'overwritten.jpg',
        ]);
        $this->assertCount(
            3,
            Storage::disk(FilesystemDisks::S3)->directories('sol-fl/media/planner_collection/cover'),
        );
    }

    #[Test]
    public function it_copies_original_media_for_converted_activated_plannerCollections(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)
            ->forUser($this->owner)
            ->create();

        $this->sourcePlannerCollection = ActivatedPlannerCollection::factory()
            ->forPlannerCollection($plannerCollection)
            ->forUser($this->owner)
            ->create();

        $this->targetPlannerCollection = PlannerCollection::factory()
            ->forUser($this->owner)
            ->create();

        Storage::disk(FilesystemDisks::S3)->put(
            'sol-fl/media/planner_collection/cover/uuid-2/original.jpg',
            Storage::disk(FilesystemDisks::LOCAL_PROJECT_DIR)->get('/tests/resources/covers/300x300.jpg'),
        );
        app(MediaFactory::class)
            ->setModelType($plannerCollection->getMorphClass())
            ->setModelId($plannerCollection->id)
            ->setUuid('uuid-2')
            ->setFileName('original.jpg')
            ->create();

        $sourcePlannerCollectionWithData = app(PlannerCollectionRepositoryInterface::class)
            ->loadAllData($this->sourcePlannerCollection);

        app(CopyMediaToPlannerCollectionRepository::class)
            ->copyMediaToPlannerCollection($sourcePlannerCollectionWithData, $this->targetPlannerCollection);

        $this->assertDatabaseHas('media', [
            'model_type' => $plannerCollection->getMorphClass(),
            'model_id' => $plannerCollection->id,
            'uuid' => 'uuid-2',
            'file_name' => 'original.jpg',
        ]);
        $this->assertDatabaseHas('media', [
            'model_type' => $this->targetPlannerCollection->getMorphClass(),
            'model_id' => $this->targetPlannerCollection->id,
            'file_name' => 'original.jpg',
        ]);
        $this->assertCount(
            3,
            Storage::disk(FilesystemDisks::S3)->directories('sol-fl/media/planner_collection/cover'),
        );
    }

    #[Test]
    public function it_throws_an_exception_for_unsupported_media_types(): void
    {
        $this->expectException(UnsupportedMediaCollectionTypeException::class);
        Media::query()->update(['collection_name' => MediaCollections::PROFILE_PICTURE]);
        app(CopyMediaToPlannerCollectionRepository::class)
            ->copyMediaToPlannerCollection($this->sourcePlannerCollection, $this->targetPlannerCollection);
    }
}
