<?php

namespace Tests\Unit\Cfa\Planner\Repositories\CollectionV2;

use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollectionRepositoryInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\Factories\Planner\PlannerCollectionFactory;
use Tests\Unit\UnitTestCase;

use function app;

class ActivatedPlannerCollectionRepositoryHasActivatedCollectionsTest extends UnitTestCase
{
    #[Test]
    public function it_returns_true_when_planner_collection_has_activated_collections(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->create();

        ActivatedPlannerCollection::factory()
            ->forUser(User::factory()->create())
            ->forPlannerCollection($plannerCollection)
            ->create();

        $hasActivatedCollections = app(ActivatedPlannerCollectionRepositoryInterface::class)
            ->hasActivatedCollections($plannerCollection);

        $this->assertTrue($hasActivatedCollections);
    }

    #[Test]
    public function it_returns_false_when_planner_collection_has_no_activated_collections(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->create();

        $hasActivatedCollections = app(ActivatedPlannerCollectionRepositoryInterface::class)
            ->hasActivatedCollections($plannerCollection);

        $this->assertFalse($hasActivatedCollections);
    }

    #[Test]
    public function it_doesnt_count_collections_of_deleted_users(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->create();

        $deletedUser = User::factory()->create();

        ActivatedPlannerCollection::factory()
            ->forUser($deletedUser)
            ->forPlannerCollection($plannerCollection)
            ->create();

        $deletedUser->delete();

        $hasActivatedCollections = app(ActivatedPlannerCollectionRepositoryInterface::class)
            ->hasActivatedCollections($plannerCollection);

        $this->assertFalse($hasActivatedCollections);
    }

    #[Test]
    public function it_does_not_count_collections_of_users_without_active_careers(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->forUser(User::factory()->create())->create();
        $user = User::factory()
            ->withExpiredCareer(School::factory()->create())
            ->create();
        ActivatedPlannerCollection::factory()
            ->forUser($user)
            ->forPlannerCollection($plannerCollection)
            ->create();

        $this->expectScopeCall();

        $hasActivatedCollections = app(ActivatedPlannerCollectionRepositoryInterface::class)
            ->hasActivatedCollections($plannerCollection);
        $this->assertFalse($hasActivatedCollections);
        $this->assertScopeCalled('whereHasActiveCareers', User::class);
    }

    #[Test]
    public function it_does_not_count_deleted_collections(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->forUser(User::factory()->create())->create();
        $deletedUser = User::factory()->create();
        $activatedCollection = ActivatedPlannerCollection::factory()
            ->forUser($deletedUser)
            ->forPlannerCollection($plannerCollection)
            ->create();

        ActivatedPlannerCollection::setQueryingEnabled();
        $activatedCollection->delete();
        ActivatedPlannerCollection::setQueryingDisabled();

        $hasActivatedCollections = app(ActivatedPlannerCollectionRepositoryInterface::class)
            ->hasActivatedCollections($plannerCollection);

        $this->assertFalse($hasActivatedCollections);
    }
}
