<?php

namespace Tests\Unit\Cfa\Planner\Repositories\CollectionV2;

use Carbon\Carbon;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollectionRepositoryInterface;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Factories\Planner\PlannerCollectionFactory;
use Tests\Unit\UnitTestCase;

use function app;

class ActivatedPlannerCollectionRepositoryDeactivateTest extends UnitTestCase
{
    private ActivatedPlannerCollectionRepositoryInterface $repository;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(ActivatedPlannerCollectionRepositoryInterface::class);
    }

    #[Test]
    public function it_deactivates_an_activated_collection(): void
    {
        $activatedCollection = ActivatedPlannerCollection::factory()
            ->forUser(User::factory()->create())
            ->forPlannerCollection(app(PlannerCollectionFactory::class)->create())
            ->create();
        $this->repository->deactivate($activatedCollection, Carbon::now());

        ActivatedPlannerCollection::setQueryingEnabled();
        $activatedCollection->refresh();
        ActivatedPlannerCollection::setQueryingDisabled();

        $this->assertNotNull($activatedCollection->deactivated_at);
    }
}
