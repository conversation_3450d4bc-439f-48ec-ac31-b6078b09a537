<?php

namespace Tests\Unit\Cfa\Planner\Repositories\CollectionV2\Chapter\Record;

use Cfa\Common\Application\Services\Helpers\SchoolHelperService;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Record;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\TemplateRecordRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use DomainException;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Factories\Planner\ChapterFactory;
use Tests\Factories\Planner\PlannerCollectionFactory;
use Tests\Factories\Planner\RecordFactory;
use Tests\Unit\UnitTestCase;

use function app;
use function collect;

class TemplateRecordRepositoryTest extends UnitTestCase
{
    private PlannerCollection $plannerCollection;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $school = School::factory()->inKathOndVla()->create();
        $this->mock(SchoolHelperService::class)->shouldReceive('school')->andReturn($school);

        $this->plannerCollection = app(PlannerCollectionFactory::class)->create();
    }

    #[Test]
    public function it_returns_a_template_record_for_a_planner_collection(): void
    {
        app(RecordFactory::class)
            ->forPlannerCollection($this->plannerCollection)
            ->setIsTemplate(true)
            ->setName('Team NEGENDUUST!')
            ->create();

        app(RecordFactory::class)
            ->forPlannerCollection($this->plannerCollection)
            ->setIsTemplate(false)
            ->create();

        $templateRecord = app(TemplateRecordRepositoryInterface::class)
            ->findTemplateRecord($this->plannerCollection);
        $this->assertSame('Team NEGENDUUST!', $templateRecord->name);
    }

    #[Test]
    public function it_returns_a_template_record_for_an_activated_planner_collection(): void
    {
        $activatedPlannerCollection = ActivatedPlannerCollection::factory()
            ->forUser(User::factory()->create())
            ->forPlannerCollection($this->plannerCollection)
            ->create();

        app(RecordFactory::class)
            ->forPlannerCollection($this->plannerCollection)
            ->setIsTemplate(true)
            ->setName('Team NEGENDUUST!')
            ->create();

        app(RecordFactory::class)
            ->forPlannerCollection($this->plannerCollection)
            ->setIsTemplate(false)
            ->create();

        $templateRecord = app(TemplateRecordRepositoryInterface::class)
            ->findTemplateRecord($activatedPlannerCollection);
        $this->assertSame('Team NEGENDUUST!', $templateRecord->name);
    }

    #[Test]
    public function it_throws_an_exception_when_it_finds_multiple_template_records(): void
    {
        $this->expectException(DomainException::class);

        $record = new Record(['name' => 'Team NEGENDUUST!']);

        $this->mock(RecordRepositoryInterface::class)
            ->shouldReceive('getByPlannerCollectionInterface')
            ->once()
            ->andReturn(collect([$record, $record]));

        app(TemplateRecordRepositoryInterface::class)->findTemplateRecord($this->plannerCollection);
    }

    #[Test]
    public function it_returns_only_the_template_record(): void
    {
        app(RecordFactory::class)
            ->forPlannerCollection($this->plannerCollection)
            ->setIsTemplate(true)
            ->setName('Team NEGENDUUST!')
            ->create();

        $otherPlannerCollection = app(PlannerCollectionFactory::class)->create();
        $otherChapter = app(ChapterFactory::class)->forPlannerCollection($otherPlannerCollection)->create();
        app(RecordFactory::class)
            ->forChapter($otherChapter)
            ->forPlannerCollection($otherPlannerCollection)
            ->setIsTemplate(true)
            ->create();

        app(RecordFactory::class)->forPlannerCollection($this->plannerCollection)->setIsTemplate(false)->create();

        $templateRecord = app(TemplateRecordRepositoryInterface::class)
            ->findOrCreateTemplateRecord($this->plannerCollection);
        $this->assertSame('Team NEGENDUUST!', $templateRecord->name);
    }

    #[Test]
    public function it_calls_get_by_planner_collection_interface(): void
    {
        $record = new Record(['name' => 'test']);

        $this->mock(RecordRepositoryInterface::class)
            ->shouldReceive('getByPlannerCollectionInterface')
            ->once()
            ->andReturn(collect([$record]));

        $templateRecord = app(TemplateRecordRepositoryInterface::class)
            ->findOrCreateTemplateRecord($this->plannerCollection);

        $this->assertEquals($record, $templateRecord);
    }

    #[Test]
    public function it_creates_a_template_record_on_the_planner_collection(): void
    {
        app(RecordFactory::class)
            ->forPlannerCollection($this->plannerCollection)
            ->setIsTemplate(false)
            ->setName('Some random record')
            ->create();
        $this->activatedPlannerCollection = ActivatedPlannerCollection::factory()
            ->forUser(User::factory()->create())
            ->forPlannerCollection($this->plannerCollection)
            ->create();
        $this->activatedPlannerCollection->setRelation('plannerCollection', $this->plannerCollection);

        $templateRecord =
            app(TemplateRecordRepositoryInterface::class)
                ->findOrCreateTemplateRecord($this->activatedPlannerCollection);

        $this->assertNotNull($templateRecord);
        Record::setQueryingEnabled();
        $this->assertTrue(
            Record::where('planner_collection_id', $this->plannerCollection->id)
                ->whereNull('activated_planner_collection_id')
                ->where('is_template', 1)
                ->exists(),
        );
        Record::setQueryingDisabled();
    }

    #[Test]
    public function it_does_not_creates_a_template_record_on_the_planner_collection_if_one_exits(): void
    {
        app(RecordFactory::class)
            ->forPlannerCollection($this->plannerCollection)
            ->setIsTemplate(true)
            ->setName('Team NEGENDUUST!')
            ->create();
        $this->activatedPlannerCollection = ActivatedPlannerCollection::factory()
            ->forUser(User::factory()->create())
            ->forPlannerCollection($this->plannerCollection)
            ->create();
        $this->activatedPlannerCollection->setRelation('plannerCollection', $this->plannerCollection);

        $templateRecord =
            app(TemplateRecordRepositoryInterface::class)
                ->findOrCreateTemplateRecord($this->activatedPlannerCollection);

        $this->assertNotNull($templateRecord);
        $this->assertSame('Team NEGENDUUST!', $templateRecord->name);
        Record::setQueryingEnabled();
        $this->assertFalse(
            Record::where('planner_collection_id', $this->plannerCollection->id)
                ->whereNull('activated_planner_collection_id')
                ->where('is_template', 1)
                ->where('name', '<>', 'Team NEGENDUUST!')
                ->exists(),
        );
        Record::setQueryingDisabled();
    }

    #[Test]
    public function it_throws_an_exception_when_there_are_multiple_template_records(): void
    {
        $this->expectException(DomainException::class);

        $record = new Record(['name' => 'test']);

        $this->mock(RecordRepositoryInterface::class)
            ->shouldReceive('getByPlannerCollectionInterface')
            ->once()
            ->andReturn(collect([$record, $record]));

        $templateRecord = app(TemplateRecordRepositoryInterface::class)
            ->findOrCreateTemplateRecord($this->plannerCollection);

        $this->assertEquals($record, $templateRecord);
    }

    #[Test]
    public function it_returns_a_template_for_planner_collection(): void
    {
        $record = app(RecordFactory::class)
            ->forCollection($this->plannerCollection)
            ->forPlannerCollection($this->plannerCollection)
            ->setIsTemplate(true)
            ->create();

        $record = app(RecordRepositoryInterface::class)->getById($record->id, $this->plannerCollection);

        $templateRecord = app(TemplateRecordRepositoryInterface::class)
            ->findOrCreateTemplateRecord($this->plannerCollection);

        $this->assertEquals($record, $templateRecord);
    }

    #[Test]
    public function it_creates_and_returns_a_record_if_no_record_exists_on_planner_collection(): void
    {
        Record::setQueryingEnabled();

        $templateRecord = Record::whereIsTemplate(true)->where(
            'planner_collection_id',
            $this->plannerCollection->id,
        )->get();

        Record::setQueryingDisabled();

        $this->assertCount(0, $templateRecord);

        app(TemplateRecordRepositoryInterface::class)
            ->findOrCreateTemplateRecord($this->plannerCollection);

        Record::setQueryingEnabled();

        $templateRecord = Record::whereIsTemplate(true)->where(
            'planner_collection_id',
            $this->plannerCollection->id,
        )->get();

        Record::setQueryingDisabled();

        $this->assertCount(1, $templateRecord);
    }

    #[Test]
    public function it_creates_and_returns_a_record_with_the_given_name(): void
    {
        Record::setQueryingEnabled();

        $templateRecord = Record::whereIsTemplate(true)->where(
            'planner_collection_id',
            $this->plannerCollection->id,
        )->get();

        Record::setQueryingDisabled();

        $this->assertCount(0, $templateRecord);

        app(TemplateRecordRepositoryInterface::class)
            ->findOrCreateTemplateRecord($this->plannerCollection, 'Some fancy name');

        Record::setQueryingEnabled();

        $templateRecord = Record::whereIsTemplate(true)->where(
            'planner_collection_id',
            $this->plannerCollection->id,
        )->get();

        Record::setQueryingDisabled();
        $this->assertCount(1, $templateRecord);
        $this->assertEquals('Some fancy name', $templateRecord->first()->name);
    }
}
