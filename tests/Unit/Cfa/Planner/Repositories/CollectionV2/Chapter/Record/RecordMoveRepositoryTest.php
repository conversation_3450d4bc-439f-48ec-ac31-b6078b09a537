<?php

namespace Tests\Unit\Cfa\Planner\Repositories\CollectionV2\Chapter\Record;

use Cfa\Common\Domain\School\School;
use Cfa\Planner\Domain\CalendarItem\CalendarItem;
use Cfa\Planner\Domain\CalendarItem\Row\CalendarItemRow;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\Chapter\Chapter;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\DuplicateRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Record;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordMoveRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordOverwrite;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionInterface;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Factories\Planner\ChapterFactory;
use Tests\Factories\Planner\PlannerCollectionFactory;
use Tests\Factories\Planner\RecordFactory;

use function app;

class RecordMoveRepositoryTest extends AbstractRecordRepositoryTestCase
{
    protected array $defaultQueryingEnabledOnModels = [
        ActivatedPlannerCollection::class,
        PlannerCollection::class,
        Chapter::class,
        Record::class,
        RecordOverwrite::class,
    ];

    private School $school;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->school = School::factory()->create();
    }

    #[Test]
    public function it_moves_a_own_record_to_a_new_collection_chapter(): void
    {
        $this->createPlannerCollectionWithOneChapterWithOneRecord();
        $calendarItem = CalendarItem::factory()
            ->forSchool($this->school)
            ->forOwner($this->user)
            ->create();

        $calendarItemRow = CalendarItemRow::factory()->create([
            'calendaritem_id' => $calendarItem->id,
            'record_v2_id' => $this->record1ForChapter1->id,
            'planner_collection_id' => $this->plannerCollection->id,
            'activated_planner_collection_id' => null,
        ]);

        $this->record1ForChapter1->setCollectionRelation($this->plannerCollection);
        $targetPlannerCollection = app(PlannerCollectionFactory::class)->create();
        $targetChapter = app(ChapterFactory::class)
            ->forPlannerCollection($this->plannerCollection)
            ->create();
        $targetChapterRecord = app(RecordFactory::class)
            ->forPlannerCollection($targetPlannerCollection)
            ->setOrder(5)
            ->setZill(true)
            ->forChapter($targetChapter)
            ->create();
        $movedRecord = app(RecordMoveRepositoryInterface::class)
            ->move($targetPlannerCollection, $targetChapter, $this->record1ForChapter1);
        $this->withQueryingEnabledOnModels(fn(): Record => $this->record1ForChapter1->refresh());
        $this->assertEquals($targetChapter->id, $this->record1ForChapter1->chapter_id);
        // Order was 1337.
        $this->assertEquals($targetChapterRecord->order + 1, $this->record1ForChapter1->order);
        // Check if its the same record.
        $this->assertEquals($movedRecord->uid, $this->record1ForChapter1->uid);
        $this->assertEquals($movedRecord->planner_collection_id, $targetPlannerCollection->id);
        $this->assertNull($movedRecord->activated_planner_collection_id);

        $calendarItemRow->refresh();
        $this->assertEquals($movedRecord->id, $calendarItemRow->record_v2_id);
        $this->assertEquals($targetPlannerCollection->id, $calendarItemRow->planner_collection_id);
        $this->assertNull($calendarItemRow->activated_planner_collection_id);
    }

    #[Test]
    public function it_copies_a_inherited_record_to_a_new_collection_chapter_and_deletes_the_record(): void
    {
        $this->createSimpleActivatedPlannerCollection();
        $calendarItem = CalendarItem::factory()
            ->forSchool($this->school)
            ->forOwner($this->user)
            ->create();

        $calendarItemRow = CalendarItemRow::factory()->create([
            'calendaritem_id' => $calendarItem->id,
            'record_v2_id' => $this->record1ForChapter1->id,
            'planner_collection_id' => null,
            'activated_planner_collection_id' => $this->activatedCollection->id,
        ]);

        // Make the record Inherited.
        $this->record1ForChapter1->setCollectionRelation($this->activatedCollection);
        $this->withQueryingEnabledOnModels(fn(): Record => $this->record1ForChapter1->load('overwrites'));
        $targetPlannerCollection = app(PlannerCollectionFactory::class)->create();
        $targetChapter = app(ChapterFactory::class)
            ->setOrder(2)
            ->forPlannerCollection($this->plannerCollection)
            ->create();
        $newRecord = app(RecordFactory::class)
            ->forCollection($targetPlannerCollection)
            ->forPlannerCollection($targetPlannerCollection)
            ->forChapter($targetChapter)
            ->create();
        $this->mock(DuplicateRepositoryInterface::class)
            ->shouldReceive('duplicateAndReorderRecords')
            ->withArgs(fn(
                PlannerCollectionInterface $targetCollection,
                Chapter $chapter,
                Record $record,
            ): bool => $targetCollection->uid === $targetPlannerCollection->uid
                && $chapter->uid === $targetChapter->uid
                && $record->uid === $this->record1ForChapter1->uid)->andReturn($newRecord)
            ->once();
        $this->mock(RecordRepositoryInterface::class)
            ->shouldReceive('delete')
            ->withArgs(fn(Record $record): bool => $record->uid === $this->record1ForChapter1->uid)
            ->once();

        $movedRecord = app(RecordMoveRepositoryInterface::class)
            ->move($targetPlannerCollection, $targetChapter, $this->record1ForChapter1);

        $this->assertEquals($newRecord->uid, $movedRecord->uid);
        $this->assertEquals($movedRecord->planner_collection_id, $targetPlannerCollection->id);
        $this->assertNull($movedRecord->activated_planner_collection_id);

        $calendarItemRow->refresh();
        $this->assertEquals($movedRecord->id, $calendarItemRow->record_v2_id);
        $this->assertEquals($targetPlannerCollection->id, $calendarItemRow->planner_collection_id);
        $this->assertNull($calendarItemRow->activated_planner_collection_id);
    }
}
