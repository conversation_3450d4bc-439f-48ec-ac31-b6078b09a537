<?php

namespace Tests\Unit\Cfa\Planner\Repositories\CollectionV2\Chapter;

use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\Chapter\Chapter;
use Cfa\Planner\Domain\CollectionV2\Chapter\ChapterOverwrite;
use Cfa\Planner\Domain\CollectionV2\Chapter\ChapterOverwriteColumn;
use Cfa\Planner\Domain\CollectionV2\Chapter\ChapterRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Record;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionOverwrite;
use Illuminate\Container\Container;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use LogicException;
use PHPUnit\Framework\Attributes\Test;
use stdClass;
use Tests\Factories\Planner\ChapterFactory;
use Tests\Factories\Planner\ChapterOverwriteFactory;
use Tests\Factories\Planner\PlannerCollectionFactory;
use Tests\Factories\Planner\RecordFactory;

use function app;
use function array_merge;
use function collect;
use function json_encode;
use function str_repeat;

class ChapterRepositoryTest extends AbstractChapterRepositoryTestCase
{
    protected array $defaultQueryingEnabledOnModels = [
        Chapter::class,
        ChapterOverwrite::class,
    ];

    #[Test]
    public function it_throws_exception_if_deleting_a_chapter_without_context(): void
    {
        $this->setupActivatedCollectionAndChaptersForUser();
        $this->expectException(LogicException::class);
        $this->expectExceptionMessage('You should set the collection relation.');
        $chapter = app(ChapterFactory::class)
            ->forPlannerCollection($this->plannerCollection)
            ->create();

        $this->chapterRepository->delete($chapter);
    }

    #[Test]
    public function it_deletes_the_chapter_for_a_planner_collection(): void
    {
        $this->setupActivatedCollectionAndChaptersForUser();

        $chapters = app(ChapterFactory::class)
            ->forPlannerCollection($this->plannerCollection)
            ->createMultiple(2);

        $chapters->each(function (Chapter $chapter): void {
            $chapter->setCollectionRelation($this->plannerCollection);
        });

        $this->chapterRepository->delete($chapters->first());
        Chapter::setQueryingEnabled();
        $this->assertNotNull($chapters->first()->fresh()->deleted_at);
        $this->assertNull($chapters->last()->fresh()->deleted_at);
        Chapter::setQueryingDisabled();
    }

    #[Test]
    public function it_deletes_custom_chapter_for_an_activated_collection(): void
    {
        $this->setupActivatedCollectionAndChaptersForUser();

        $chapters = app(ChapterFactory::class)
            ->forActivatedCollection($this->activatedCollection)
            ->createMultiple(2);

        $chapters->each(function (Chapter $chapter): void {
            $chapter->setCollectionRelation($this->activatedCollection);
        });

        $this->chapterRepository->delete($chapters->first());
        Chapter::setQueryingEnabled();
        $this->assertNotNull($chapters->first()->fresh()->deleted_at);
        $this->assertNull($chapters->last()->fresh()->deleted_at);
        Chapter::setQueryingDisabled();
    }

    #[Test]
    public function it_deletes_the_inherited_chapter_for_an_activated_collection(): void
    {
        $this->setupActivatedCollectionAndChaptersForUser();
        $now = Carbon::now();
        Carbon::setTestNow($now);
        Auth::setUser($this->user);
        $chapters = app(ChapterFactory::class)
            ->forPlannerCollection($this->plannerCollection)
            ->createMultiple(2);

        $chapters->each(function (Chapter $chapter): void {
            $chapter->setCollectionRelation($this->activatedCollection);
        });

        $this->chapterRepository->delete($chapters->first());
        Chapter::setQueryingEnabled();
        // Original chapters should be UNTOUCHED.
        $this->assertNull($chapters->first()->fresh()->deleted_at);
        $this->assertNull($chapters->last()->fresh()->deleted_at);
        // But an overwrite should have been created for the deleted chapter.
        $overwrite = DB::table('chapter_overwrites')->where(
            [
                'activated_planner_collection_id' => $this->activatedCollection->id,
                'chapter_id' => $chapters->first()->id,
                'column' => ChapterOverwriteColumn::DeletedAt,
                'user_id' => $this->user->id,
                'created_at' => $now->toDateTimeString(),
            ],
        )->first();
        $this->assertInstanceOf(stdClass::class, $overwrite);
        $this->assertSame(json_encode($now->toDateTimeString()), $overwrite->value);

        // But not for a not deleted chapter.
        $this->assertDatabaseMissing(
            'chapter_overwrites',
            [
                'chapter_id' => $chapters->last()->id,
            ],
        );
        Chapter::setQueryingDisabled();
    }

    #[Test]
    public function it_uses_the_record_repository_to_delete_all_records_of_own_planner_collection(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->forUser($this->user)->create();

        $chapter = app(ChapterFactory::class)->forPlannerCollection($plannerCollection)->create();
        $chapter->setCollectionRelation($plannerCollection);

        $records = app(RecordFactory::class)
            ->forPlannerCollection($plannerCollection)
            ->forChapter($chapter)
            ->createMultiple(2);

        $deletedRecordUids = collect();

        $this->mock(RecordRepositoryInterface::class)
            ->shouldReceive('getAllByChapter')
            ->once()
            ->andReturn($records)
            ->shouldReceive('delete')
            ->withArgs(function (Record $record) use ($deletedRecordUids) {
                $deletedRecordUids->push($record->uid);

                return true;
            })
            ->twice();
        Container::getInstance()->forgetInstance(ChapterRepositoryInterface::class);
        $this->chapterRepository = app(ChapterRepositoryInterface::class);
        $this->chapterRepository->delete($chapter);

        $this->assertEquals($records->pluck('uid'), $deletedRecordUids);
    }

    #[Test]
    public function it_uses_the_record_repository_to_delete_records_of_own_chapter_in_activated_collection(): void
    {
        $this->setupActivatedCollectionAndChaptersForUser();

        $userChapter = $this->userChapters->first();
        $userChapter->setCollectionRelation($this->activatedCollection);

        $records = app(RecordFactory::class)
            ->forActivatedCollection($this->activatedCollection)
            ->forChapter($userChapter)
            ->createMultiple(2);

        $deletedRecordUids = collect();

        $this->mock(RecordRepositoryInterface::class)
            ->shouldReceive('getAllByChapter')
            ->once()
            ->andReturn($records)
            ->shouldReceive('delete')
            ->withArgs(function (Record $record) use ($deletedRecordUids) {
                $deletedRecordUids->push($record->uid);

                return true;
            })
            ->twice();
        Container::getInstance()->forgetInstance(ChapterRepositoryInterface::class);
        $this->chapterRepository = app(ChapterRepositoryInterface::class);
        $this->chapterRepository->delete($userChapter);

        $this->assertEquals($records->pluck('uid'), $deletedRecordUids);
    }

    #[Test]
    public function it_uses_the_record_repository_to_delete_all_records_of_inherited_chapter(): void
    {
        $this->setupActivatedCollectionAndChaptersForUser();
        $this->chapterWithOverwrites->setCollectionRelation($this->activatedCollection);

        $records = app(RecordFactory::class)
            ->forActivatedCollection($this->activatedCollection)
            ->forChapter($this->chapterWithOverwrites)
            ->createMultiple(2);

        $deletedRecordUids = collect();

        $this->mock(RecordRepositoryInterface::class)
            ->shouldReceive('getAllByChapter')
            ->once()
            ->andReturn($records)
            ->shouldReceive('delete')
            ->withArgs(function (Record $record) use ($deletedRecordUids) {
                $deletedRecordUids->push($record->uid);

                return true;
            })
            ->twice();
        Container::getInstance()->forgetInstance(ChapterRepositoryInterface::class);
        $this->chapterRepository = app(ChapterRepositoryInterface::class);
        $this->chapterRepository->delete($this->chapterWithOverwrites);

        $this->assertEquals($records->pluck('uid'), $deletedRecordUids);
    }

    #[Test]
    public function it_allows_saving_of_a_chapter(): void
    {
        $chapter = new Chapter();
        $chapter->name = $this->faker->name();
        $chapter->planner_collection_id = app(PlannerCollectionFactory::class)->create()->id;
        $this->assertNull($chapter->id);
        $this->chapterRepository->save($chapter);
        Chapter::setQueryingEnabled();
        $this->assertIsInt($chapter->fresh()->id);
        Chapter::setQueryingDisabled();
    }

    #[Test]
    public function it_creates_overwrites_for_all_attributes(): void
    {
        $this->setupActivatedCollectionAndChaptersForUser();
        $attributes = [
            'name' => 'new name',
            'order' => 1337,
        ];
        $this->chapterWithOverwrites->setCollectionRelation($this->activatedCollection);
        $this->chapterRepository->overwriteAttributes(
            $this->chapterWithOverwrites,
            $attributes,
        );
        $this->assertOverwritesExist($this->chapterWithOverwrites, array_merge($attributes, ['deleted_at' => null]));
    }

    #[Test]
    public function it_creates_overwrites_without_committing(): void
    {
        $this->setupActivatedCollectionAndChaptersForUser();
        $attributes = [
            'name' => 'new name',
            'order' => 1337,
        ];
        $this->chapterWithOverwrites->setCollectionRelation($this->activatedCollection);
        $this->chapterRepository->overwriteAttributesWithoutCommit($this->chapterWithOverwrites, $attributes);

        $secondChapter = $this->plannerCollectionChapters->get(1);
        $secondChapter->setCollectionRelation($this->activatedCollection);
        $this->chapterRepository->overwriteAttributesWithoutCommit($secondChapter, $attributes);

        // The overwrites created in setupActivatedCollectionAndChaptersForUser.
        $this->assertSame(3, $this->withQueryingEnabledOnModels(fn() => ChapterOverwrite::count()));

        $this->chapterRepository->commitOverwrites();

        $this->assertOverwritesExist($this->chapterWithOverwrites, array_merge($attributes, ['deleted_at' => null]));
        $this->assertOverwritesExist($secondChapter, $attributes);
    }

    #[Test]
    public function it_does_not_delete_overwrites_from_other_chapters_when_using_overwrites_without_committing(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->create();
        $this->activatedCollection = ActivatedPlannerCollection::factory()
            ->forUser($this->user)
            ->forPlannerCollection($plannerCollection)
            ->create();
        $chapter = app(ChapterFactory::class)
            ->forPlannerCollection($plannerCollection)
            ->setName('name')
            ->setOrder(30)
            ->create();
        $chapter->setCollectionRelation($this->activatedCollection);

        $existingOverwrite = app(ChapterOverwriteFactory::class)
            ->forUser($this->user)
            ->forActivatedPlannerCollection($this->activatedCollection)
            ->forChapter($chapter)
            ->forColumn(ChapterOverwriteColumn::Order)
            ->withValue(0)
            ->create();
        $chapter->setRelation('overwrites', collect([$existingOverwrite]));

        $this->chapterRepository->overwriteAttributesWithoutCommit(
            $chapter,
            [
                ChapterOverwriteColumn::Order->value => 0,
                ChapterOverwriteColumn::Name->value => 'overwrite name',
            ],
        );

        $chapter2 = app(ChapterFactory::class)
            ->forPlannerCollection($plannerCollection)
            ->setOrder(14)
            ->create();
        $chapter2->setCollectionRelation($this->activatedCollection);
        $existingOverwrite2 = app(ChapterOverwriteFactory::class)
            ->forUser($this->user)
            ->forActivatedPlannerCollection($this->activatedCollection)
            ->forChapter($chapter2)
            ->forColumn(ChapterOverwriteColumn::Name)
            ->withValue('name')
            ->create();
        $chapter2->setRelation('overwrites', collect([$existingOverwrite2]));

        $this->chapterRepository->overwriteAttributesWithoutCommit(
            $chapter2,
            [ChapterOverwriteColumn::Order->value => 15],
        );

        $this->chapterRepository->commitOverwrites();

        $this->assertOverwritesExist(
            $chapter,
            [
                ChapterOverwriteColumn::Order->value => 0,
                ChapterOverwriteColumn::Name->value => 'overwrite name',
            ],
        );
        $this->assertOverwritesExist(
            $chapter2,
            [
                ChapterOverwriteColumn::Order->value => 15,
                ChapterOverwriteColumn::Name->value => 'name',
            ],
        );
    }

    #[Test]
    public function it_does_not_delete_all_chapter_overwrites_when_everything_remains_the_same(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->create();
        $this->activatedCollection = ActivatedPlannerCollection::factory()
            ->forUser($this->user)
            ->forPlannerCollection($plannerCollection)
            ->create();
        $chapter = app(ChapterFactory::class)
            ->forPlannerCollection($plannerCollection)
            ->setName('name')
            ->setOrder(1)
            ->create();
        $chapter->setCollectionRelation($this->activatedCollection);
        $chapter->setRelation('overwrites', collect());

        $attributes = [
            ChapterOverwriteColumn::Order->value => 0,
            ChapterOverwriteColumn::Name->value => 'overwrite name',
        ];

        $this->chapterRepository->overwriteAttributes($chapter, $attributes);
        ChapterOverwrite::setQueryingEnabled();
        $chapter->setRelation('overwrites', ChapterOverwrite::query()->get());
        ChapterOverwrite::setQueryingDisabled();
        $this->chapterRepository->overwriteAttributes($chapter, $attributes);

        $this->assertOverwritesExist($chapter, $attributes);
    }

    #[Test]
    public function it_throws_an_exception_if_not_committing_on_time(): void
    {
        $this->setupActivatedCollectionAndChaptersForUser();
        $attributes = ['name' => 'new name'];
        $this->chapterWithOverwrites->setCollectionRelation($this->activatedCollection);
        $this->chapterRepository->overwriteAttributesWithoutCommit($this->chapterWithOverwrites, $attributes);

        $activatedCollection2 = ActivatedPlannerCollection::factory()
            ->forUser($this->user)
            ->forPlannerCollection($this->plannerCollection)
            ->create();
        $secondChapter = $this->plannerCollectionChapters->get(1);
        $secondChapter->setCollectionRelation($activatedCollection2);

        $this->expectException(LogicException::class);
        $this->chapterRepository->overwriteAttributesWithoutCommit($secondChapter, $attributes);
    }

    #[Test]
    public function it_throws_an_exception_when_calling_overwrite_attributes_on_non_inherited_chapter(): void
    {
        $this->setupActivatedCollectionAndChaptersForUser();
        $attributes = [
            'name' => 'new name',
            'order' => 1337,
        ];
        $this->expectExceptionMessage('You can only overwrite inherited.');
        $this->chapterWithOverwrites->setCollectionRelation($this->plannerCollection);
        $this->chapterRepository->overwriteAttributes(
            $this->chapterWithOverwrites,
            $attributes,
        );
    }

    #[Test]
    public function it_deletes_old_overwrites_and_creates_new_ones(): void
    {
        $this->setupActivatedCollectionAndChaptersForUser();
        $this->chapterWithOverwrites->setCollectionRelation($this->activatedCollection);
        $this->chapterRepository->overwriteAttributes(
            $this->chapterWithOverwrites,
            ['name' => 'new name 1'],
        );
        $this->chapterRepository->overwriteAttributes(
            $this->chapterWithOverwrites,
            ['name' => 'new name 2'],
        );

        $attributes = [
            'chapter_id' => $this->chapterWithOverwrites->id,
            'activated_planner_collection_id' => $this->activatedCollection->id,
            'user_id' => $this->user->id,
            'column' => ChapterOverwriteColumn::Name->value,
        ];

        ChapterOverwrite::setQueryingEnabled();
        $deletedOverwrite = ChapterOverwrite::withTrashed()
            ->where($attributes)
            ->whereNotNull('deleted_at')
            ->orderByDesc('id')
            ->first();

        ChapterOverwrite::setQueryingDisabled();
        $this->assertNotNull($deletedOverwrite);
        $this->assertEquals('new name 1', $deletedOverwrite->value);
        $this->assertTrue($deletedOverwrite->trashed());

        $this->assertOverwritesExist(
            $this->chapterWithOverwrites,
            [
                'name' => 'new name 2',
                'order' => 666,
                'deleted_at' => null,
            ],
        );
    }

    #[Test]
    public function it_does_not_create_an_overwrite_for_an_attribute_that_did_not_change(): void
    {
        $this->setupActivatedCollectionAndChaptersForUser();
        $this->withQueryingEnabledOnModels(fn(): bool => ChapterOverwrite::query()->delete());
        $this->withQueryingEnabledOnModels(
            fn(): bool => Chapter::where('id', $this->chapterWithOverwrites->id)->update([
                'name' => 'nam & e',
                'order' => 123,
            ]),
        );
        $chapter = $this->chapterRepository
            ->getAllByUids([$this->chapterWithOverwrites->uid], $this->activatedCollection)
            ->first();

        $this->chapterRepository->overwriteAttributes(
            $chapter,
            [
                ChapterOverwriteColumn::Name->value => 'nam & e',
                ChapterOverwriteColumn::Order->value => 666,
            ],
        );

        $this->assertEquals(1, $this->withQueryingEnabledOnModels(fn() => ChapterOverwrite::count()));
        $this->assertSame(
            ChapterOverwriteColumn::Order,
            $this->withQueryingEnabledOnModels(fn() => ChapterOverwrite::first()->column),
        );
    }

    #[Test]
    public function it_does_not_create_an_overwrite_for_an_attribute_with_existing_overwrite_with_same_value(): void
    {
        $this->setupActivatedCollectionAndChaptersForUser();
        $chapter = $this->chapterRepository
            ->getAllByUids([$this->chapterWithOverwrites->uid], $this->activatedCollection)
            ->first();

        $this->assertEquals(
            3,
            $this->withQueryingEnabledOnModels(fn(): int => ChapterOverwrite::withTrashed()->count()),
        );

        $this->chapterRepository->overwriteAttributes(
            $chapter,
            [
                ChapterOverwriteColumn::Name->value => 'Number of the beast',
                ChapterOverwriteColumn::Order->value => 1234,
                ChapterOverwriteColumn::DeletedAt->value => null,
            ],
        );

        // Only 1 additional overwrite creatd, for the order attribute, the others didn't change.
        $this->assertEquals(
            4,
            $this->withQueryingEnabledOnModels(fn(): int => ChapterOverwrite::withTrashed()->count()),
        );
        $this->assertEquals(
            1,
            $this->withQueryingEnabledOnModels(fn(): int => ChapterOverwrite::onlyTrashed()->count()),
        );
        $this->assertSame(
            ChapterOverwriteColumn::Order,
            $this->withQueryingEnabledOnModels(fn() => ChapterOverwrite::orderByDesc('id')->first()->column),
        );
    }

    #[Test]
    public function it_removes_an_overwrite_if_the_value_of_an_attribute_returns_to_the_original_value(): void
    {
        $this->setupActivatedCollectionAndChaptersForUser();
        $this->withQueryingEnabledOnModels(fn() => Chapter::where('id', $this->chapterWithOverwrites->id)->update([
            'name' => 'name',
            'order' => 123,
        ]));
        $chapter = $this->chapterRepository
            ->getAllByUids([$this->chapterWithOverwrites->uid], $this->activatedCollection)
            ->first();

        $this->assertEquals(3, $this->withQueryingEnabledOnModels(fn() => ChapterOverwrite::count()));

        $this->chapterRepository->overwriteAttributes(
            $chapter,
            [
                ChapterOverwriteColumn::Name->value => 'Number of the beast',
                ChapterOverwriteColumn::Order->value => 123,
            ],
        );

        $this->assertEquals(2, $this->withQueryingEnabledOnModels(fn() => ChapterOverwrite::count()));
        $this->assertEquals(
            1,
            $this->withQueryingEnabledOnModels(fn(): int => ChapterOverwrite::onlyTrashed()->count()),
        );
        $this->assertSame(
            ChapterOverwriteColumn::Order,
            $this->withQueryingEnabledOnModels(fn() => ChapterOverwrite::onlyTrashed()->first()->column),
        );
    }

    #[Test]
    public function it_validates_the_attribute_keys_and_values(): void
    {
        $this->setupActivatedCollectionAndChaptersForUser();
        $attributes = [
            'name' => str_repeat('a', 256),
            'order' => 'This is not an integer.',
            'invalid_prop' => 'This should fail.',
        ];

        $exception = null;
        try {
            $this->chapterWithOverwrites->setCollectionRelation($this->activatedCollection);
            $this->chapterRepository->overwriteAttributes(
                $this->chapterWithOverwrites,
                $attributes,
            );
        } catch (ValidationException $validationException) {
            $exception = $validationException;
        }

        $this->assertInstanceOf(ValidationException::class, $exception);

        $this->assertEquals([
            'name.value' => ['Name.value mag niet uit meer dan 255 tekens bestaan.'],
            'order.value' => ['Order.value moet een getal zijn.'],
        ], $exception->errors());

        PlannerCollectionOverwrite::setQueryingEnabled();
        $this->assertEquals(0, PlannerCollectionOverwrite::count());
        PlannerCollectionOverwrite::setQueryingDisabled();
    }

    #[Test]
    public function it_throws_an_exception_if_you_reorder_without_collection_context(): void
    {
        $this->expectException(LogicException::class);
        $plannerCollection = app(PlannerCollectionFactory::class)->create();
        $chapter = app(ChapterFactory::class)
            ->forPlannerCollection($plannerCollection)
            ->setOrder(1)
            ->create();

        $this->chapterRepository->setOrder($chapter, 3);
    }

    #[Test]
    public function it_reorders_chapters_for_collections_attached_to_a_user_collection(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->create();
        $chapter = app(ChapterFactory::class)
            ->forPlannerCollection($plannerCollection)
            ->setOrder(1)
            ->create();

        $chapter->setCollectionRelation($plannerCollection);

        $this->chapterRepository->setOrder($chapter, 3);

        Chapter::setQueryingEnabled();
        ChapterOverwrite::setQueryingEnabled();
        PlannerCollection::setQueryingEnabled();
        $this->assertEquals(3, $chapter->fresh()->order);
        $this->assertSame(0, ChapterOverwrite::count());
        Chapter::setQueryingDisabled();
        ChapterOverwrite::setQueryingDisabled();
        PlannerCollection::setQueryingDisabled();
    }

    #[Test]
    public function it_reorders_chapters_for_collections_attached_to_an_activated_collection(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->create();
        $chapter = app(ChapterFactory::class)
            ->setOrder(1)
            ->forPlannerCollection($plannerCollection)
            ->create();

        $user = User::factory()->create();
        Auth::setUser($user);
        $activatedCollection = ActivatedPlannerCollection::factory()
            ->forUser($user)
            ->forPlannerCollection($plannerCollection)
            ->create();

        $chapter->setCollectionRelation($activatedCollection);

        $this->chapterRepository->setOrder($chapter, 3);
        $this->chapterRepository->commitOverwrites();

        Chapter::setQueryingEnabled();
        ChapterOverwrite::setQueryingEnabled();
        PlannerCollection::setQueryingEnabled();
        // It should not update original.
        $this->assertEquals(1, $chapter->fresh()->order);

        // But it should have created the overwrites.
        $this->assertSame(1, ChapterOverwrite::count());
        $chapterOverwrite = ChapterOverwrite::whereChapterId($chapter->id)->first();
        $this->assertEquals(ChapterOverwriteColumn::Order, $chapterOverwrite->column);
        $this->assertEquals(3, $chapterOverwrite->value);
        Chapter::setQueryingDisabled();
        ChapterOverwrite::setQueryingDisabled();
        PlannerCollection::setQueryingDisabled();
    }

    #[Test]
    public function it_returns_a_template_chapter_for_planner_collection(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->create();
        Chapter::setQueryingEnabled();
        PlannerCollection::setQueryingEnabled();
        $chapter = Chapter::factory()
            ->forPlannerCollection($plannerCollection)
            ->setIsTemplate(true)
            ->create();
        Chapter::setQueryingDisabled();
        PlannerCollection::setQueryingDisabled();
        $templateChapter = $this->chapterRepository->getTemplate($plannerCollection);
        $this->assertEquals($chapter->uid, $templateChapter->uid);
        $this->assertEquals($chapter->planner_collection_id, $templateChapter->planner_collection_id);
    }

    #[Test]
    public function it_creates_a_template_chapter_for_planner_collection_if_one_does_not_exist(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->create();
        $chapter = $this->chapterRepository->getAllByPlannerCollection($plannerCollection)->first();

        $this->assertNull($chapter);
        $this->chapterRepository->getTemplate($plannerCollection);

        $chapters = $this->chapterRepository->getAllByPlannerCollection($plannerCollection);

        $this->assertCount(1, $chapters);
    }

    private function assertOverwritesExist(Chapter $chapter, array $overwrites): void
    {
        ChapterOverwrite::setQueryingEnabled();

        $this->assertEquals(count($overwrites), ChapterOverwrite::where('chapter_id', $chapter->id)->count());

        foreach ($overwrites as $column => $value) {
            $attributes = [
                'chapter_id' => $chapter->id,
                'activated_planner_collection_id' => $this->activatedCollection->id,
                'user_id' => $this->user->id,
                'column' => $column,
            ];
            $chapterOverwrites = ChapterOverwrite::where($attributes)->get();
            $this->assertEquals(1, $chapterOverwrites->count());
            $this->assertEquals($value, $chapterOverwrites->first()->value);
        }

        ChapterOverwrite::setQueryingDisabled();
    }
}
