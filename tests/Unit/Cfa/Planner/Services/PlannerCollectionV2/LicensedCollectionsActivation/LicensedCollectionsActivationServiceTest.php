<?php

namespace Tests\Unit\Cfa\Planner\Services\PlannerCollectionV2\LicensedCollectionsActivation;

use App\Models\Feature\Feature;
use App\Models\Feature\FeatureToggle;
use Carbon\Carbon;
use Cfa\Common\Domain\Permission\CollectionLicense;
use Cfa\Common\Domain\Permission\LicensePortalLicenceType;
use Cfa\Common\Domain\Permission\LicensePortalProductType;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Application\Services\Collection\CreateDraftCollection;
use Cfa\Planner\Application\Services\Collection\SyncDraftCollection;
use Cfa\Planner\Application\Services\PlannerCollectionV2\LicensedCollectionsActivation\LicensedCollectionsActivationService;
use Cfa\Planner\Domain\Collection\PublisherCollection\Publisher\Publisher;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\ArchiveRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\ServiceTestCase;

class LicensedCollectionsActivationServiceTest extends ServiceTestCase
{
    private const string PRODUCT_ID = 'xxx-xxx-xxx-xxx';

    /** @var LicensedCollectionsActivationService */
    protected $service;
    protected string $serviceName = LicensedCollectionsActivationService::class;
    private User $user;
    private User $user2;
    private Career $career;
    private Career $career2;
    private School $schoolWithTwoUsers;

    #[Override]
    public function setUp(): void
    {
        parent::setUp();
        FeatureToggle::where('feature', Feature::AutomaticallyActivateLicensedCollections)->first()->enable();

        $users = User::factory(5)->createQuietly();
        $schools = School::factory(5)->createQuietly();
        $this->createCollectionsWithLicenses(5);

        $this->user = $users->get(0);
        $this->user2 = $users->get(1);
        $this->schoolWithTwoUsers = $schools->get(0);
        $this->career = Career::factory()->forUser($this->user)->inSchool($this->schoolWithTwoUsers)
            ->withRole(RoleName::Teacher)->createQuietly();
        $this->career2 = Career::factory()->forUser($this->user2)->inSchool($this->schoolWithTwoUsers)
            ->withRole(RoleName::TeacherForAllClasses)->createQuietly();
    }

    #[Test]
    public function it_activates_collection_for_specific_career(): void
    {
        $collection = $this->createPlannerCollection();
        $this->createValidLicense();

        $this->assertDatabaseEmpty('activated_planner_collections');

        $this->service->activate(careerIds: [$this->career->id]);

        $this->assertOnlyOneUserHaveCollectionActivated($this->user, $collection);
    }

    #[Test]
    public function it_creates_collection_with_automatically_activated_at_date(): void
    {
        $collection = $this->createPlannerCollection();
        $this->createValidLicense();
        $date = Carbon::now()->format('Y-m-d H:i:s');
        Carbon::setTestNow($date);

        $this->assertDatabaseEmpty('activated_planner_collections');

        $this->service->activate(careerIds: [$this->career->id]);

        $this->assertDatabaseCount('activated_planner_collections', 1);
        $this->assertDatabaseHas('activated_planner_collections', [
            'planner_collection_id' => $collection->id,
            'user_id' => $this->user->id,
            'automatically_activated_at' => $date,
        ]);
    }

    #[Test]
    public function it_activates_collection_for_teacher_for_all_classes(): void
    {
        $school = School::factory()->create();
        $collection = $this->createPlannerCollection();
        $this->createValidLicense(school: $school);

        $user = User::factory()->create();
        $career = Career::factory()->forUser($user)->inSchool($school)
            ->withRole(RoleName::TeacherForAllClasses)->create();

        $this->service->activate(careerIds: [$career->id]);

        $this->assertOnlyOneUserHaveCollectionActivated($user, $collection);
    }

    #[Test]
    public function it_activates_collection_for_management(): void
    {
        $school = School::factory()->create();
        $collection = $this->createPlannerCollection();
        $this->createValidLicense(school: $school);

        $user = User::factory()->create();
        $career = Career::factory()->forUser($user)->inSchool($school)
            ->withRole(RoleName::Management)->create();

        $this->service->activate(careerIds: [$career->id]);

        $this->assertOnlyOneUserHaveCollectionActivated($user, $collection);
    }

    #[Test]
    public function it_activates_for_digiboardware_uid(): void
    {
        $digiboardwareUid = 'digiboardware_uid_test';

        $collection = $this->createPlannerCollection([
            'bingel_uid' => null,
            'digiboardware_uid' => $digiboardwareUid,
        ]);
        $this->createValidLicense(['product_id' => $digiboardwareUid]);

        $this->service->activate(productIds: [$digiboardwareUid]);

        $this->assertOnlyMainUsersHaveCollectionActivated($collection);
    }

    #[Test]
    public function it_activates_for_bingel_dc_uid(): void
    {
        $bingelDcUid = 'bingel_dc_uid_test';

        $collection = $this->createPlannerCollection([
            'bingel_uid' => null,
            'bingel_dc_uid' => $bingelDcUid,
        ]);
        $this->createValidLicense(['product_id' => $bingelDcUid]);
        $this->service->activate(productIds: [$bingelDcUid]);

        $this->assertOnlyMainUsersHaveCollectionActivated($collection);
    }

    #[Test]
    public function it_activates_again_if_previously_deleted(): void
    {
        $now = Carbon::now();
        Carbon::setTestNow($now);

        $this->createValidLicense();
        $collection = $this->createPlannerCollection();

        $this->service->activate();

        $this->assertDatabaseCount('activated_planner_collections', 2);
        $this->assertDatabaseHasActivatedCollection($this->user, $collection, archivedAt: null, deletedAt: null);
        $this->assertDatabaseHasActivatedCollection($this->user2, $collection, archivedAt: null, deletedAt: null);

        $this->archiveAllCollections();
        $this->deleteAllCollections();

        $this->assertDatabaseCount('activated_planner_collections', 2);
        $this->assertDatabaseHasActivatedCollection($this->user, $collection, archivedAt: $now, deletedAt: $now);
        $this->assertDatabaseHasActivatedCollection($this->user2, $collection, archivedAt: $now, deletedAt: $now);

        $this->service->activate();

        $this->assertDatabaseCount('activated_planner_collections', 4);
        $this->assertDatabaseHasActivatedCollection($this->user, $collection, archivedAt: null, deletedAt: null);
        $this->assertDatabaseHasActivatedCollection($this->user2, $collection, archivedAt: null, deletedAt: null);
        $this->assertDatabaseHasActivatedCollection($this->user, $collection, archivedAt: $now, deletedAt: $now);
        $this->assertDatabaseHasActivatedCollection($this->user2, $collection, archivedAt: $now, deletedAt: $now);
    }

    #[Test]
    public function it_does_not_activate_if_previously_archived(): void
    {
        $now = Carbon::now();
        Carbon::setTestNow($now);

        $this->createValidLicense();
        $collection = $this->createPlannerCollection();

        $this->service->activate();

        $this->assertDatabaseCount('activated_planner_collections', 2);
        $this->assertDatabaseHasActivatedCollection($this->user, $collection, archivedAt: null, deletedAt: null);
        $this->assertDatabaseHasActivatedCollection($this->user2, $collection, archivedAt: null, deletedAt: null);

        $this->archiveAllCollections();

        $this->assertDatabaseCount('activated_planner_collections', 2);
        $this->assertDatabaseHasActivatedCollection($this->user, $collection, archivedAt: $now, deletedAt: null);
        $this->assertDatabaseHasActivatedCollection($this->user2, $collection, archivedAt: $now, deletedAt: null);

        $this->service->activate();

        $this->assertDatabaseCount('activated_planner_collections', 2);
        $this->assertDatabaseHasActivatedCollection($this->user, $collection, archivedAt: $now, deletedAt: null);
        $this->assertDatabaseHasActivatedCollection($this->user2, $collection, archivedAt: $now, deletedAt: null);
    }

    #[Test]
    public function it_does_not_activate_collection_if_school_does_not_have_license(): void
    {
        $this->createPlannerCollection();

        $this->service->activate(careerIds: [$this->career->id]);

        $this->assertDatabaseEmpty('activated_planner_collections');
    }

    #[Test]
    public function it_does_not_activate_collection_if_collection_is_already_activated(): void
    {
        $collection = $this->createPlannerCollection();
        $this->createValidLicense();

        $this->service->activate(careerIds: [$this->career->id]);

        $this->assertOnlyOneUserHaveCollectionActivated($this->user, $collection);

        $this->service->activate(careerIds: [$this->career->id]);

        $this->assertOnlyOneUserHaveCollectionActivated($this->user, $collection);
    }

    #[Test]
    public function it_does_not_activate_for_incorrect_license_type(): void
    {
        $this->createPlannerCollection();
        $this->createValidLicense(['license_type' => LicensePortalLicenceType::HasAccessToCare]);

        $this->service->activate();

        $this->assertDatabaseEmpty('activated_planner_collections');
    }

    #[Test]
    public function it_doest_not_activate_for_expired_license(): void
    {
        $this->createPlannerCollection();

        $this->createValidLicense(['enddate' => Carbon::now()->subWeek()]);

        $this->service->activate();

        $this->assertDatabaseEmpty('activated_planner_collections');
    }

    #[Test]
    public function it_doest_not_activate_for_future_license(): void
    {
        $this->createPlannerCollection();

        $this->createValidLicense(['startdate' => Carbon::now()->addWeek()]);

        $this->service->activate();

        $this->assertDatabaseEmpty('activated_planner_collections');
    }

    #[Test]
    public function it_doest_not_activate_for_non_yearbook(): void
    {
        $this->createPlannerCollection();

        $this->createValidLicense(['product_type' => LicensePortalProductType::ReadingBox]);

        $this->service->activate();

        $this->assertDatabaseEmpty('activated_planner_collections');
    }

    #[Test]
    public function it_doest_not_activate_for_pupil(): void
    {
        $this->createPlannerCollection();
        $this->createValidLicense();

        $user = User::factory()->create();
        $career = Career::factory()->forUser($user)->inSchool($this->schoolWithTwoUsers)
            ->withRole(RoleName::Pupil)->create();

        $this->service->activate(careerIds: [$career->id]);

        $this->assertDatabaseEmpty('activated_planner_collections');
    }

    #[Test]
    public function it_activates_only_for_specific_collection(): void
    {
        $differentProductId = 'different-product-id';
        $this->createPlannerCollection(['bingel_uid' => $differentProductId]);
        $this->createValidLicense(['product_id' => $differentProductId]);

        $collection = $this->createPlannerCollection();
        $this->createValidLicense();

        $this->service->activate(productIds: [$collection->bingel_uid]);

        $this->assertOnlyMainUsersHaveCollectionActivated($collection);
    }

    #[Test]
    public function it_activates_for_specific_license_product_id(): void
    {
        $differentProductId = 'different-product-id';
        $this->createPlannerCollection(['bingel_uid' => $differentProductId]);
        $this->createValidLicense(['product_id' => $differentProductId]);

        $collection = $this->createPlannerCollection();
        $license = $this->createValidLicense();

        $this->service->activate(productIds: [$license->product_id]);

        $this->assertOnlyMainUsersHaveCollectionActivated($collection);
    }

    #[Test]
    public function it_activates_only_for_specific_school(): void
    {
        $collection = $this->createPlannerCollection();

        $school = School::factory()->create();
        $user = User::factory()->withActiveCareer($school, RoleName::Teacher)->create();

        $this->createValidLicense(school: $this->schoolWithTwoUsers);
        $this->createValidLicense(school: $school);

        $this->service->activate(schoolIds: [$school->id]);

        $this->assertOnlyOneUserHaveCollectionActivated($user, $collection);
    }

    #[Test]
    public function it_activates_when_no_parameter_passed(): void
    {
        $collection = $this->createPlannerCollection();
        $this->createValidLicense();

        $this->service->activate();

        $this->assertOnlyMainUsersHaveCollectionActivated($collection);
    }

    #[Test]
    public function it_does_not_activate_when_feature_flag_is_down(): void
    {
        FeatureToggle::where('feature', Feature::AutomaticallyActivateLicensedCollections)->first()->disable();

        $this->createPlannerCollection();
        $this->createValidLicense();

        $this->service->activate();

        $this->assertDatabaseEmpty('activated_planner_collections');
    }

    #[Test]
    public function it_does_not_activate_when_collection_is_not_published(): void
    {
        $this->createPlannerCollection(['published_at' => null]);
        $this->createValidLicense();

        $this->service->activate();

        $this->assertDatabaseEmpty('activated_planner_collections');
    }

    #[Test]
    public function it_activates_only_once_for_two_careers_in_the_same_school(): void
    {
        $school = School::factory()->create();
        $user = User::factory()->create();
        Career::factory(2)->forUser($user)->inSchool($school)->withRole(RoleName::Teacher)->create();

        $collection = $this->createPlannerCollection();
        $this->createValidLicense(school: $school);

        $this->service->activate();

        $this->assertOnlyOneUserHaveCollectionActivated($user, $collection);
    }

    #[Test]
    public function it_activates_only_for_specified_productid_school_and_career(): void
    {
        $school1 = School::factory()->create();
        $school2 = School::factory()->create();

        $user1 = User::factory()->create();
        $career1 = Career::factory()->forUser($user1)->inSchool($school1)->withRole(RoleName::Teacher)
            ->create();

        $user2 = User::factory()->create();
        $career2 = Career::factory()->forUser($user2)->inSchool($school2)->withRole(RoleName::Teacher)
            ->create();

        $collection = $this->createPlannerCollection();
        $license = $this->createValidLicense(school: $school1);
        $this->createValidLicense(school: $school2);

        $differentProductId = 'differentProductId';
        $this->createPlannerCollection(['bingel_uid' => $differentProductId]);
        $this->createValidLicense(attributes: ['product_id' => $differentProductId], school: $school1);

        $this->service->activate(
            careerIds: [$career1->id, $career2->id],
            productIds: [$license->product_id],
            schoolIds: [$school1->id],
        );

        $this->assertOnlyOneUserHaveCollectionActivated($user1, $collection);
    }

    #[Test]
    public function it_does_not_activate_if_models_are_deleted(): void
    {
        $collection = $this->createPlannerCollection(['deleted_at' => Carbon::now()]);
        $license = $this->createValidLicense();

        $this->service->activate();
        $this->assertDatabaseEmpty('activated_planner_collections');

        PlannerCollection::setQueryingEnabled();
        $collection->forceFill(['deleted_at' => null])->save();
        $license->forceFill(['deleted_at' => Carbon::now()])->save();

        $this->service->activate();
        $this->assertDatabaseEmpty('activated_planner_collections');

        $license->forceFill(['deleted_at' => null])->save();
        $this->user->forceFill(['deleted_at' => Carbon::now()])->save();
        $this->user2->forceFill(['deleted_at' => Carbon::now()])->save();

        $this->service->activate();
        $this->assertDatabaseEmpty('activated_planner_collections');

        $this->user->forceFill(['deleted_at' => null])->save();
        $this->user2->forceFill(['deleted_at' => null])->save();
        $this->career->forceFill(['deleted_at' => Carbon::now()])->save();
        $this->career2->forceFill(['deleted_at' => Carbon::now()])->save();

        $this->service->activate();
        $this->assertDatabaseEmpty('activated_planner_collections');

        $this->career->forceFill(['deleted_at' => null])->save();
        $this->career2->forceFill(['deleted_at' => null])->save();

        $this->service->activate();
        $this->assertDatabaseCount('activated_planner_collections', 2);
    }

    #[Test]
    public function it_does_not_activate_for_draft(): void
    {
        $collection = $this->createPlannerCollection();
        $this->createValidLicense();
        app(CreateDraftCollection::class)->createDraft($collection);

        $this->service->activate();

        $this->assertOnlyMainUsersHaveCollectionActivated($collection);
    }

    #[Test]
    public function it_does_not_activate_for_draft_after_it_is_synced(): void
    {
        $collection = $this->createPlannerCollection();
        $this->createValidLicense();
        app(CreateDraftCollection::class)->createDraft($collection);
        PlannerCollection::setQueryingEnabled();
        $drafts = PlannerCollection::where(['published_at' => null])->get();
        PlannerCollection::setQueryingDisabled();

        $this->assertCount(1, $drafts);

        app(SyncDraftCollection::class)->sync($drafts->first());

        $this->service->activate();

        $this->assertOnlyMainUsersHaveCollectionActivated($collection);
    }

    private function createPlannerCollection(array $attributes = []): PlannerCollection
    {
        return PlannerCollection::factory()
            ->withBingelUid(self::PRODUCT_ID)
            ->forPublisher(Publisher::VAN_IN)
            ->withPublishedAt(Carbon::now())
            ->create($attributes);
    }

    private function createCollectionsWithLicenses(int $count): void
    {
        foreach (CollectionLicense::factory($count)->create() as $license) {
            PlannerCollection::factory()->published()->withBingelUid($license->product_id)->create();
        }
    }

    private function createValidLicense(
        array $attributes = [],
        bool $withEvents = false,
        ?School $school = null,
    ): CollectionLicense {
        $license = CollectionLicense::factory()
            ->withProductType(LicensePortalProductType::Yearbook)
            ->withLicenseType(LicensePortalLicenceType::HasAccessToDc)
            ->withProductId(self::PRODUCT_ID)
            ->forSchool($school ?? $this->schoolWithTwoUsers);

        if ($withEvents) {
            return $license->createWithEvents($attributes);
        }

        return $license->create($attributes);
    }

    private function assertOnlyMainUsersHaveCollectionActivated(PlannerCollection $collection): void
    {
        $this->assertDatabaseCount('activated_planner_collections', 2);
        $this->assertDatabaseHasActivatedCollection($this->user, $collection, archivedAt: null, deletedAt: null);
        $this->assertDatabaseHasActivatedCollection($this->user2, $collection, archivedAt: null, deletedAt: null);
    }

    private function assertOnlyOneUserHaveCollectionActivated(User $user, PlannerCollection $collection): void
    {
        $this->assertDatabaseCount('activated_planner_collections', 1);
        $this->assertDatabaseHasActivatedCollection($user, $collection, archivedAt: null, deletedAt: null);
    }

    private function assertDatabaseHasActivatedCollection(
        User $user,
        PlannerCollection $collection,
        ?Carbon $archivedAt,
        ?Carbon $deletedAt,
    ): void {
        $this->assertDatabaseHas('activated_planner_collections', [
            'planner_collection_id' => $collection->id,
            'user_id' => $user->id,
            'archived_at' => $archivedAt,
            'deleted_at' => $deletedAt,
        ]);
    }

    private function archiveAllCollections(): void
    {
        $archiveRepository = app(ArchiveRepositoryInterface::class);
        ActivatedPlannerCollection::setQueryingEnabled();
        ActivatedPlannerCollection::all()->each(fn(ActivatedPlannerCollection $activatedPlannerCollection) =>
            $archiveRepository->archive($activatedPlannerCollection, $this->user));
        ActivatedPlannerCollection::setQueryingDisabled();
    }

    private function deleteAllCollections(): void
    {
        $collectionRepository = app(PlannerCollectionRepositoryInterface::class);
        ActivatedPlannerCollection::setQueryingEnabled();
        ActivatedPlannerCollection::all()->each(fn(ActivatedPlannerCollection $activatedPlannerCollection) =>
            $collectionRepository->delete($activatedPlannerCollection));
        ActivatedPlannerCollection::setQueryingDisabled();
    }
}
