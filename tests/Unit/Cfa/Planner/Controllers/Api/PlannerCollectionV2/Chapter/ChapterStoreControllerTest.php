<?php

namespace Tests\Unit\Cfa\Planner\Controllers\Api\PlannerCollectionV2\Chapter;

use Cfa\Common\Domain\User\User;
use Cfa\Planner\Application\Controllers\Api\PlannerCollectionV2\Chapter\ChapterStoreRequest;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\Chapter\Chapter;
use Cfa\Planner\Domain\CollectionV2\Chapter\ChapterOverwriteColumn;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Factories\Planner\ChapterFactory;
use Tests\Factories\Planner\ChapterOverwriteFactory;
use Tests\Factories\Planner\PlannerCollectionFactory;
use Tests\Unit\ControllerTestCase;

class ChapterStoreControllerTest extends ControllerTestCase
{
    protected static ?string $routeName = 'v2.api.collections.chapters.store';

    private PlannerCollection $userCollection;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->userCollection = app(PlannerCollectionFactory::class)->forUser($this->owner)->create();

        $this->setUrlParameters(
            [
                'collectionv2' => $this->userCollection,
            ],
        );
    }

    #[Test]
    public function it_allows_creating_new_chapter(): void
    {
        $data = [
            ChapterStoreRequest::NAME => $this->faker->name(),
        ];

        $response = $this->postJsonWithRequiredHeaders($data);
        $this->enableQuerying();
        $chapter = Chapter::where('name', $data['name'])->first();

        $response->assertJsonCreatedResponse($chapter->uid);
        $this->assertSame($data['name'], $chapter->name);
        $this->assertSame(1, $chapter->order);
        $this->assertEquals($this->userCollection->id, $chapter->planner_collection_id);
    }

    #[Test]
    public function it_adds_1_to_the_order_of_latest_chapter_order(): void
    {
        $existingChapter = app(ChapterFactory::class)->setOrder(8)
            ->forPlannerCollection($this->userCollection)
            ->create();

        $data = [
            ChapterStoreRequest::NAME => $this->faker->name(),
        ];

        $this->postJsonWithRequiredHeaders($data);

        $this->enableQuerying();
        $chapter = Chapter::where('name', $data['name'])->first();
        $this->assertSame($existingChapter->order + 1, $chapter->order);
    }

    #[Test]
    public function it_creates_chapters_on_activated_collection(): void
    {
        $debbie = User::factory()->withActiveCareer($this->school)->create();
        $debbiesCollection = app(PlannerCollectionFactory::class)->forUser($debbie)->create();
        $debbiesChapter = app(ChapterFactory::class)->setOrder(8)
            ->forPlannerCollection($debbiesCollection)
            ->create();
        $nicksCollection = ActivatedPlannerCollection::factory()->forUser($this->owner)
            ->forPlannerCollection($debbiesCollection)
            ->create();

        $this->setUrlParameters(
            [
                'collectionv2' => $nicksCollection,
            ],
        );

        $data = [
            ChapterStoreRequest::NAME => $this->faker->name(),
        ];

        $this->postJsonWithRequiredHeaders($data);

        $this->enableQuerying();
        $chapter = Chapter::where('name', $data['name'])->first();
        $this->assertSame($debbiesChapter->order + 1, $chapter->order);
        $this->assertEquals($nicksCollection->id, $chapter->activated_planner_collection_id);
    }

    #[Test]
    public function it_creates_chapters_on_activated_collection_with_order_overwrite(): void
    {
        $debbie = User::factory()->withActiveCareer($this->school)->create();
        $debbiesCollection = app(PlannerCollectionFactory::class)->forUser($debbie)->create();
        $debbiesChapter = app(ChapterFactory::class)->setOrder(8)
            ->forPlannerCollection($debbiesCollection)
            ->create();
        $nicksCollection = ActivatedPlannerCollection::factory()->forUser($this->owner)
            ->forPlannerCollection($debbiesCollection)
            ->create();

        $newOrderByOverwrite = 12;
        app(ChapterOverwriteFactory::class)->forChapter($debbiesChapter)
            ->forColumn(ChapterOverwriteColumn::Order)
            ->withValue($newOrderByOverwrite)
            ->forActivatedPlannerCollection($nicksCollection)
            ->forUser($this->owner)
            ->create();

        $this->setUrlParameters(
            [
                'collectionv2' => $nicksCollection,
            ],
        );

        $data = [
            ChapterStoreRequest::NAME => $this->faker->name(),
        ];

        $this->postJsonWithRequiredHeaders($data);

        $this->enableQuerying();
        $chapter = Chapter::where('name', $data['name'])->first();
        $this->assertSame($newOrderByOverwrite + 1, $chapter->order);
    }

    #[Test]
    public function it_throws_a_not_found_error_if_the_collection_doesnt_exists(): void
    {
        $this->setUrlParameters([
            'collectionv2' => 'invalidCollection',
        ]);
        $this->postJsonWithRequiredHeaders()
            ->assertNotFound();
    }

    #[Test]
    public function it_throws_a_forbidden_exception_if_the_user_is_not_the_owner_of_the_collection(): void
    {
        $this->setAuthenticatedUser(User::factory()->create());
        $this->postJsonWithRequiredHeaders()
            ->assertJsonForbiddenResponse();
    }

    #[Test]
    public function it_throws_an_unprocessable_entity_error_if_the_body_is_invalid(): void
    {
        $this->postJsonWithRequiredHeaders([
            ChapterStoreRequest::NAME => '',
        ])
            ->assertJsonValidationErrors(['name' => 'Naam is verplicht']);
    }

    #[Test]
    public function it_returns_correct_validation_errors_with_incorrect_parameters(): void
    {
        $this->postJsonWithRequiredHeaders([
            ChapterStoreRequest::NAME => $this->faker->randomNumber(),
        ])->assertJsonValidationErrors(['name' => 'Naam moet een tekst zijn']);
    }

    private function enableQuerying(): void
    {
        PlannerCollection::setQueryingEnabled();
        Chapter::setQueryingEnabled();
    }
}
