<?php

namespace Tests\Unit\Cfa\Planner\Controllers\Api\PlannerCollectionV2;

use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Factories\Planner\PlannerCollectionFactory;
use Tests\Unit\ControllerTestCase;

use function app;

class PlannerCollectionDearchiveControllerTest extends ControllerTestCase
{
    protected static ?string $routeName = 'v2.api.collections.dearchive';

    private PlannerCollection $plannerCollection;

    private ActivatedPlannerCollection $activatedCollection;

    private PlannerCollection $userCollection;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();
        $this->userCollection = app(PlannerCollectionFactory::class)->archived()->forUser($this->owner)->create();
        PlannerCollection::setQueryingDisabled();
        $this->plannerCollection = app(PlannerCollectionFactory::class)->create();
        $this->activatedCollection = ActivatedPlannerCollection::factory()
            ->archived()
            ->forUser($this->owner)
            ->forPlannerCollection($this->plannerCollection)
            ->create();
    }

    #[Test]
    public function it_dearchives_a_user_collection_when_archived(): void
    {
        $this->setUrlParameters(['collectionv2' => $this->userCollection->uid])
            ->patchJsonWithRequiredHeaders()
            ->assertJsonUpdatedResponse($this->userCollection->uid);

        PlannerCollection::setQueryingEnabled();
        $this->userCollection->refresh();
        PlannerCollection::setQueryingDisabled();

        $this->assertNull($this->userCollection->archived_at);
    }

    #[Test]
    public function it_dearchives_an_activated_collection_when_archived(): void
    {
        $this->setUrlParameters(['collectionv2' => $this->activatedCollection->uid])
            ->patchJsonWithRequiredHeaders()
            ->assertJsonUpdatedResponse($this->activatedCollection->uid);

        ActivatedPlannerCollection::setQueryingEnabled();
        $this->activatedCollection->refresh();
        ActivatedPlannerCollection::setQueryingDisabled();

        $this->assertNull($this->activatedCollection->archived_at);
    }
}
