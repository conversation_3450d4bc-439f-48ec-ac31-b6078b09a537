<?php

namespace Tests\Unit\Cfa\Planner\Controllers\Api\PlannerCollectionV2;

use Carbon\Carbon;
use Cfa\Common\Application\Services\Helpers\SchoolHelperService;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\CollectionV2\AccessLevel;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\ShareAccessStatusRepositoryInterface;
use Illuminate\Support\Collection;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Factories\Planner\PlannerCollectionFactory;
use Tests\Unit\ControllerTestCase;

class SharePlannerCollectionControllerTest extends ControllerTestCase
{
    protected static ?string $routeName = 'v2.api.collections.share';

    protected PlannerCollection $plannerCollection;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        Carbon::setTestNow(Carbon::now()->startOfDay());

        PlannerCollection::setQueryingEnabled();
        $this->plannerCollection = app(PlannerCollectionFactory::class)
            ->setName('Test')
            ->forUser($this->owner)
            ->create();
        PlannerCollection::setQueryingDisabled();
        $this->mock(SchoolHelperService::class)->shouldReceive('school')->andReturn($this->school);
        $this->setUrlParameters(['collectionv2' => $this->plannerCollection->uid]);
    }

    #[Test]
    public function it_fetches_the_current_collection_access_for_users(): void
    {
        $user = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $parameters = [
            'school_access' => false,
            'users' => [
                [
                    'uid' => $user->uid,
                    'access' => AccessLevel::READ,
                ],
            ],
        ];
        $this->mock(ShareAccessStatusRepositoryInterface::class)
            ->makePartial()
            ->shouldReceive('get')
            ->withArgs(fn(Collection $userUids, PlannerCollection $collection): bool => $userUids->first() ===
                $user->uid &&
                $collection->uid === $this->plannerCollection->uid)
            ->once()
            ->andReturn(new Collection());

        $this->postJsonWithRequiredHeaders($parameters)
            ->assertJsonNoContentResponse();
    }

    #[Test]
    public function it_saves_the_school_access_for_the_current_school(): void
    {
        $extraSchool = School::factory()->inKathOndVla()->create();
        $this->plannerCollection->schoolsWithAccess()->sync([$extraSchool->id]);
        $parameters = [
            'school_access' => true,
            'users' => [],
        ];
        $this->postJsonWithRequiredHeaders($parameters)
            ->assertJsonNoContentResponse();
        $schoolsWithAccess = $this->plannerCollection->schoolsWithAccess()->get();
        $this->assertCount(2, $schoolsWithAccess);
        $this->assertEquals($schoolsWithAccess->pluck('uid')->toArray(), [$this->school->uid, $extraSchool->uid]);
    }

    #[Test]
    public function it_doesnt_fail_to_save_the_school_access_when_school_already_has_access(): void
    {
        $extraSchool = School::factory()->inKathOndVla()->create();
        $this->plannerCollection->schoolsWithAccess()->sync([$extraSchool->id, $this->school->id]);
        $parameters = [
            'school_access' => true,
            'users' => [],
        ];
        $this->postJsonWithRequiredHeaders($parameters)
            ->assertJsonNoContentResponse();
        $schoolsWithAccess = $this->plannerCollection->schoolsWithAccess()->get();
        $this->assertCount(2, $schoolsWithAccess);
        $this->assertEquals($schoolsWithAccess->pluck('uid')->toArray(), [$this->school->uid, $extraSchool->uid]);
    }

    #[Test]
    public function it_revokes_the_school_access_for_the_current_school(): void
    {
        $extraSchool = School::factory()->inKathOndVla()->create();
        $this->plannerCollection->schoolsWithAccess()->sync([$this->school->id, $extraSchool->id]);
        $parameters = [
            'school_access' => false,
            'users' => [],
        ];
        $schoolsWithAccess = $this->plannerCollection->schoolsWithAccess()->get();
        $this->assertEquals($schoolsWithAccess->pluck('uid')->toArray(), [$this->school->uid, $extraSchool->uid]);
        $this->postJsonWithRequiredHeaders($parameters)
            ->assertJsonNoContentResponse();
        $schoolsWithAccess = $this->plannerCollection->schoolsWithAccess()->get();
        $this->assertCount(1, $schoolsWithAccess);
        $this->assertEquals($schoolsWithAccess->first()->uid, $extraSchool->uid);
    }

    #[Test]
    public function it_validates_the_presence_of_school_access(): void
    {
        $parameters = [
            'users' => [],
        ];
        $this->postJsonWithRequiredHeaders($parameters)
            ->assertJsonValidationErrors(['school_access' => 'School access is verplicht.']);
    }

    #[Test]
    public function it_validates_the_boolean_type_for_school_access(): void
    {
        $parameters = [
            'school_access' => 'no boolean',
            'users' => [],
        ];
        $this->postJsonWithRequiredHeaders($parameters)
            ->assertJsonValidationErrors(['school_access' => 'School access moet ‘ja’ of ‘nee’ zijn.']);
    }

    #[Test]
    public function it_gives_read_access_to_users_without_access(): void
    {
        $user1 = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $user2 = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $parameters = [
            'school_access' => false,
            'users' => [
                [
                    'uid' => $user1->uid,
                    'access' => AccessLevel::READ,
                ],
                [
                    'uid' => $user2->uid,
                    'access' => AccessLevel::READ,
                ],
            ],
        ];

        $this->postJsonWithRequiredHeaders($parameters)
            ->assertJsonNoContentResponse();

        $this->assertUserHasReadAccess($user1->id);
        $this->assertUserHasReadAccess($user2->id);
    }

    #[Test]
    public function it_gives_write_access_to_users_without_access(): void
    {
        $user1 = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $user2 = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $parameters = [
            'school_access' => false,
            'users' => [
                [
                    'uid' => $user1->uid,
                    'access' => AccessLevel::WRITE,
                ],
                [
                    'uid' => $user2->uid,
                    'access' => AccessLevel::WRITE,
                ],
            ],
        ];

        $this->postJsonWithRequiredHeaders($parameters)
            ->assertJsonNoContentResponse();

        $this->assertUserHasWriteAccess($user1->id);
        $this->assertUserHasWriteAccess($user2->id);
    }

    #[Test]
    public function it_does_nothing_for_users_with_the_same_access_level(): void
    {
        $readAccessUser = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $this->giveReadAccess($readAccessUser);
        $parameters = [
            'school_access' => false,
            'users' => [
                [
                    'uid' => $readAccessUser->uid,
                    'access' => AccessLevel::READ,
                ],
            ],
        ];

        $this->postJsonWithRequiredHeaders($parameters)
            ->assertJsonNoContentResponse();

        $this->assertUserHasReadAccess($readAccessUser->id);
    }

    #[Test]
    public function it_gives_read_access_to_users_with_write_access(): void
    {
        $user1 = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $user2 = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $this->giveWriteAccess($user1);
        $this->giveWriteAccess($user2);

        $parameters = [
            'school_access' => false,
            'users' => [
                [
                    'uid' => $user1->uid,
                    'access' => AccessLevel::READ,
                ],
                [
                    'uid' => $user2->uid,
                    'access' => AccessLevel::READ,
                ],
            ],
        ];

        $this->postJsonWithRequiredHeaders($parameters)
            ->assertJsonNoContentResponse();

        $this->assertUserHasReadAccess($user1->id);
        $this->assertUserHasReadAccess($user2->id);
    }

    #[Test]
    public function it_gives_write_access_to_users_with_read_access(): void
    {
        $user1 = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $user2 = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $this->giveReadAccess($user1);
        $this->giveReadAccess($user2);

        $parameters = [
            'school_access' => false,
            'users' => [
                [
                    'uid' => $user1->uid,
                    'access' => AccessLevel::WRITE,
                ],
                [
                    'uid' => $user2->uid,
                    'access' => AccessLevel::WRITE,
                ],
            ],
        ];

        $this->postJsonWithRequiredHeaders($parameters)
            ->assertJsonNoContentResponse();

        $this->assertUserHasWriteAccess($user1->id);
        $this->assertUserHasWriteAccess($user2->id);
    }

    #[Test]
    public function it_revokes_access_to_users_that_did_not_activate_the_collection(): void
    {
        $readAccessUser = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $writeAccessUser = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $this->giveReadAccess($readAccessUser);
        $this->giveWriteAccess($writeAccessUser);

        $parameters = [
            'school_access' => false,
            'users' => [
                [
                    'uid' => $readAccessUser->uid,
                    'access' => AccessLevel::NO_ACCESS,
                ],
                [
                    'uid' => $writeAccessUser->uid,
                    'access' => AccessLevel::NO_ACCESS,
                ],
            ],
        ];

        $this->postJsonWithRequiredHeaders($parameters)
            ->assertJsonNoContentResponse();

        $this->assertUserHasNoAccess($readAccessUser->id);
        $this->assertUserHasNoAccess($writeAccessUser->id);
    }

    #[Test]
    public function it_does_not_revokes_access_to_users_that_did_activate_the_collection(): void
    {
        $readAccessUser = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $writeAccessUser = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $this->giveReadAccess($readAccessUser);
        $this->giveWriteAccess($writeAccessUser);
        ActivatedPlannerCollection::factory()
            ->forUser($readAccessUser)
            ->forPlannerCollection($this->plannerCollection)
            ->create();
        ActivatedPlannerCollection::factory()
            ->forUser($writeAccessUser)
            ->forPlannerCollection($this->plannerCollection)
            ->create();

        $parameters = [
            'school_access' => false,
            'users' => [
                [
                    'uid' => $readAccessUser->uid,
                    'access' => AccessLevel::NO_ACCESS,
                ],
                [
                    'uid' => $writeAccessUser->uid,
                    'access' => AccessLevel::NO_ACCESS,
                ],
            ],
        ];

        $this->postJsonWithRequiredHeaders($parameters)
            ->assertJsonNoContentResponse();

        $this->assertUserHasReadAccess($readAccessUser->id);
        $this->assertUserHasWriteAccess($writeAccessUser->id);
    }

    #[Test]
    public function it_sets_the_activated_collection_to_read_only_when_collection_is_activated(): void
    {
        $writeAccessUser = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $this->giveWriteAccess($writeAccessUser);
        $activatedPlannerCollection = ActivatedPlannerCollection::factory()
            ->forUser($writeAccessUser)
            ->forPlannerCollection($this->plannerCollection)
            ->writeAccess()
            ->create();

        $parameters = [
            'school_access' => false,
            'users' => [
                [
                    'uid' => $writeAccessUser->uid,
                    'access' => AccessLevel::READ,
                ],
            ],
        ];

        $this->postJsonWithRequiredHeaders($parameters)
            ->assertJsonNoContentResponse();

        $this->assertUserHasReadAccess($writeAccessUser->id);
        $this->assertDatabaseHas(
            'activated_planner_collections',
            ['id' => $activatedPlannerCollection->id, 'is_writable' => false],
        );
    }

    #[Test]
    public function it_keeps_the_read_activated_collections_when_going_from_read_to_write(): void
    {
        $readAccessUser = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $this->giveReadAccess($readAccessUser);
        $activatedPlannerCollection = ActivatedPlannerCollection::factory()
            ->forUser($readAccessUser)
            ->forPlannerCollection($this->plannerCollection)
            ->create();

        $parameters = [
            'school_access' => false,
            'users' => [
                [
                    'uid' => $readAccessUser->uid,
                    'access' => AccessLevel::WRITE,
                ],
            ],
        ];

        $this->postJsonWithRequiredHeaders($parameters)
            ->assertJsonNoContentResponse();

        $this->assertUserHasWriteAccess($readAccessUser->id);
        $this->assertDatabaseHas(
            'activated_planner_collections',
            ['id' => $activatedPlannerCollection->id, 'is_writable' => false],
        );
        $this->assertDatabaseMissing(
            'activated_planner_collections',
            [
                'planner_collection_id' => $this->plannerCollection->id,
                'user_id' => $readAccessUser->id,
                'is_writable' => true,
            ],
        );
    }

    #[Test]
    public function it_responds_forbidden_when_user_is_not_the_owner(): void
    {
        $colleague = User::factory()->create();
        $this->plannerCollection->usersWithWriteAccess()->attach($colleague);

        $this->setAuthenticatedUser($colleague)
            ->postJsonWithRequiredHeaders()
            ->assertForbidden();
    }

    #[Test]
    public function it_throws_error_on_invalid_access_type(): void
    {
        $user = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $parameters = [
            'school_access' => false,
            'users' => [
                [
                    'uid' => $user->uid,
                    'access' => 'forbidden access',
                ],
            ],
        ];

        $this->postJsonWithRequiredHeaders($parameters)
            ->assertUnprocessable();
    }

    private function assertUserHasReadAccess(int $userId): void
    {
        $this->assertDatabaseHas(
            'planner_collections_v2_access',
            [
                'plannercollection_id' => $this->plannerCollection->id,
                'entity_type' => 'user',
                'entity_id' => $userId,
                'is_writable' => false,
            ],
        );
    }

    private function assertUserHasWriteAccess(int $userId): void
    {
        $this->assertDatabaseHas(
            'planner_collections_v2_access',
            [
                'plannercollection_id' => $this->plannerCollection->id,
                'entity_type' => 'user',
                'entity_id' => $userId,
                'is_writable' => true,
            ],
        );
    }

    private function assertUserHasNoAccess(int $userId): void
    {
        $this->assertDatabaseMissing(
            'planner_collections_v2_access',
            [
                'plannercollection_id' => $this->plannerCollection->id,
                'entity_type' => 'user',
                'entity_id' => $userId,
            ],
        );
    }

    private function giveReadAccess(User $user): void
    {
        $this->plannerCollection->usersWithReadAccess()->attach($user);
    }

    private function giveWriteAccess(User $user): void
    {
        $this->plannerCollection->usersWithWriteAccess()->attach($user);
    }
}
