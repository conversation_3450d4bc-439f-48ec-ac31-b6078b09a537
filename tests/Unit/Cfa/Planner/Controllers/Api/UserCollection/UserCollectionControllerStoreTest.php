<?php

namespace Tests\Unit\Cfa\Planner\Controllers\Api\UserCollection;

use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudience;
use Cfa\Planner\Domain\Collection\CollectionTemplate;
use PHPUnit\Framework\Attributes\Test;

class UserCollectionControllerStoreTest extends UserCollectionControllerTestCase
{
    protected static ?string $routeName = 'api.collections.store';

    /**
     * Test if usercollection detail is stored and belongs to the user.
     */
    #[Test]
    public function it_stores_collection_correctly(): void
    {
        $params = [
            'name' => $this->faker->word,
            'targetAudienceIds' => [TargetAudience::randomUid()],
        ];
        $response = $this->postJsonWithRequiredHeaders($params);

        // Get the last created collection based on id.
        $collection = $this->owner->usercollections()->orderBy('id', 'desc')->first();

        $response->assertJsonCreatedResponse($collection->uid);

        $this->assertEquals($collection->template, CollectionTemplate::Default);
    }

    /**
     * Test if usercollection template is saved correctly.
     */
    #[Test]
    public function it_saves_the_template_of_the_collection_correctly(): void
    {
        $template = $this->faker->randomElement(CollectionTemplate::cases());

        $params = [
            'name' => $this->faker->word,
            'template' => $template->name,
        ];

        $response = $this->postJsonWithRequiredHeaders($params);

        $collection = $this->owner->usercollections()->orderBy('id', 'desc')->first();

        $this->assertEquals($template, $collection->template);
    }

    /**
     * Test if validation error is shown when template is invalid.
     */
    #[Test]
    public function it_returns_validation_error_on_invalid_template(): void
    {
        $params = [
            'name' => $this->faker->word,
            'template' => 'INVALID',
        ];

        $expectedMessage = 'Het veld template moet een geldig waarde hebben.';
        $response = $this->postJsonWithRequiredHeaders($params)
            ->assertJsonValidationErrors(['template' => $expectedMessage]);
    }

    /**
     * Test creates chapters automatically considering the template of the collection.
     */
    #[Test]
    public function it_creates_chapters_considering_the_template_of_the_collection(): void
    {
        $template = CollectionTemplate::Activity->name;

        config([
            'tenants.sol-fl.chapters.' . $template => [
                'This is a chapter',
                'This is another chapter',
            ],
        ]);

        $params = [
            'name' => $this->faker->word,
            'template' => $template,
        ];

        $response = $this->postJsonWithRequiredHeaders($params);

        $collection = $this->owner->usercollections()->orderBy('id', 'desc')->first();

        $this->assertEquals($template, $collection->template->name);
        $this->assertCount(2, $collection->chapters);

        $this->assertEquals('This is a chapter', $collection->chapters->first()->name);
        $this->assertEquals('This is another chapter', $collection->chapters->last()->name);
    }

    /**
     * Tests if a status code HTTP_UNPROCESSABLE_ENTITY (422) and the correct error data are returned when trying to
     * access api.collections.store without any parameters.
     */
    #[Test]
    public function it_returns_error_without_name_parameter(): void
    {
        $this->setAuthenticatedUser($this->owner);

        $this->postJsonWithRequiredHeaders()
            ->assertJsonValidationErrors(['name' => 'Naam is verplicht']);
    }
}
