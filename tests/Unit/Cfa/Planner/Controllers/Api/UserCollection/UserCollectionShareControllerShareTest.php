<?php

namespace Tests\Unit\Cfa\Planner\Controllers\Api\UserCollection;

use Carbon\Carbon;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\Collection\Chapter\Chapter;
use Cfa\Planner\Domain\Collection\CollectionUpdatable;
use Cfa\Planner\Domain\Collection\PublisherCollection\Publisher\Publisher;
use Cfa\Planner\Domain\Collection\PublisherCollection\PublisherCollection;
use Cfa\Planner\Domain\Collection\UserCollection\UserCollection;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNode;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNodeType;
use Cfa\Planner\Domain\CurriculumNode\CurriculumType;
use Cfa\Planner\Domain\Record\Material\Material;
use Cfa\Planner\Domain\Record\Record;
use Illuminate\Support\Facades\DB;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\ControllerTestCase;

use function factory;

class UserCollectionShareControllerShareTest extends ControllerTestCase
{
    protected static ?string $routeName = 'api.collections.share';

    /**
     * The test collection.
     *
     * @var UserCollection
     */
    protected $collection;

    /**
     * The test chapter.
     *
     * @var Chapter
     */
    protected $chapter;

    /**
     * The test record.
     *
     * @var Record
     */
    protected $record;

    /**
     * The test material.
     *
     * @var Material
     */
    protected $material;

    /**
     * User to share with.
     *
     * @var User
     */
    protected $user;

    public const USER = 'user';
    public const SCHOOL = 'school';

    /**
     * {@inheritdoc}
     */
    #[Override]
    protected function setUp(): void
    {
        parent::setUp();
        factory(Publisher::class)->create();
        $this->collection = factory(UserCollection::class)->states('nonArchived')->create();
        $this->setUrlParameters(['collection' => $this->collection->uid]);

        $this->user = $this->createUsersWithRole($this->school, RoleName::Teacher);
    }

    /**
     * Sets up a child collection of the shared collection.
     * If the parent collection contains chapters, all the data of this collection is copied to the child.
     *
     * @param User $user The owner the child collection is for.
     * @param CollectionUpdatable|null $updatable The updatable value of the child collection.
     */
    protected function setUpChildCollection(User $user, ?CollectionUpdatable $updatable = null): UserCollection
    {
        $childCollection = factory(UserCollection::class)->create([
            'name' => $this->collection->name,
            'extra_info' => $this->collection->extra_info,
            'publisher_id' => null,
            'parent_collection_id' => $this->collection->id,
            'owner_id' => $user->id,
            'updatable' => $updatable ?? CollectionUpdatable::Updatable,
        ]);

        if ($this->collection->chapters->count() === 0) {
            return $childCollection;
        }

        /* @var Chapter $parentChapter */
        $parentChapter = $this->collection->chapters->first();
        /* @var Chapter $childChapter */
        $childChapter = $parentChapter->replicate();
        $childChapter->plannercollection_id = $childCollection->id;
        $childChapter->parent_chapter_id = $parentChapter->id;
        $childChapter->save();

        $parentChapter->records->each(function (Record $record) use ($childCollection, $childChapter): void {
            $record->duplicate(
                [],
                [
                    'chapter_id' => $childChapter->id,
                    'owner_id' => $childCollection->owner_id,
                    'parent_record_id' => $record->id,
                ],
            );
        });

        return $childCollection;
    }

    /**
     * Sets up the parent collection that it contains a chapter, record, curriculumnode and material.
     */
    protected function setUpCollectionDetails(): void
    {
        $this->chapter = Chapter::factory()
            ->forPlannerCollection($this->collection)
            ->create();

        $this->collection->chapters()->save($this->chapter);

        $this->record = factory(Record::class)
            ->states('sharable')
            ->create(['chapter_id' => $this->chapter->id, 'owner_id' => $this->collection->owner_id]);
        $this->chapter->records()->save($this->record);

        $curriculumnode = CurriculumNode::factory()
            ->forCurriculumType(CurriculumType::Default)
            ->create();

        $this->record->curriculumnodes()->save($curriculumnode);

        $this->material = factory(Material::class)
            ->create(['record_id' => $this->record->id]);
        $this->record->allMaterials()->save($this->material);
    }

    /**
     * Test if schools and users are added to the plannercollection_access table.
     */
    #[Test]
    public function it_adds_access_entries(): void
    {
        $this->postJsonWithRequiredHeaders($this->getParameters([$this->user->uid], [$this->school->uid]))
            ->assertJsonNoContentResponse();

        $this->assertAccessTableContainsEntry($this->user->id, self::USER);
        $this->assertAccessTableContainsEntry($this->school->id, self::SCHOOL);
    }

    /**
     * Test if schools/users that are not sent the second time are removed from the plannercollection_access table
     * and that schools/users that are only sent the second time are properly added to the table.
     */
    #[Test]
    public function it_removes_not_included_entries_and_adds_new_entries(): void
    {
        $secondUser = $this->createUsersWithRole($this->school, RoleName::Teacher);
        $newSchool = School::factory()->create();
        Career::factory()
            ->forUser($this->owner)
            ->inSchool($newSchool)
            ->withRole(RoleName::Teacher)
            ->create();

        $this->postJsonWithRequiredHeaders(
            $this->getParameters([$this->user->uid, $secondUser->uid], [$this->school->uid]),
        )->assertJsonNoContentResponse();
        $this->postJsonWithRequiredHeaders(
            $this->getParameters([$secondUser->uid], [$newSchool->uid]),
        )->assertJsonNoContentResponse();

        $this->assertAccessTableContainsEntry($secondUser->id, self::USER);
        $this->assertAccessTableContainsEntry($newSchool->id, self::SCHOOL);
        $this->assertEquals(2, DB::table('plannercollection_access')->count());
    }

    /**
     * Test if using a collection with a parent collection results in a validation error.
     */
    #[Test]
    public function it_returns_validation_error_when_sharing_an_unshareable_collection(): void
    {
        $parentCollection = factory(PublisherCollection::class)->create();
        $this->collection->parent_collection_id = $parentCollection->id;
        $this->collection->save();

        $this->setUrlParameters(['collection' => $this->collection])
            ->postJsonWithRequiredHeaders($this->getParameters())
            ->assertJsonValidationErrors(['has_no_parent_collection' => 'Has no parent collection is verplicht']);
    }

    /**
     * Test if missing parameters result in a validation error.
     */
    #[Test]
    public function it_returns_validation_error_with_missing_parameters(): void
    {
        $this->postJsonWithRequiredHeaders()
            ->assertJsonValidationErrors(['users' => 'Users moet bestaan'])
            ->assertJsonValidationErrors(['schools' => 'Schools moet bestaan']);
    }

    /**
     * Test if wrong parameter types result in a validation error.
     */
    #[Test]
    public function it_returns_validation_error_with_wrong_parameter_types(): void
    {
        $this->postJsonWithRequiredHeaders([
            'users' => [1],
            'schools' => [1],
        ])
            ->assertJsonValidationErrors(['users.0' => 'Users.0 moet een tekst zijn'])
            ->assertJsonValidationErrors(['schools.0' => 'Schools.0 moet een tekst zijn']);
    }

    /**
     * Tests if an authentication error is returned when the current user does not have permission to share
     * the usercollection.
     */
    #[Test]
    public function it_returns_authentication_error_when_user_does_not_have_permission(): void
    {
        $otherUser = User::factory()->create();
        $this->collection->owner_id = $otherUser->id;
        $this->collection->save();

        $this->postJsonWithRequiredHeaders()
            ->assertJsonForbiddenResponse();
    }

    #[Test]
    public function it_removes_schools_the_user_has_no_active_career_for(): void
    {
        $school = School::factory()->create();

        Career::factory()
            ->forUser($this->owner)
            ->inSchool($school)
            ->setStartDate(Carbon::now()->subDays(2))
            ->setEndDate(Carbon::now()->subDay())
            ->create();

        DB::table('plannercollection_access')->insert([
            'entity_type' => 'school',
            'entity_id' => $school->id,
            'plannercollection_id' => $this->collection->id,
        ]);

        $this->assertEquals(1, DB::table('plannercollection_access')->count());

        $this->postJsonWithRequiredHeaders($this->getParameters([], [$school->uid]))
            ->assertNotAnErrorResponse();

        $this->assertEquals(0, DB::table('plannercollection_access')->count());
    }

    #[Test]
    public function it_removes_users_the_user_has_no_school_in_common_with(): void
    {
        $school = School::factory()->create();
        $otherUserCareer = $this->user->careers->first();
        $otherUserCareer->startdate = Carbon::yesterday();
        $otherUserCareer->enddate = Carbon::yesterday();
        $otherUserCareer->save();

        DB::table('plannercollection_access')->insert([
            'entity_type' => 'user',
            'entity_id' => $this->user->id,
            'plannercollection_id' => $this->collection->id,
        ]);

        $this->assertEquals(1, DB::table('plannercollection_access')->count());

        $this->postJsonWithRequiredHeaders($this->getParameters([], [$school->uid]))
            ->assertNotAnErrorResponse();

        $this->assertEquals(0, DB::table('plannercollection_access')->count());
    }

    /**
     * The shared collection was previously accessible by all the users of the school.
     * The call changes the accessibility to 1 user instead of all the users of the school.
     * This tests if the child collection of users who don't have access anymore, is blocked.
     */
    #[Test]
    public function it_sets_collections_to_blocked_going_from_shared_school_to_users(): void
    {
        $this->collection->schoolsWithAccess()->sync($this->school->id);

        $userCollection = $this->setUpChildCollection($this->user);
        $ownerCollection = $this->setUpChildCollection($this->owner);

        $this->postJsonWithRequiredHeaders($this->getParameters([$this->owner->uid], []))
            ->assertJsonNoContentResponse();

        $this->assertEquals(CollectionUpdatable::Blocked, $userCollection->fresh()->updatable);
        $this->assertEquals(CollectionUpdatable::Updatable, $ownerCollection->fresh()->updatable);
    }

    /**
     * The shared collection was previously accessible by 2 users.
     * The call changes the accessibility to only 1 of those 2 users.
     * This tests if the child collection of the removed user is blocked.
     */
    #[Test]
    public function it_sets_collections_to_blocked_when_selecting_less_shared_users(): void
    {
        $this->collection->usersWithAccess()->sync([$this->user->id, $this->owner->id]);

        $userCollection = $this->setUpChildCollection($this->user);
        $ownerCollection = $this->setUpChildCollection($this->owner);

        $this->postJsonWithRequiredHeaders($this->getParameters([$this->owner->uid], []))
            ->assertJsonNoContentResponse();

        $this->assertEquals(CollectionUpdatable::Blocked, $userCollection->fresh()->updatable);
        $this->assertEquals(CollectionUpdatable::Updatable, $ownerCollection->fresh()->updatable);
    }

    /**
     * The shared collection was previously accessible by 2 users.
     * The call changes the accessibility to no-one (no school and no users are selected).
     * This tests if all the child collections are blocked.
     */
    #[Test]
    public function it_sets_all_collections_to_blocked_when_no_schools_or_users_are_given(): void
    {
        $this->collection->usersWithAccess()->sync([$this->user->id, $this->owner->id]);

        $userCollection = $this->setUpChildCollection($this->user);
        $ownerCollection = $this->setUpChildCollection($this->owner, CollectionUpdatable::NotUpdatable);

        $this->postJsonWithRequiredHeaders($this->getParameters([], []))
            ->assertJsonNoContentResponse();

        $this->assertEquals(CollectionUpdatable::Blocked, $userCollection->fresh()->updatable);
        $this->assertEquals(CollectionUpdatable::Blocked, $ownerCollection->fresh()->updatable);
    }

    /**
     * The shared collection was previously accessible by 2 users.
     * The call changes the accessibility to all the users of the school (this contains the previously selected users).
     * This tests if the child collection of the users are not blocked.
     */
    #[Test]
    public function it_does_not_block_collection_going_from_shared_users_to_school(): void
    {
        $this->collection->usersWithAccess()->sync([$this->user->id, $this->owner->id]);

        $userCollection = $this->setUpChildCollection($this->user);
        $ownerCollection = $this->setUpChildCollection($this->owner);

        $this->postJsonWithRequiredHeaders($this->getParameters([], [$this->school->uid]))
            ->assertJsonNoContentResponse();

        $this->assertEquals(CollectionUpdatable::Updatable, $userCollection->fresh()->updatable);
        $this->assertEquals(CollectionUpdatable::Updatable, $ownerCollection->fresh()->updatable);
    }

    /**
     * When the collection was unshared to a certain user, but now it is shared again.
     * The child collection is set to NOT_UPDATABLE when the parent and child collection are equal.
     */
    #[Test]
    public function it_unblocks_a_user_that_got_access_to_the_shared_collection_again(): void
    {
        $this->setUpCollectionDetails();

        $userCollection = $this->setUpChildCollection($this->user, CollectionUpdatable::Blocked);

        $this->postJsonWithRequiredHeaders($this->getParameters([$this->user->uid], []))
            ->assertJsonNoContentResponse();

        $this->assertEquals(CollectionUpdatable::NotUpdatable, $userCollection->fresh()->updatable);
    }

    /**
     * When the collection was unshared to a certain user, but now it is shared again.
     * The child collection is set to UPDATABLE when a parent chapter was changed.
     */
    #[Test]
    public function it_unblocks_and_sets_the_child_collection_to_updatable_when_chapters_not_equal(): void
    {
        $this->setUpCollectionDetails();

        $userCollection = $this->setUpChildCollection($this->user, CollectionUpdatable::Blocked);

        $this->chapter->name = $this->faker->name();
        $this->chapter->save();

        $this->postJsonWithRequiredHeaders($this->getParameters([$this->user->uid], []))
            ->assertJsonNoContentResponse();

        $this->assertEquals(CollectionUpdatable::Updatable, $userCollection->fresh()->updatable);
    }

    /**
     * When the collection was unshared to a certain user, but now it is shared again.
     * The child collection is set to UPDATABLE when a parent record was changed.
     */
    #[Test]
    public function it_unblocks_and_sets_the_child_collection_to_updatable_when_records_not_equal(): void
    {
        $this->setUpCollectionDetails();

        $userCollection = $this->setUpChildCollection($this->user, CollectionUpdatable::Blocked);

        $this->record->name = $this->faker->name();
        $this->record->save();

        $this->postJsonWithRequiredHeaders($this->getParameters([$this->user->uid], []))
            ->assertJsonNoContentResponse();

        $this->assertEquals(CollectionUpdatable::Updatable, $userCollection->fresh()->updatable);
    }

    /**
     * When the collection was unshared to a certain user, but now it is shared again.
     * The child collection is set to UPDATABLE when the curriculumnodes of a certain record were changed.
     */
    #[Test]
    public function it_unblocks_and_sets_the_child_collection_to_updatable_when_curriculumnodes_not_equal(): void
    {
        $this->setUpCollectionDetails();

        $userCollection = $this->setUpChildCollection($this->user, CollectionUpdatable::Blocked);

        $curriculumnode = CurriculumNode::factory()
            ->forCurriculumType(CurriculumType::Default)
            ->forType(CurriculumNodeType::Goal)
            ->create();
        $this->record->curriculumnodes()->save($curriculumnode);

        $this->postJsonWithRequiredHeaders($this->getParameters([$this->user->uid], []))
            ->assertJsonNoContentResponse();

        $this->assertEquals(CollectionUpdatable::Updatable, $userCollection->fresh()->updatable);
    }

    /**
     * When the collection was unshared to a certain user, but now it is shared again.
     * The child collection is set to UPDATABLE when a material of a certain record was changed.
     */
    #[Test]
    public function it_unblocks_and_sets_the_child_collection_to_updatable_when_materials_not_equal(): void
    {
        $this->setUpCollectionDetails();

        $userCollection = $this->setUpChildCollection($this->user, CollectionUpdatable::Blocked);

        $this->material->description = $this->faker->name();
        $this->material->save();

        $this->postJsonWithRequiredHeaders($this->getParameters([$this->user->uid], []))
            ->assertJsonNoContentResponse();

        $this->assertEquals(CollectionUpdatable::Updatable, $userCollection->fresh()->updatable);
    }

    /**
     * Set the users and schools to pass to the call.
     *
     * @param array $users User uids to pass to the call.
     * @param array $schools School uids to pass to the call.
     */
    protected function getParameters(array $users = [], array $schools = []): array
    {
        return [
            'users' => $users,
            'schools' => $schools,
        ];
    }

    /**
     * Assert the access table contains the given entity id.
     *
     * @param int $entityId Id of the entity that is expected to be in the access table.
     * @param string $entityType Type of the entity.
     */
    private function assertAccessTableContainsEntry(int $entityId, string $entityType): void
    {
        $this->assertDatabaseHas(
            'plannercollection_access',
            ['plannercollection_id' => $this->collection->id, 'entity_type' => $entityType, 'entity_id' => $entityId],
        );
    }
}
