<?php

namespace Tests\Unit\Cfa\Planner\Controllers\Api\UserCollection;

use App\Models\Feature\Feature;
use App\Models\Feature\FeatureToggle;
use Carbon\Carbon;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\CalendarItem\CalendarItem;
use Cfa\Planner\Domain\CalendarItem\Row\CalendarItemRow;
use Cfa\Planner\Domain\Collection\Chapter\Chapter;
use Cfa\Planner\Domain\Collection\UserCollection\UserCollection;
use Cfa\Planner\Domain\Collection\UserCollection\UserCollectionRepositoryInterface;
use Cfa\Planner\Domain\Record\Record;
use Illuminate\Support\Facades\Log;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\ControllerTestCase;

use function array_keys;
use function factory;

class UserCollectionControllerDeleteTest extends ControllerTestCase
{
    protected static ?string $routeName = 'api.collections.delete';

    /**
     * The user collection.
     *
     * @var UserCollection
     */
    private $collection;

    /**
     * {@inheritdoc}
     */
    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->collection = factory(UserCollection::class)->create([
            'owner_id' => $this->owner->id,
            'archived_at' => Carbon::now(),
        ]);

        $this->setUrlParameters([
            'collection' => $this->collection->uid,
        ]);
    }

    /**
     * Tests if the usercollection is deleted correctly
     */
    #[Test]
    public function it_deletes_the_collection_correctly(): void
    {
        $this->deleteJsonWithRequiredHeaders()
            ->assertJsonNoContentResponse();

        $this->assertEmpty(UserCollection::whereOwnerId($this->owner->id)->get());
    }

    /**
     * Tests if the usercollection is deleted correctly
     */
    #[Test]
    public function it_give_a_validation_error_when_collection_is_not_archived(): void
    {
        $this->collection->dearchive();
        $this->deleteJsonWithRequiredHeaders()
            ->assertJsonValidationErrors(['archived_at' => 'Gearchiveerd op is verplicht']);

        $this->assertNotEmpty(UserCollection::whereOwnerId($this->owner->id)->get());
    }

    #[Test]
    public function it_gives_no_validation_error_when_collection_is_not_archived_in_v2(): void
    {
        factory(FeatureToggle::class)->state('active')->create(['feature' => Feature::CollectionsV2]);
        $this->collection->dearchive();
        $this->deleteJsonWithRequiredHeaders()->assertNoContent();
    }

    #[Test]
    public function it_gives_validation_error_when_collection_has_unmigrated_children_in_v2(): void
    {
        factory(FeatureToggle::class)->state('active')->create(['feature' => Feature::CollectionsV2]);
        $this->collection->dearchive();
        $this->partialMock(UserCollectionRepositoryInterface::class)
            ->shouldReceive('hasUnmigratedChildren')
            ->withArgs(fn(UserCollection $collection): bool => $collection->uid === $this->collection->uid)
            ->once()
            ->andReturnTrue();
        Log::shouldReceive('warning')
            ->once()
            ->withArgs(function (string $message, array $context): bool {
                $this->assertSame('Delete Collection with unmigrated parents', $message);
                $this->assertSame(['collection', 'dd.trace_id', 'dd.span_id'], array_keys($context));
                $this->assertSame($this->collection->uid, $context['collection']);

                return true;
            })
            // Validation exception get ignored normally, but because we Mock log, we have to expect them.
            ->shouldReceive('error')->once();

        $this->deleteJsonWithRequiredHeaders()->assertJsonValidationErrors(
            [
                'collection' => 'Nog niet alle collega\'s hebben deze collectie geheractiveerd, waardoor u deze niet' .
                    ' kunt verwijderen.',
            ],
        );
    }

    /**
     * Test if its records get unlinked when the collection is deleted.
     * Created for SOL20-436
     */
    #[Test]
    public function it_makes_its_records_unlinked_when_a_collection_is_deleted(): void
    {
        $chapter = Chapter::factory()
            ->forPlannerCollection($this->collection)
            ->create();
        $record = factory(Record::class)->create([
            'chapter_id' => $chapter->id,
        ]);
        CalendarItem::factory()->create();
        CalendarItemRow::factory()->forLegacyRecord($record)->create();

        $this->deleteJsonWithRequiredHeaders()
            ->assertJsonNoContentResponse();

        $record->refresh();
        $this->assertEmpty($record->chapter_id);
        $this->assertNull($record->deleted_at);
    }

    /**
     * Tests if a not found exception is thrown if the usercollection doesnt exists.
     */
    #[Test]
    public function it_throws_a_not_found_exception_if_the_collection_doesnt_exists(): void
    {
        $this->setUrlParameters([
            'collection' => 'invalidCollection',
        ]);

        $this->deleteJsonWithRequiredHeaders()
            ->assertNotFound();
    }

    /**
     * Tests if an unauthorized exception is thrown if the user is not the owner of the usercollection.
     */
    #[Test]
    public function it_throws_an_unauthorized_exception_if_the_user_isnt_the_owner_of_the_collection(): void
    {
        $this->setAuthenticatedUser(User::factory()->create());

        $this->deleteJsonWithRequiredHeaders()
            ->assertJsonForbiddenResponse();
    }
}
