<?php

namespace Tests\Unit\Cfa\Planner\Controllers\Planner;

use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\Collection\MigrationStatus;
use Cfa\Planner\Domain\Collection\PlannerCollectionRepositoryInterface as LegacyPlannerCollectionInterfacy;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionBaseResource;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\ShareStatus;
use PHPUnit\Framework\Attributes\Test;
use Tests\Factories\Planner\PlannerCollectionFactory;
use Tests\Unit\ControllerTestCase;

use function app;
use function is_callable;

class CollectionsIndexControllerTest extends ControllerTestCase
{
    protected static ?string $routeName = 'web.planner.collections.index';

    private string $componentName = 'Planner/Collections/CollectionsOverview';

    #[Test]
    public function it_sends_the_correct_data_to_the_view(): void
    {
        $this->actingAs($this->owner);

        $collections = app(PlannerCollectionFactory::class)->forUser($this->owner)->create();

        PlannerCollection::setQueryingEnabled();
        ActivatedPlannerCollection::setQueryingEnabled();
        $builder = PlannerCollection::query();
        $collections = PlannerCollection::withCount(
            [
                'usersWithWriteAccess',
                'schoolsWithAccess',
                'usersWithAccess',
                'writeActivatedPlannerCollections',
            ],
        )
            ->whereIn('id', $collections->pluck('id'))
            ->get();
        PlannerCollection::setQueryingDisabled();
        ActivatedPlannerCollection::setQueryingDisabled();
        $this->mock(PlannerCollectionRepositoryInterface::class)
            ->shouldReceive('getAllByUser')
            ->withArgs(fn(
                User $user,
                array $additionalWiths = [],
                array $additionalWithCounts = [],
                ?array $columns = null,
                ?callable $additionalConditions = null,
            ): bool => $user->uid === $this->owner->uid
                && $additionalWithCounts === [
                    'schoolsWithAccess',
                    'usersWithAccess',
                    'writeActivatedPlannerCollections',
                ]
                && $columns === null
                && is_callable($additionalConditions)
                && $additionalConditions($builder))
            ->once()
            ->andReturn($collections);
        $expectedCollections = PlannerCollectionBaseResource::collection($collections)->resolve();
        $expectedCollections[0]['owner'] = [
            'uid' => $this->owner->uid,
            'fullname' => $this->owner->firstname . ' ' . $this->owner->lastname,
            'firstname' => $this->owner->firstname,
            'initials' => $this->owner->initials,
            'isAuthenticatedUser' => true,
            'profile_picture_parsed' => null,
            'profile_picture_thumb_parsed' => null,
        ];
        $expectedCollections[0]['targetAudiences'] = [];
        $expectedCollections[0]['isCoAuthor'] = false;
        $expectedCollections[0]['canUpdateOwnerProperties'] = true;
        $expectedCollections[0]['shareStatus'] = ShareStatus::OWNER->value;

        $this->getJsonWithRequiredHeaders()
            ->assertResponseOk()
            ->assertComponent($this->componentName)
            ->assertPropValue('school', $this->school)
            ->assertPropValue('collections', $expectedCollections)
            ->assertPropValue('collectionType', 'MY_COLLECTIONS');
        $this->assertEquals([
            'type' => 'Null',
            'column' => 'archived_at',
            'boolean' => 'and',
        ], $builder->getQuery()->wheres[0]);
    }

    #[Test]
    public function it_sends_unmigrated_collections(): void
    {
        $this->actingAs($this->owner);

        $processingCollection = new PlannerCollection();
        $processingCollection->name = 'Test';
        $processingCollection->setAttribute('isLegacyCollection', true);
        $processingCollection->setAttribute('migrationStatus', MigrationStatus::PROCESSING);

        $queuedCollection = new PlannerCollection();
        $queuedCollection->setAttribute('isLegacyCollection', true);
        $queuedCollection->setAttribute('migrationStatus', MigrationStatus::QUEUED);

        $unmigratedCollection = new PlannerCollection();
        $unmigratedCollection->setAttribute('isLegacyCollection', true);
        $unmigratedCollection->setAttribute('migrationStatus', null);

        $this->mock(PlannerCollectionRepositoryInterface::class)
            ->shouldReceive('getAllByUser')
            ->once()
            ->andReturn(collect());
        $this->mock(LegacyPlannerCollectionInterfacy::class)
            ->shouldReceive('getUnmigratedCollectionsForUser')
            ->once()
            ->withArgs(fn(User $user): bool => $this->owner->id === $user->id)
            ->andReturn([$processingCollection, $queuedCollection, $unmigratedCollection]);

        $expectedCollections = PlannerCollectionBaseResource::collection(
            collect([$processingCollection, $queuedCollection, $unmigratedCollection]),
        )->resolve();

        $expectedCollections[0]['name'] = 'Test';
        $expectedCollections[0]['shareStatus'] = 'reader';
        $expectedCollections[0]['targetAudiences'] = [];
        $expectedCollections[0]['isLegacyCollection'] = true;
        $expectedCollections[0]['isMigrationPending'] = true;

        $expectedCollections[1]['shareStatus'] = 'reader';
        $expectedCollections[1]['targetAudiences'] = [];
        $expectedCollections[1]['isLegacyCollection'] = true;
        $expectedCollections[1]['isMigrationPending'] = true;

        $expectedCollections[2]['shareStatus'] = 'reader';
        $expectedCollections[2]['targetAudiences'] = [];
        $expectedCollections[2]['isLegacyCollection'] = true;
        $expectedCollections[2]['isMigrationPending'] = false;

        $actualCollections = $this->getJsonWithRequiredHeaders()
            ->assertResponseOk()
            ->props('collections');

        $expectedCollections[0]['owner'] = ['fullname' => 'Geen beheerder', 'isPublisher' => false];
        $expectedCollections[1]['owner'] = ['fullname' => 'Geen beheerder', 'isPublisher' => false];
        $expectedCollections[2]['owner'] = ['fullname' => 'Geen beheerder', 'isPublisher' => false];

        $this->assertEquals($actualCollections, $expectedCollections);
    }

    #[Test]
    public function it_sends_unmigrated_collections_first_and_sorted(): void
    {
        $this->actingAs($this->owner);

        $plannerCollection = app(PlannerCollectionFactory::class)->setName('FirstCreated')->create();

        $processingCollection = new PlannerCollection();
        $processingCollection->name = 'LastCreated';
        $processingCollection->setAttribute('isLegacyCollection', true);
        $processingCollection->setAttribute('migrationStatus', MigrationStatus::PROCESSING);

        $secondProcessingCollection = new PlannerCollection();
        $secondProcessingCollection->name = 'SecondToLastCreated';
        $secondProcessingCollection->setAttribute('isLegacyCollection', true);
        $secondProcessingCollection->setAttribute('migrationStatus', MigrationStatus::PROCESSING);

        $processingCollectionAccented = new PlannerCollection();
        $processingCollectionAccented->name = 'ŁàstCreated';
        $processingCollectionAccented->setAttribute('isLegacyCollection', true);
        $processingCollectionAccented->setAttribute('migrationStatus', MigrationStatus::PROCESSING);

        $this->mock(PlannerCollectionRepositoryInterface::class)
            ->shouldReceive('getAllByUser')
            ->once()
            ->andReturn(collect([$plannerCollection]));
        $this->mock(LegacyPlannerCollectionInterfacy::class)
            ->shouldReceive('getUnmigratedCollectionsForUser')
            ->once()
            ->withArgs(fn(User $user): bool => $this->owner->id === $user->id)
            ->andReturn([$processingCollection, $processingCollectionAccented, $secondProcessingCollection]);

        $expectedCollections = PlannerCollectionBaseResource::collection(
            collect(
                [
                    $processingCollection,
                    $processingCollectionAccented,
                    $secondProcessingCollection,
                    $plannerCollection,
                ],
            ),
        )->resolve();

        $expectedCollections[0]['name'] = 'LastCreated';
        $expectedCollections[0]['shareStatus'] = 'reader';
        $expectedCollections[0]['targetAudiences'] = [];
        $expectedCollections[0]['isLegacyCollection'] = true;
        $expectedCollections[0]['isMigrationPending'] = true;

        $expectedCollections[1]['name'] = 'ŁàstCreated';
        $expectedCollections[1]['shareStatus'] = 'reader';
        $expectedCollections[1]['targetAudiences'] = [];
        $expectedCollections[1]['isLegacyCollection'] = true;
        $expectedCollections[1]['isMigrationPending'] = true;

        $expectedCollections[2]['name'] = 'SecondToLastCreated';
        $expectedCollections[2]['shareStatus'] = 'reader';
        $expectedCollections[2]['targetAudiences'] = [];
        $expectedCollections[2]['isLegacyCollection'] = true;
        $expectedCollections[2]['isMigrationPending'] = true;

        $expectedCollections[3]['name'] = 'FirstCreated';
        $expectedCollections[3]['shareStatus'] = 'reader';
        $expectedCollections[3]['targetAudiences'] = [];
        $expectedCollections[3]['isLegacyCollection'] = false;
        $expectedCollections[3]['isMigrationPending'] = false;

        $actualCollections = $this->getJsonWithRequiredHeaders()
            ->assertResponseOk()
            ->props('collections');

        $expectedCollections[0]['owner'] = ['fullname' => 'Geen beheerder', 'isPublisher' => false];
        $expectedCollections[1]['owner'] = ['fullname' => 'Geen beheerder', 'isPublisher' => false];
        $expectedCollections[2]['owner'] = ['fullname' => 'Geen beheerder', 'isPublisher' => false];
        $expectedCollections[3]['owner'] = ['fullname' => 'Geen beheerder', 'isPublisher' => false];

        $this->assertEquals($actualCollections, $expectedCollections);
    }

    #[Test]
    public function it_flags_co_author_when_collection_is_activated_as_writable(): void
    {
        $colleague = User::factory()->create();
        $plannerCollection = app(PlannerCollectionFactory::class)->forUser($this->owner)->create();
        ActivatedPlannerCollection::factory()
            ->forPlannerCollection($plannerCollection)
            ->forUser($colleague)
            ->writeAccess()
            ->create();

        $this->getJsonWithRequiredHeaders()
            ->assertResponseOk()
            ->assertPropValue('collections.0.isCoAuthor', true)
            ->assertPropValue('collections.0.coAuthorCount', 1)
            ->assertPropValue('collections.0.owner.fullname', $this->owner->fullname);
    }

    #[Test]
    public function it_doesnt_flag_co_author_when_collection_is_activated_as_readable(): void
    {
        $colleague = User::factory()->create();
        $plannerCollection = app(PlannerCollectionFactory::class)->forUser($this->owner)->create();
        ActivatedPlannerCollection::factory()
            ->forPlannerCollection($plannerCollection)
            ->forUser($colleague)
            ->create();

        $this->getJsonWithRequiredHeaders()
            ->assertResponseOk()
            ->assertPropValue('collections.0.isCoAuthor', false);
    }

    #[Test]
    public function it_flags_co_author_when_write_access_is_given_for_the_collection(): void
    {
        $colleague = User::factory()->create();
        $plannerCollection = app(PlannerCollectionFactory::class)->forUser($colleague)->create();
        ActivatedPlannerCollection::factory()
            ->forPlannerCollection($plannerCollection)
            ->forUser($this->owner)
            ->writeAccess()
            ->create();

        $this->getJsonWithRequiredHeaders()
            ->assertResponseOk()
            ->assertPropValue('collections.0.isCoAuthor', true)
            ->assertPropValue('collections.0.coAuthorCount', 1)
            ->assertPropValue('collections.0.owner.fullname', $colleague->fullname);
    }

    #[Test]
    public function it_correctly_counts_co_authors_for_the_collection(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->forUser($this->owner)->create();

        $writeColleagues = User::factory()->times(3)->create();
        $writeColleagues->each(fn(User $colleague) => ActivatedPlannerCollection::factory()
            ->forPlannerCollection($plannerCollection)
            ->forUser($colleague)
            ->writeAccess()
            ->create());

        $readColleagues = User::factory()->times(3)->create();
        $readColleagues->each(fn(User $colleague) => ActivatedPlannerCollection::factory()
            ->forPlannerCollection($plannerCollection)
            ->forUser($colleague)
            ->create());

        $plannerCollection
            ->usersWithAccess()
            ->syncWithPivotValues(
                [User::factory()->create()->id],
                ['is_writable' => 1],
            );

        $this->getJsonWithRequiredHeaders()
            ->assertResponseOk()
            ->assertPropValue('collections.0.isCoAuthor', true)
            ->assertPropValue('collections.0.coAuthorCount', 3);
    }

    #[Test]
    public function it_doesnt_flag_co_author_when_read_access_is_given_for_the_collection(): void
    {
        $colleague = User::factory()->create();
        $plannerCollection = app(PlannerCollectionFactory::class)->forUser($colleague)->create();
        ActivatedPlannerCollection::factory()
            ->forPlannerCollection($plannerCollection)
            ->forUser($this->owner)
            ->create();

        $this->getJsonWithRequiredHeaders()
            ->assertResponseOk()
            ->assertPropValue('collections.0.isCoAuthor', false)
            ->assertPropValue('collections.0.coAuthorCount', 0);
    }

    #[Test]
    public function it_returns_deactivated_collections(): void
    {
        $colleague = User::factory()->create();
        $plannerCollection = app(PlannerCollectionFactory::class)->forUser($colleague)->create();
        $deactivatedCollection = ActivatedPlannerCollection::factory()
            ->forUser($this->owner)
            ->forPlannerCollection($plannerCollection)
            ->deactivate()
            ->create();

        $this->getJsonWithRequiredHeaders()
            ->assertResponseOk()
            ->assertPropValue('collections.0.id', $deactivatedCollection->uid)
            ->assertPropValue('collections.0.isDeactivated', true);
    }
}
