<?php

namespace Tests\Unit\Cfa\Planner\Controllers\Planner;

use Cfa\Common\Application\Services\Helpers\SchoolHelperService;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudience;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Application\Repositories\CollectionV2\TargetAudiencesRepository;
use Cfa\Planner\Domain\Collection\PublisherCollection\Publisher\Publisher;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollectionRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordOverwriteColumn;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionResource;
use PHPUnit\Framework\Attributes\Test;
use Tests\Factories\Planner\ChapterFactory;
use Tests\Factories\Planner\PlannerCollectionFactory;
use Tests\Factories\Planner\RecordFactory;
use Tests\Factories\Planner\RecordOverwriteFactory;
use Tests\Unit\ControllerTestCase;

use function app;
use function collect;
use function response;
use function uuid;

class CollectionsDetailControllerTest extends ControllerTestCase
{
    protected static ?string $routeName = 'web.planner.collections.collection.detail';

    private string $componentName = 'Planner/Collections/CollectionDetail/CollectionDetail';

    #[Test]
    public function it_sends_the_correct_data_to_the_view(): void
    {
        $collectionUid = uuid();
        $this->setUrlParameter('collectionv2', $collectionUid);

        $plannerCollection = new PlannerCollection();
        $plannerCollection->user_id = $this->owner->id;
        $plannerCollection->uid = $collectionUid;

        $targetAudience = new TargetAudience();
        $targetAudience->uid = 'target-audience';
        $targetAudience->type = TargetAudienceType::Ko;
        $targetAudience->natural_study_year = 3;

        $this->mock(TargetAudiencesRepository::class)
            ->shouldReceive('getAll')
            ->andReturn(collect([$targetAudience]));

        $this->mock(PlannerCollectionRepositoryInterface::class)
            ->shouldReceive('getByUid')
            ->with($collectionUid)
            ->once()
            ->andReturn($plannerCollection)
            ->shouldReceive('loadAllDataAndRelations')
            ->withArgs(
                fn(PlannerCollection $actualPlannerCollection): bool => $actualPlannerCollection ===
                    $plannerCollection,
            )
            ->once()
            ->andReturn($plannerCollection);

        $readerA = $this->createUser('A', 'Reader', $this->school);
        $readerB = $this->createUser('B', 'Reader', $this->school);
        $coAuthorA = $this->createUser('A', 'CoAuthor', $this->school);
        $coAuthorB = $this->createUser('B', 'CoAuthor', $this->school);
        $this->mock(ActivatedPlannerCollectionRepositoryInterface::class)
            ->shouldReceive('getReaders')
            ->withArgs(
                fn(PlannerCollection $actualPlannerCollection): bool => $actualPlannerCollection === $plannerCollection,
            )
            ->once()
            ->andReturn(
                collect([
                    $readerA,
                    $readerB,
                    $this->createUser('A', 'C', School::factory()->create()),
                ]),
            )
            ->shouldReceive('getCoAuthors')
            ->withArgs(
                fn(PlannerCollection $actualPlannerCollection): bool => $actualPlannerCollection === $plannerCollection,
            )
            ->once()
            ->andReturn(
                collect([
                    $coAuthorA,
                    $coAuthorB,
                    $this->createUser('A', 'C', School::factory()->create()),
                    $this->createUser('D', 'E', School::factory()->create()),
                ]),
            );

        $this->mockWithParameters(
            PlannerCollectionResource::class,
            function (array $parameters) use ($plannerCollection): bool {
                $this->assertCount(1, $parameters);
                $this->assertEquals($parameters['resource'], $plannerCollection);

                return true;
            },
        )
            ->shouldReceive('toResponse')
            ->andReturn(response()->json(['collection']));

        $this->getJsonWithRequiredHeaders()
            ->assertResponseOk()
            ->assertComponent($this->componentName)
            ->assertPropValue(
                'user',
                [
                    'uid' => $this->owner->uid,
                    'fullname' => $this->owner->firstname . ' ' . $this->owner->lastname,
                    'firstname' => $this->owner->firstname,
                    'initials' => $this->owner->initials,
                    'isAuthenticatedUser' => true,
                    'profile_picture_parsed' => null,
                    'profile_picture_thumb_parsed' => null,
                ],
            )
            ->assertPropValue('targetAudiences', [['id' => 'target-audience', 'name' => 'KO-J3']])
            ->assertPropValue('coAuthorsCurrentSchool', [
                [
                    'uid' => $coAuthorA->uid,
                    'initials' => 'AC',
                    'fullname' => 'A CoAuthor',
                    'firstname' => 'A',
                    'isAuthenticatedUser' => false,
                    'profile_picture_parsed' => null,
                    'profile_picture_thumb_parsed' => null,
                ],
                [
                    'uid' => $coAuthorB->uid,
                    'initials' => 'BC',
                    'fullname' => 'B CoAuthor',
                    'firstname' => 'B',
                    'isAuthenticatedUser' => false,
                    'profile_picture_parsed' => null,
                    'profile_picture_thumb_parsed' => null,
                ],
            ])
            ->assertPropValue('coAuthorCountInOtherSchools', 2)
            ->assertPropValue('readersCurrentSchool', [
                [
                    'uid' => $readerA->uid,
                    'initials' => 'AR',
                    'fullname' => 'A Reader',
                    'firstname' => 'A',
                    'isAuthenticatedUser' => false,
                    'profile_picture_parsed' => null,
                    'profile_picture_thumb_parsed' => null,
                ],
                [
                    'uid' => $readerB->uid,
                    'initials' => 'BR',
                    'fullname' => 'B Reader',
                    'firstname' => 'B',
                    'isAuthenticatedUser' => false,
                    'profile_picture_parsed' => null,
                    'profile_picture_thumb_parsed' => null,
                ],
            ])
            ->assertPropValue('readerCountInOtherSchools', 1)
            ->assertPropValue('totalColleagues', 7)
            ->assertPropValue('hasBeenActivated', true)
            ->assertPropValue('collection', ['collection'])
            ->assertPropValue('schoolIsKathOndVla', true);
    }

    #[Test]
    public function it_returns_false_if_school_is_not_kathOndVla(): void
    {
        $school = School::factory()->inOvsg()->create();
        $this->mock(SchoolHelperService::class)->shouldReceive('school')->andReturn($school);
        Career::factory()->inSchool($school)->forUser($this->owner)->withRole(RoleName::Teacher)->create();

        $plannerCollection = app(PlannerCollectionFactory::class)->create();

        $activatedCollection = ActivatedPlannerCollection::factory()
            ->forUser($this->owner)
            ->forPlannerCollection($plannerCollection)
            ->create();

        $this->setUrlParameter('collectionv2', $activatedCollection->uid);

        $this->getJsonWithRequiredHeaders()
            ->assertResponseOk()
            ->assertPropValue('schoolIsKathOndVla', false);
    }

    #[Test]
    public function it_gives_co_author_or_reader_data_for_non_publisher_collections(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->create();

        $activatedCollection = ActivatedPlannerCollection::factory()
            ->forUser($this->owner)
            ->forPlannerCollection($plannerCollection)
            ->create();

        $this->setUrlParameter('collectionv2', $activatedCollection->uid);

        $response = $this->getJsonWithRequiredHeaders()
            ->assertResponseOk()
            ->props();

        $this->assertArrayHasKey('totalColleagues', $response);
        $this->assertArrayHasKey('coAuthorsCurrentSchool', $response);
        $this->assertArrayHasKey('coAuthorCountInOtherSchools', $response);
        $this->assertArrayHasKey('readersCurrentSchool', $response);
        $this->assertArrayHasKey('readerCountInOtherSchools', $response);
    }

    #[Test]
    public function it_does_not_give_co_author_or_reader_data_for_publisher_collections(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->forPublisher(Publisher::VAN_IN)->create();

        $activatedCollection = ActivatedPlannerCollection::factory()
            ->forUser($this->owner)
            ->forPlannerCollection($plannerCollection)
            ->create();

        $this->setUrlParameter('collectionv2', $activatedCollection->uid);

        $response = $this->getJsonWithRequiredHeaders()
            ->assertResponseOk()
            ->props();

        $this->assertArrayNotHasKey('totalColleagues', $response);
        $this->assertArrayNotHasKey('hasBeenActivated', $response);
        $this->assertArrayNotHasKey('coAuthorsCurrentSchool', $response);
        $this->assertArrayNotHasKey('coAuthorCountInOtherSchools', $response);
        $this->assertArrayNotHasKey('readersCurrentSchool', $response);
        $this->assertArrayNotHasKey('readerCountInOtherSchools', $response);
    }

    #[Test]
    public function it_sets_has_been_activated_correctly_when_the_user_is_the_only_co_authors(): void
    {
        $collectionUid = uuid();
        $this->setUrlParameter('collectionv2', $collectionUid);

        $plannerCollection = new PlannerCollection();
        $plannerCollection->uid = uuid();
        $plannerCollection->user_id = $this->createUser('A', 'B', $this->school)->id;
        $plannerCollection->is_writable = true;

        $activatedPlannerCollection = new ActivatedPlannerCollection();
        $activatedPlannerCollection->uid = $collectionUid;
        $activatedPlannerCollection->user_id = $this->owner->id;

        $this->mock(PlannerCollectionRepositoryInterface::class)
            ->shouldReceive('getByUid')
            ->andReturn($activatedPlannerCollection)
            ->shouldReceive('loadAllDataAndRelations')
            ->andReturn($plannerCollection);

        $this->getJsonWithRequiredHeaders()
            ->assertResponseOk()
            ->assertPropValue('hasBeenActivated', true);
    }

    #[Test]
    public function it_counts_colleagues_correctly_when_the_user_is_one_of_the_co_authors(): void
    {
        $collectionUid = uuid();
        $this->setUrlParameter('collectionv2', $collectionUid);

        $plannerCollection = new PlannerCollection();
        $plannerCollection->uid = uuid();
        $plannerCollection->user_id = $this->createUser('A', 'B', $this->school)->id;
        $plannerCollection->is_writable = true;

        $activatedPlannerCollection = new ActivatedPlannerCollection();
        $activatedPlannerCollection->uid = $collectionUid;
        $activatedPlannerCollection->user_id = $this->owner->id;
        $activatedPlannerCollection->is_writable = true;

        $this->mock(PlannerCollectionRepositoryInterface::class)
            ->shouldReceive('getByUid')
            ->andReturn($activatedPlannerCollection)
            ->shouldReceive('loadAllDataAndRelations')
            ->andReturn($plannerCollection);

        $this->mock(ActivatedPlannerCollectionRepositoryInterface::class)
            ->shouldReceive('getReaders')
            ->once()
            ->andReturn(collect([$this->owner]))
            ->shouldReceive('getCoAuthors')
            ->once()
            ->andReturn(collect([$this->owner]));
        $this->getJsonWithRequiredHeaders()
            ->assertResponseOk()
            ->assertPropValue('totalColleagues', 1);
    }

    #[Test]
    public function it_sends_the_correct_value_for_is_own_record_for_an_activated_read_collection(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->create();
        app(ChapterFactory::class)
            ->setOrder(2)
            ->forPlannerCollection($plannerCollection)
            ->create();

        $chapter2 = app(ChapterFactory::class)
            ->setOrder(1)
            ->forPlannerCollection($plannerCollection)
            ->create();
        $record1ForChapter2 = app(RecordFactory::class)
            ->forPlannerCollection($plannerCollection)
            ->setOrder(2)
            ->forChapter($chapter2)
            ->create();
        $record2ForChapter2 = app(RecordFactory::class)
            ->forPlannerCollection($plannerCollection)
            ->setOrder(1)
            ->forChapter($chapter2)
            ->create();

        $activatedCollection = ActivatedPlannerCollection::factory()
            ->forUser($this->owner)
            ->forPlannerCollection($plannerCollection)
            ->create();

        // Add one record for the user to the activatedPlannerCollection to chapter2.
        $this->ownerRecordInChapter2 = app(RecordFactory::class)
            ->forActivatedCollection($activatedCollection)
            ->setOrder(3)
            ->forChapter($chapter2)
            ->create();

        // Create an overwrite for name on Record 2 in Chapter 2.
        app(RecordOverwriteFactory::class)
            ->forUser($this->owner)
            ->forRecord($record2ForChapter2)
            ->forActivatedPlannerCollection($activatedCollection)
            ->forColumn(RecordOverwriteColumn::Name)
            ->withValue('Bram & Bart')
            ->create();

        $this->setUrlParameter('collectionv2', $activatedCollection->uid);

        $response = $this->getJsonWithRequiredHeaders();

        $responseCollection = $response->props('collection');
        $this->assertEquals(['fullname' => 'Geen beheerder', 'isPublisher' => false], $responseCollection['owner']);

        $this->assertCount(2, $responseCollection['chapters']);
        $records = $responseCollection['chapters'][0]['records'];
        $this->assertCount(3, $records);

        $this->assertEquals($record2ForChapter2->uid, $records[0]['id']);
        $this->assertEquals('Bram & Bart', $records[0]['name']);
        $this->assertTrue($records[0]['is_user_edit']);

        $this->assertEquals($record1ForChapter2->uid, $records[1]['id']);
        $this->assertFalse($records[1]['is_own_record']);

        $this->assertEquals($this->ownerRecordInChapter2->uid, $records[2]['id']);
        $this->assertTrue($records[2]['is_own_record']);

        $this->assertFalse($responseCollection['canBeDeleted']);
    }

    #[Test]
    public function it_ignores_order_as_record_overwrite(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->create();
        app(ChapterFactory::class)
            ->setOrder(2)
            ->forPlannerCollection($plannerCollection)
            ->create();

        $chapter2 = app(ChapterFactory::class)
            ->setOrder(1)
            ->forPlannerCollection($plannerCollection)
            ->create();
        $record1ForChapter2 = app(RecordFactory::class)
            ->forPlannerCollection($plannerCollection)
            ->setOrder(2)
            ->forChapter($chapter2)
            ->create();
        $record2ForChapter2 = app(RecordFactory::class)
            ->forPlannerCollection($plannerCollection)
            ->setOrder(1)
            ->forChapter($chapter2)
            ->create();

        $activatedCollection = ActivatedPlannerCollection::factory()
            ->forUser($this->owner)
            ->forPlannerCollection($plannerCollection)
            ->create();

        // Create an overwrite for name on Record 2 in Chapter 2.
        app(RecordOverwriteFactory::class)
            ->forUser($this->owner)
            ->forRecord($record2ForChapter2)
            ->forActivatedPlannerCollection($activatedCollection)
            ->forColumn(RecordOverwriteColumn::Name)
            ->withValue('Bram & Bart')
            ->create();

        // Create an overwrite for order on Record 1 in Chapter 2.
        app(RecordOverwriteFactory::class)
            ->forUser($this->owner)
            ->forRecord($record1ForChapter2)
            ->forActivatedPlannerCollection($activatedCollection)
            ->forColumn(RecordOverwriteColumn::Order)
            ->withValue(4)
            ->create();

        $this->setUrlParameter('collectionv2', $activatedCollection->uid);

        $response = $this->getJsonWithRequiredHeaders();

        $responseCollection = $response->props('collection');
        $records = $responseCollection['chapters'][0]['records'];

        $this->assertEquals($record2ForChapter2->uid, $records[0]['id']);
        $this->assertEquals('Bram & Bart', $records[0]['name']);
        $this->assertTrue($records[0]['is_user_edit']);

        $this->assertEquals($record1ForChapter2->uid, $records[1]['id']);
        $this->assertFalse($records[1]['is_user_edit']);
    }

    #[Test]
    public function it_ignores_chapterId_as_record_overwrite(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->create();
        $chapter1 = app(ChapterFactory::class)
            ->setOrder(2)
            ->forPlannerCollection($plannerCollection)
            ->create();

        $chapter2 = app(ChapterFactory::class)
            ->setOrder(1)
            ->forPlannerCollection($plannerCollection)
            ->create();
        $record1ForChapter2 = app(RecordFactory::class)
            ->forPlannerCollection($plannerCollection)
            ->setOrder(2)
            ->forChapter($chapter2)
            ->create();
        $record2ForChapter2 = app(RecordFactory::class)
            ->forPlannerCollection($plannerCollection)
            ->setOrder(1)
            ->forChapter($chapter2)
            ->create();

        $activatedCollection = ActivatedPlannerCollection::factory()
            ->forUser($this->owner)
            ->forPlannerCollection($plannerCollection)
            ->create();

        // Create an overwrite for name on Record 2 in Chapter 2.
        app(RecordOverwriteFactory::class)
            ->forUser($this->owner)
            ->forRecord($record2ForChapter2)
            ->forActivatedPlannerCollection($activatedCollection)
            ->forColumn(RecordOverwriteColumn::Name)
            ->withValue('Bram & Bart')
            ->create();

        // Create an overwrite for Chapter on Record 1 in Chapter 2.
        app(RecordOverwriteFactory::class)
            ->forUser($this->owner)
            ->forRecord($record1ForChapter2)
            ->forActivatedPlannerCollection($activatedCollection)
            ->forColumn(RecordOverwriteColumn::ChapterId)
            ->withValue($chapter1->id)
            ->create();

        $this->setUrlParameter('collectionv2', $activatedCollection->uid);

        $response = $this->getJsonWithRequiredHeaders();

        $responseCollection = $response->props('collection');
        $firstRecord = $responseCollection['chapters'][0]['records'][0];
        $secondRecord = $responseCollection['chapters'][1]['records'][0];

        $this->assertEquals($record2ForChapter2->uid, $firstRecord['id']);
        $this->assertEquals('Bram & Bart', $firstRecord['name']);
        $this->assertTrue($firstRecord['is_user_edit']);

        $this->assertEquals($record1ForChapter2->uid, $secondRecord['id']);
        $this->assertFalse($secondRecord['is_user_edit']);
    }

    #[Test]
    public function it_still_returns_the_activated_data_when_user_also_owns_the_parent(): void
    {
        $plannerCollection = app(PlannerCollectionFactory::class)->forUser($this->owner)->create();
        app(ChapterFactory::class)
            ->setOrder(2)
            ->forPlannerCollection($plannerCollection)
            ->create();

        $activatedCollection = ActivatedPlannerCollection::factory()
            ->forUser($this->owner)
            ->forPlannerCollection($plannerCollection)
            ->archived()
            ->create();

        $this->setUrlParameter('collectionv2', $activatedCollection->uid);

        $response = $this->getJsonWithRequiredHeaders();

        $responseCollection = $response->props('collection');

        $this->assertEquals('reader', $responseCollection['shareStatus']);
        $this->assertTrue($responseCollection['canBeDeleted']);
        $this->assertEquals($activatedCollection->uid, $responseCollection['id']);
    }

    #[Test]
    public function it_returns_false_for_when_the_collection_is_a_read_activated_collection(): void
    {
        $activatedCollectionUid = uuid();

        $this->setUrlParameter('collectionv2', $activatedCollectionUid);

        $plannerCollection = app(PlannerCollectionFactory::class)->forUser($this->owner)->create();
        $activatedPlannerCollection = new ActivatedPlannerCollection();
        $activatedPlannerCollection->user_id = $this->owner->id;
        $activatedPlannerCollection->uid = $activatedCollectionUid;

        $plannerCollection->setAttribute('collectionv2', $plannerCollection->uid);
        $plannerCollection->uid = $activatedPlannerCollection->uid;
        $plannerCollection->is_writable = false;
        $plannerCollection->user_id = User::factory()
            ->withActiveCareer($this->school, RoleName::Teacher)
            ->create()
            ->id;

        $this->mock(PlannerCollectionRepositoryInterface::class)
            ->shouldReceive('getByUid')
            ->once()
            ->with($activatedCollectionUid)
            ->andReturn($activatedPlannerCollection)
            ->shouldReceive('loadAllDataAndRelations')
            ->once()
            ->andReturn($plannerCollection);

        $this->mockWithParameters(
            PlannerCollectionResource::class,
            function (array $parameters) use ($plannerCollection): bool {
                $this->assertCount(1, $parameters);
                $this->assertEquals($parameters['resource'], $plannerCollection);

                return true;
            },
        )
            ->shouldReceive('toResponse')
            ->andReturn(response()->json(['collection']));

        $this->getJsonWithRequiredHeaders()
            ->assertResponseOk()
            ->assertPropValue('hasBeenActivated', false);
    }

    #[Test]
    public function it_returns_false_for_has_been_activated_when_a_collection_has_not_been_activated(): void
    {
        $collectionUid = uuid();
        $this->setUrlParameter('collectionv2', $collectionUid);

        $plannerCollection = new PlannerCollection();
        $plannerCollection->user_id = $this->owner->id;
        $plannerCollection->uid = $collectionUid;

        $this->mock(PlannerCollectionRepositoryInterface::class)
            ->shouldReceive('getByUid')
            ->with($collectionUid)
            ->once()
            ->andReturn($plannerCollection)
            ->shouldReceive('loadAllDataAndRelations')
            ->withArgs(
                fn(PlannerCollection $actualPlannerCollection): bool => $actualPlannerCollection ===
                    $plannerCollection,
            )
            ->once()
            ->andReturn($plannerCollection);

        $this->mockWithParameters(
            PlannerCollectionResource::class,
            function (array $parameters) use ($plannerCollection): bool {
                $this->assertCount(1, $parameters);
                $this->assertEquals($parameters['resource'], $plannerCollection);

                return true;
            },
        )
            ->shouldReceive('toResponse')
            ->andReturn(response()->json(['collection']));

        $this->getJsonWithRequiredHeaders()
            ->assertResponseOk()
            ->assertComponent($this->componentName)
            ->assertPropValue(
                'user',
                [
                    'uid' => $this->owner->uid,
                    'fullname' => $this->owner->firstname . ' ' . $this->owner->lastname,
                    'firstname' => $this->owner->firstname,
                    'initials' => $this->owner->initials,
                    'isAuthenticatedUser' => true,
                    'profile_picture_parsed' => null,
                    'profile_picture_thumb_parsed' => null,
                ],
            )
            ->assertPropValue('hasBeenActivated', false)
            ->assertPropValue('collection', ['collection']);
    }

    private function createUser(string $firstName, string $lastName, School $school): User
    {
        return User::factory()
            ->withActiveCareer($school, RoleName::Teacher)
            ->create(['firstname' => $firstName, 'lastname' => $lastName]);
    }
}
