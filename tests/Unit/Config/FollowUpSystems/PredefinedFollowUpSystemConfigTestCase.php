<?php

namespace Tests\Unit\Config\FollowUpSystems;

use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;
use Tests\Unit\UnitTestCase;

abstract class PredefinedFollowUpSystemConfigTestCase extends UnitTestCase
{
    protected function assertSubTypeConfig(array $subTypeConfig, string $type, string $subType): void
    {
        $this->assertIsArray($subTypeConfig);
        $this->assertNotEmpty($subTypeConfig);

        foreach ($subTypeConfig as $testAudience => $testAudienceConfig) {
            self::arrayHasKey($testAudience, TestAudience::cases());
            $this->assertTestAudienceConfig($testAudienceConfig, $type, $subType, $testAudience);
        }
    }

    protected function assertTestAudienceConfig(
        array $testAudienceConfig,
        string $type,
        string $subType,
        string $testAudience,
    ): void {
        $this->assertIsArray($testAudienceConfig);
        $this->assertNotEmpty($testAudienceConfig);

        foreach ($testAudienceConfig as $testMoment => $testMomentConfig) {
            self::arrayHasKey($testMoment, TestMoment::cases());
            $this->assertTestMomentConfig($testMomentConfig, $type, $subType, $testAudience, $testMoment);
        }
    }

    protected function assertTestMomentConfig(
        array $testMomentConfig,
        string $type,
        string $subType,
        string $testAudience,
        string $testMoment,
    ): void {
        $this->assertIsArray($testMomentConfig);
        $this->assertNotEmpty($testMomentConfig);
    }

    protected function getFailedMessage(
        string $type,
        string $subType,
        string $testAudience,
        string $testMoment,
        mixed $key,
    ): string {
        return $type . ' => ' .
            $subType . ' => ' .
            $testAudience . ' => ' .
            $testMoment . ' => ' .
            $key;
    }

    public static function getLvsTypes(): array
    {
        return [
            ...static::getLvsTypesWithNnConfig(),
            'tijd-voor-taal-accent-spelling' => [
                FollowUpSystemType::from(FollowUpSystemType::TijdVoorTaalAccentSpelling->value),
            ],
            'ik-lees-met-hup-en-aap' => [
                FollowUpSystemType::from(FollowUpSystemType::IkLeesMetHupEnAap->value),
            ],
            'talent' => [
                FollowUpSystemType::from(FollowUpSystemType::Talent->value),
            ],
        ];
    }

    public static function getLvsTypesWithNnConfig(): array
    {
        return [
            'maths' => [FollowUpSystemType::from(FollowUpSystemType::LvsVclbMaths->value)],
            'reading' => [FollowUpSystemType::from(FollowUpSystemType::LvsVclbReading->value)],
            'spelling' => [FollowUpSystemType::from(FollowUpSystemType::LvsVclbSpelling->value)],
        ];
    }
}
