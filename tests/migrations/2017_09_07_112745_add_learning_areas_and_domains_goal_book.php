<?php

use App\Services\Sequence\SequenceCalculator;
use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetwork;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNode;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNodeType;
use Cfa\Planner\Domain\CurriculumNode\CurriculumType;
use Cfa\Planner\Domain\CurriculumNode\GradeLevelConfiguration\GradeLevelConfiguration;
use Illuminate\Database\Migrations\Migration;

class AddLearningAreasAndDomainsGoalBook extends Migration
{
    /**
     * Id of the educational network.
     *
     * @var int
     */
    protected $educationalNetworkId;

    /**
     * Sequence calculator to generate sequences for the imported curriculum nodes.
     *
     * @var SequenceCalculator
     */
    protected $sequenceCalculator;

    /**
     * Learning areas to insert.
     *
     * @var array
     */
    protected $learningAreaMapping = [
        'WI' => 'Wiskunde',
        'NL' => 'Nederlandse taal',
        'WO' => 'Wereldoriëntatie',
        'MV' => 'Muzische vorming',
        'LO' => 'Lichamelijke opvoeding',
        'FR' => 'Frans',
    ];

    /**
     * Domains to insert.
     *
     * @var array
     */
    protected $domainMapping = [
        'WI' => [
            'DOD' => 'Domein-overschrijdend',
            'GET' => 'Getallen',
            'MET' => 'Meten',
            'MTK' => 'Meetkunde',
        ],
        'NL' => [
            'LUI' => 'Luisteren',
            'SPR' => 'Spreken',
            'LEZ' => 'Lezen',
            'SCH' => 'Schrijven',
            'TBS' => 'Taalbeschouwing',
        ],
        'WO' => [
            'NAT' => 'Natuur',
            'TEC' => 'Techniek',
            'MEN' => 'Mens',
            'MAA' => 'Maatschappij',
            'TIJD' => 'Tijd',
            'RUI' => 'Ruimte',
            'VKR' => 'Verkeers- en mobiliteitseducatie',
        ],
        'MV' => [
            'ATT' => 'Algemeen',
            'BLD' => 'Beeld',
            'MUZ' => 'Muziek',
            'BEW' => 'Beweging',
            'DRA' => 'Drama',
            'MED' => 'Media',
        ],
        'LO' => [
            'GYM' => 'Gymnastiek',
            'ATL' => 'Atletiek',
            'BAL' => 'Balvaardigheden: sportspelen',
            'ROL' => 'Voortbewegen op rollend/glijdend materiaal',
            'RIT' => 'Ritmisch en expressief bewegen',
            'ZWE' => 'Zwemmen',
            'FGZ' => 'Fitheid en gezondheid',
            'ZSF' => 'Zelfconcept en sociaal functioneren',
        ],
        'FR' => [
            'DOD' => 'Domeinoverstijgende doelen',
            'MOI' => 'Mondelinge interactie',
            'LUI' => 'Luisteren',
            'SPR' => 'Spreken',
            'LEZ' => 'Lezen',
            'SCH' => 'Schrijven',
        ],
    ];

    /**
     * Grade level configruations for OVSG
     *
     * @var array
     */
    private $gradelevelconfiguration = [
        [
            'target_audience_type' => TargetAudienceType::Ko,
            'natural_study_year' => 1,
        ],
        [
            'target_audience_type' => TargetAudienceType::Ko,
            'natural_study_year' => 2,
        ],
        [
            'target_audience_type' => TargetAudienceType::Ko,
            'natural_study_year' => 3,
        ],
        [
            'target_audience_type' => TargetAudienceType::Ko,
            'natural_study_year' => 4,
        ],
        [
            'target_audience_type' => TargetAudienceType::Lo,
            'natural_study_year' => 1,
        ],
        [
            'target_audience_type' => TargetAudienceType::Lo,
            'natural_study_year' => 2,
        ],
        [
            'target_audience_type' => TargetAudienceType::Lo,
            'natural_study_year' => 3,
        ],
        [
            'target_audience_type' => TargetAudienceType::Lo,
            'natural_study_year' => 4,
        ],
        [
            'target_audience_type' => TargetAudienceType::Lo,
            'natural_study_year' => 5,
        ],
        [
            'target_audience_type' => TargetAudienceType::Lo,
            'natural_study_year' => 6,
        ],
    ];

    /**
     * Curriculum type for which to execute the migration.
     *
     * @var CurriculumType
     */
    protected $curriculumType;

    /**
     * Prefix for goal book goals.
     *
     * @var string
     */
    protected $goalBookPrefix = 'DL';

    /**
     * AddLearningAreasAndDomainsGoalBook constructor.
     */
    public function __construct()
    {
        $this->curriculumType = CurriculumType::Goalbook;
        $this->sequenceCalculator = app(SequenceCalculator::class);
        $this->educationalNetworkId = EducationalNetwork::whereUid('4cb01b07-b281-4599-95c2-3495a83594ea')
            ->first()->id;
    }

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $learningAreas = collect($this->learningAreaMapping)->map(function ($learningArea, $code) {
            return $this->insertCurriculumnode(
                CurriculumNodeType::LearningArea,
                $learningArea,
                $this->getLearningAreaCode($code),
            );
        });

        collect($this->domainMapping)->each(function ($domains, $learningAreaCode) use ($learningAreas): void {
            collect($domains)->each(function ($domain, $code) use ($learningAreaCode, $learningAreas): void {
                $learningArea = $learningAreas->get($learningAreaCode);
                $curriculumnode = $this->insertCurriculumnode(
                    CurriculumNodeType::Domain,
                    $domain,
                    $this->getDomainCode($learningAreaCode, $code),
                );
                $curriculumnode->parents()->attach($learningArea->id);
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $learningAreaCodes = collect($this->learningAreaMapping)
            ->keys()
            ->map(function ($learningAreaCode) {
                return $this->getLearningAreaCode($learningAreaCode);
            });
        $domainCodes = collect($this->domainMapping)
            ->map(function ($domains, $learningAreaCode) {
                return collect($domains)->map(function ($domain, $code) use ($learningAreaCode) {
                    return $this->getDomainCode($learningAreaCode, $code);
                });
            })
            ->flatten();

        $codes = $learningAreaCodes->merge($domainCodes)
            ->map(function (string $code) {
                return $this->generateReferenceCode($code);
            })
            ->all();

        CurriculumNode::whereCurriculumType($this->curriculumType)
            ->whereIn('reference_code', $codes)
            ->forceDelete();
    }

    /**
     * Generate a reference code for the curriculum node based on its code.
     *
     * @param string $code Code of the learning area.
     */
    private function generateReferenceCode(string $code): string
    {
        return sha1($this->curriculumType->value . $this->educationalNetworkId . $code);
    }

    /**
     * Insert a curriculumnode with the givden type, name and code in the database.
     *
     * @param CurriculumNodeType $curriculumnodeType Type of the curriculumnode.
     * @param string $name Name of the curriculumnode.
     * @param string $code Code of the curriculumnode.
     */
    private function insertCurriculumnode(
        CurriculumNodeType $curriculumnodeType,
        string $name,
        string $code,
    ): CurriculumNode {
        $curriculumNode = new CurriculumNode();
        $curriculumNode->educationalnetwork_id = $this->educationalNetworkId;
        $curriculumNode->name = $name;
        $curriculumNode->code = $code;
        $curriculumNode->reference_code = $this->generateReferenceCode($code);
        $curriculumNode->sequence = $this->sequenceCalculator->getNext($curriculumnodeType);
        $curriculumNode->type = $curriculumnodeType;
        $curriculumNode->curriculum_type = $this->curriculumType;
        $curriculumNode->is_active = true;
        $curriculumNode->version = 1;
        $curriculumNode->change_hash = $curriculumNode->getChangeHash();
        $curriculumNode->save();
        $this->insertGradeLevelConfiguration($curriculumNode);

        return $curriculumNode;
    }

    /**
     * Add an empty grade level configuration for the curriculum node.
     *
     * @param CurriculumNode $curriculumNode Curriculum node to create a grade level configuration for.
     */
    protected function insertGradeLevelConfiguration(CurriculumNode $curriculumNode): void
    {
        foreach ($this->gradelevelconfiguration as $grade) {
            $grade['level'] = null;
            $grade['curriculumnode_id'] = $curriculumNode->id;
            GradeLevelConfiguration::forceCreate($grade);
        }
    }

    /**
     * Get the code for the domain based on its own code and the code of its learning area.
     *
     * @param string $learningAreaCode Learning area code for the domain.
     * @param string $code Own code of the domain.
     */
    protected function getDomainCode(string $learningAreaCode, string $code): string
    {
        return self::getLearningAreaCode($learningAreaCode) . '-' . $code;
    }

    /**
     * Get the code for the learning area based on its own code.
     *
     * @param string $code Own code of the learning area.
     */
    protected function getLearningAreaCode(string $code): string
    {
        return $this->goalBookPrefix . '-' . $code;
    }
}
