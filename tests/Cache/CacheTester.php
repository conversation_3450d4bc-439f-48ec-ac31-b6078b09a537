<?php

namespace Tests\Cache;

use Cache as LaravelCache;
use Exception;
use Illuminate\Support\Facades\Cache;
use PHPUnit\Framework\Assert;

use function tenant;

class CacheTester
{
    /**
     * The tags of the cache.
     *
     * @var array
     */
    protected $tags = [];

    /**
     * The key of the cache
     *
     * @var string
     */
    protected $key;

    /**
     * Cache constructor.
     */
    public function __construct(array $tags = [])
    {
        $this->tags = $tags;
        if (Cache::getDefaultDriver() !== 'redis') {
            throw new Exception('Works only with Redis driver, change CACHE_DRIVER in phpunit.xml');
        }
    }

    /**
     * Get the tags.
     */
    public function getTags(): array
    {
        return $this->tags;
    }

    /**
     * Get the key.
     */
    public function getKey(): string
    {
        return $this->key;
    }

    /**
     * Creates a cache object with the given repository class and tags as cache tags.
     * The tenant will be automatically added as a cache tag as well.
     */
    public static function create(string $repositoryClass, array $tags = []): self
    {
        $cache = new self($tags);

        $cache->setRepository($repositoryClass);
        $cache->setTenant();

        return $cache;
    }

    /**
     * Adds the given repository classname as a cache tag.
     */
    public function setRepository(string $repositoryClass): self
    {
        return $this->addTag('repository:' . $repositoryClass);
    }

    /**
     * Adds the current tenant uit as a cache tag.
     */
    public function setTenant(): self
    {
        return $this->addTag('tenant:' . tenant()->uid);
    }

    /**
     * Adds the given tag to the cache tags.
     */
    public function addTag(string $tag): self
    {
        $this->tags[] = $tag;

        return $this;
    }

    /**
     * Sets the given key as the cache key.
     */
    public function setKey(string $key): self
    {
        $this->key = $key;

        return $this;
    }

    /**
     * Asserts that the cache with the set tags and key is empty.
     */
    public function assertEmpty(): self
    {
        Assert::assertEmpty($this->getContents());

        return $this;
    }

    /**
     * Asserts that the cache with the set tags and key is filled.
     */
    public function assertFilled(): self
    {
        Assert::assertNotEmpty($this->getContents());

        return $this;
    }

    /**
     * Asserts that the cache with the set tags and key is NULL.
     */
    public function assertNull(): self
    {
        Assert::assertNull($this->getContents());

        return $this;
    }

    /**
     * Asserts that the cache with the set tags and key is not NULL.
     */
    public function assertNotNull(): self
    {
        Assert::assertNotNull($this->getContents());

        return $this;
    }

    /**
     * Asserts that the cache with the set tags and key is has the given amount of items.
     */
    public function assertCount(int $count): self
    {
        Assert::assertCount($count, $this->getContents());

        return $this;
    }

    /**
     * Returns the contents of the cache with the set tags and key.
     */
    public function getContents(): mixed
    {
        return LaravelCache::tags($this->tags)->get($this->key);
    }
}
