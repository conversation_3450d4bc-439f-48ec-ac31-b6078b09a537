<?php

namespace App\Infrastructure\Queue\Jobs;

use App\Constants\FilesystemDisks;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use LogicException;
use Opis\JsonSchema\Validator;
use Override;

use function basename;
use function config;
use function json_decode;

class SmdExternalSqsJob extends ExternalSqsJob
{
    #[Override]
    public function fire(): void
    {
        $data = $this->getJobData();

        $internalJobClass = config('smd-event-mapping.' . $data['event']['type']);

        if ($internalJobClass !== null) {
            dispatch(app($internalJobClass, [
                'payload' => $data['payload'],
                'event' => $data['event'],
            ]));
        }

        // Processing complete, remove the job from the queue.
        $this->delete();
    }

    #[Override]
    protected function decodeAndValidate(?array $json): ?array
    {
        $decodedJson = parent::decodeAndValidate($json);

        $decodedJson = $this->validateType($decodedJson);

        $eventType = Str::lower($decodedJson['event']['type']);
        // Load all json schemas.
        $validator = new Validator();
        $storage = Storage::disk(FilesystemDisks::SMD_SCHEMAS);
        $jsonSchemas = $storage->files();
        foreach ($jsonSchemas as $file) {
            $validator->resolver()->registerFile('json-schema-id:/' . basename((string) $file), $storage->path($file));
        }

        $schemaName = $eventType === 'merge_person' ? 'merge-person-schema.json' : $eventType . '-schema.json';
        $rawMessage = json_decode((string) json_decode($this->getRawBody(), 1)['Message']);
        $result = $validator->validate($rawMessage, 'json-schema-id:/' . $schemaName);

        if (!$result->isValid()) {
            // Check if there is a version mismatch.
            $this->compareSMDVersion($decodedJson);

            // Log the validation errors.
            $validationError = $result->error();
            Log::info('JSON validation failed, ' . $validationError->keyword(), $validationError->args());

            return null;
        }

        return $decodedJson;
    }

    private function compareSMDVersion(?array $json): void
    {
        if (empty($json)) {
            return;
        }

        $jsonVersion = $json['event']['version'] ?? null;
        $currentVersion = Storage::disk(FilesystemDisks::SMD_SCHEMAS)->get('version.txt');

        if ((string) $jsonVersion !== (string) $currentVersion) {
            Log::info("SMD schema mismatch, json version: $jsonVersion SOL version $currentVersion");
        }
    }

    private function validateType(?array $json): array
    {
        if ($json === null || ($json['Message'] ?? null) === null) {
            throw new LogicException('No message found.');
        }

        $json = json_decode((string) $json['Message'], true);
        if (($json['event'] ?? null) === null) {
            throw new LogicException('No event type found. Empty message.');
        }

        $event = $json['event'];
        if (!array_key_exists($event['type'], config('smd-event-mapping'))) {
            throw new LogicException('Unsupported event type is passed: ' . $event['type'] . ' for ' . $event['uid']);
        }

        return $json;
    }
}
