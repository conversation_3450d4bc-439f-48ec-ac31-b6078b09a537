<?php

namespace App\Infrastructure\Queue\Jobs;

use App\Models\SMD\EventLogStatus;
use App\Models\SQS\SqsEventLog;
use Aws\Sqs\SqsClient;
use Carbon\Carbon;
use Illuminate\Container\Container;
use Illuminate\Queue\Jobs\SqsJob;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;
use LogicException;
use Override;

use function class_basename;

abstract class ExternalSqsJob extends SqsJob
{
    private int $sqsEventLogId;

    /** {@inheritdoc} */
    public function __construct(Container $container, SqsClient $sqs, array $job, $connectionName, $queue)
    {
        parent::__construct($container, $sqs, $job, $connectionName, $queue);
        $this->logToSqsEventLog(EventLogStatus::New);
    }

    #[Override]
    public function getName(): string
    {
        return class_basename(static::class);
    }

    /** @throws InvalidArgumentException When incorrect json message received. */
    protected function getJobData(): ?array
    {
        $payload = null;
        try {
            $payload = is_array($this->payload()) ? $this->payload() : [];
            if ($decoded = $this->decodeAndValidate($payload)) {
                return $decoded;
            }
        } catch (LogicException $e) {
            // handling will happen below.
        }

        // Message can't be processed (it will end up in the deadletter queue).
        Log::error('Unable to process queue message.', $payload);
        $this->failedToProcessJob();

        return null;
    }

    /** @throws InvalidArgumentException When called. */
    protected function failedToProcessJob(): void
    {
        $this->markAsFailed();

        // Release back on SQS queue. Message will end up on the deadletter queue.
        $this->release();

        Log::error('Unable to process queue message.', $this->payload());
        throw new InvalidArgumentException('Invalid json');
    }

    protected function decodeAndValidate(?array $json): ?array
    {
        return $json;
    }

    /** {@inheritdoc} */
    #[Override]
    public function failed($exception): void
    {
        $this->markAsFailed();
    }

    #[Override]
    public function markAsFailed(): void
    {
        parent::markAsFailed();
        $this->logToSqsEventLog(EventLogStatus::Failed);
    }

    #[Override]
    public function delete(): void
    {
        parent::delete();
        $this->logToSqsEventLog(EventLogStatus::Processed);
    }

    protected function logToSqsEventLog(EventLogStatus $eventLogStatus): void
    {
        if (!isset($this->sqsEventLogId) && $eventLogStatus->value === EventLogStatus::New->value) {
            $this->sqsEventLogId = SqsEventLog::create(
                [
                    'source' => $this->getName(),
                    'status' => $eventLogStatus->value,
                    'payload' => $this->getRawBody(),
                ],
            )->id;

            return;
        }

        SqsEventLog::getQuery()
            ->where('id', $this->sqsEventLogId)
            ->update(
                [
                    'status' => $eventLogStatus->value,
                    'updated_at' => Carbon::now(),
                ],
            );
    }
}
