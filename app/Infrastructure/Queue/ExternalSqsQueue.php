<?php

namespace App\Infrastructure\Queue;

use App\Constants\CacheStores;
use App\Infrastructure\Queue\Jobs\BingelResultsExternalSqsJob;
use App\Infrastructure\Queue\Jobs\LambdaResultsExternalSqsJob;
use App\Infrastructure\Queue\Jobs\SmdExternalSqsJob;
use Aws\Sqs\Exception\SqsException;
use Illuminate\Contracts\Queue\Queue as QueueContract;
use Illuminate\Queue\SqsQueue;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Override;

use function config;
use function explode;

class ExternalSqsQueue extends SqsQueue implements QueueContract
{
    /**
     * The cache key to use to track if enabled or disabled.
     *
     * @var string
     */
    private const CACHE_KEY = 'external_sqs_disabled';

    /**
     * Buffer array of messages retrieved from SQS.
     *
     * @var array
     */
    protected $receivedMessageBuffer = [];

    private ?string $queue = null;
    private ?string $queueUrl = null;

    private array $queueMapping;

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function pop($queue = null)
    {
        if ($this->isDisabled()) {
            return null;
        }

        $queueUrl = $this->getQueue($queue);

        if (empty($this->receivedMessageBuffer)) {
            $response = $this->sqs->receiveMessage([
                'QueueUrl' => $queueUrl,
                'AttributeNames' => ['ApproximateReceiveCount'],
                'MaxNumberOfMessages' => 10,
            ]);

            $this->queue = $queue;
            $this->queueUrl = $queueUrl;
            $this->receivedMessageBuffer = $response['Messages'] ?? [];
        }

        $message = array_shift($this->receivedMessageBuffer);

        if ($message === null) {
            return null;
        }

        $queueJobClass = $this->getClassName();
        if ($queueJobClass === null) {
            return null;
        }

        return new $queueJobClass($this->container, $this->sqs, $message, $this->connectionName, $this->queueUrl);
    }

    private function getClassName(): ?string
    {
        if (isset($this->queueMapping)) {
            return $this->queueMapping[$this->queue] ?? null;
        }

        $this->queueMapping = [
            config('queue.queue-names.smd') => SmdExternalSqsJob::class,
            config('queue.queue-names.bingel-results') => BingelResultsExternalSqsJob::class,
            config('queue.queue-names.lambda-results') => LambdaResultsExternalSqsJob::class,
        ];

        return $this->queueMapping[$this->queue] ?? null;
    }

    /**
     * Get the number of queue jobs that are ready to process.
     *
     * @param string|null $queue The queue name.
     */
    public function readyNow(?string $queue = null): int
    {
        if ($this->isDisabled()) {
            return 0;
        }

        return collect(explode(',', $queue ?? ''))->sum(fn(string $singleQueue): int => $this->size($singleQueue));
    }

    /** {@inheritdoc} */
    #[Override]
    public function size($queue = null)
    {
        try {
            return parent::size($queue);
        } catch (SqsException $sqsException) {
            // This method is only used for auto-scaling, so it's not that important that a request passes.
            Log::debug('Could not retrieve queue size.', ['exception' => $sqsException]);

            return 0;
        }
    }

    /**
     * Check if queue processing has been disabled.
     */
    protected function isDisabled(): bool
    {
        return Cache::store(CacheStores::MAINTENANCE)->get(self::CACHE_KEY, false);
    }

    /**
     * Disable queue processing.
     */
    public static function disable(): void
    {
        self::setDisabledStatus(true);
    }

    /**
     * Enable queue processing.
     */
    public static function enable(): void
    {
        self::setDisabledStatus(false);
    }

    /**
     * Set the disabled status of the queue.
     */
    private static function setDisabledStatus(bool $disabled): void
    {
        Cache::store(CacheStores::MAINTENANCE)->forever(self::CACHE_KEY, $disabled);
    }
}
