<?php

namespace App\Infrastructure\Csp\Policies;

use Override;
use <PERSON>tie\Csp\Directive;

class ProductionPolicy extends CommonPolicy
{
    /**
     * {@inheritdoc}
     */
    #[Override]
    public function configure(): void
    {
        parent::configure();

        $this
            ->addDirective(Directive::UPGRADE_INSECURE_REQUESTS, '')
            ->addDirective(Directive::BLOCK_ALL_MIXED_CONTENT, '');
    }
}
