<?php

namespace App\Infrastructure\Database\Eloquent\Relations;

use App\Events\RelationSynced;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsToMany as BaseBelongsToMany;
use Illuminate\Support\Collection as SupportCollection;
use Override;

class BelongsToMany extends BaseBelongsToMany
{
    /**
     * {@inheritdoc}
     *
     * @param array|Collection|SupportCollection $ids Ids to sync.
     * @param bool $detaching Detach the ids that are not listed.
     *
     * @return array
     *
     * @SuppressWarnings(PHPMD.BooleanArgumentFlag)
     */
    #[Override]
    public function sync($ids, $detaching = true)
    {
        $result = parent::sync($ids, $detaching);

        $this->fireSyncEvent($result);

        return $result;
    }

    /**
     * Fire the synced event based on the sync function result.
     *
     * @param array $syncResult Result of the sync function.
     */
    protected function fireSyncEvent(array $syncResult): void
    {
        $model = $this->getParent();

        // If the eventDispatcher for this model has been unset, bail out.
        if (!$model::getEventDispatcher()) {
            return;
        }

        $attached = $syncResult['attached'];
        $detached = $syncResult['detached'];

        if (count($attached) || count($detached)) {
            $eventAlias = $model->getEventAlias($this->getTable());
            $eventData = $eventAlias ? new $eventAlias($model, $syncResult) : new RelationSynced($model, $syncResult);
            $model::getEventDispatcher()->dispatch($eventData);
        }
    }
}
