<?php

namespace App\Traits;

use function array_key_exists;

trait FrontendValidationRules
{
    protected array $allowedRules = [
        'after',
        'alpha',
        'alpha_dash',
        'alpha_helper',
        'alpha_num',
        'alpha_spaces',
        'before',
        'between',
        'confirmed',
        'credit_card',
        'date_between',
        'date_format',
        'decimal',
        'digits',
        'dimensions',
        'email',
        'ext',
        'image',
        'in',
        'index',
        'integer',
        'ip',
        'is',
        'is_not',
        'length',
        'max',
        'max_value',
        'mimes',
        'min',
        'min_value',
        'notIn',
        'numeric',
        'regex',
        'required',
        'size',
        'url',
    ];

    protected array $rulesToSkip = [];

    /**
     * Get the validation rules to be used as Frontend validation as json.
     */
    public function getFrontendValidationRulesAsJson(): string
    {
        return json_encode($this->getValidationRulesAttribute());
    }

    /**
     * Get the validation rules to be used as Frontend validation as json.
     */
    public function getValidationRulesAttribute(): array
    {
        $rules = $this->rules();
        foreach ($rules as $model => $rule) {
            $rules[$model] = implode('|', $this->cleanRulesForFrontend($model, $rule));
        }

        return $rules;
    }

    public function withoutRule(string $attribute, string $rule): self
    {
        $this->rulesToSkip[$attribute][$rule] = true;

        return $this;
    }

    private function cleanRulesForFrontend(string $model, array $rules): array
    {
        $newRules = [];
        foreach ($rules as $rule) {
            $searchString = '';
            if (is_string($rule)) {
                // Use only the first element of the rule (max instead of max:255).
                $searchString = explode(':', $rule)[0];
            }
            if (is_object($rule)) {
                $searchString = get_class($rule);
            }

            if (array_key_exists($searchString, $this->rulesToSkip[$model] ?? [])) {
                continue;
            }

            if (in_array($searchString, $this->allowedRules, true)) {
                $newRules[] = $rule;
            }
        }

        return $newRules;
    }
}
