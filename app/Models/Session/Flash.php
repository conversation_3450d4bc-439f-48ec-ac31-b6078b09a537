<?php

namespace App\Models\Session;

enum Flash: string
{
    case InactivePupils = 'inactivePupils';
    case InactivePupilsDataLimitation = 'inactivePupilsDataLimitation';

    public function getMessage(): array
    {
        $messageObject = [
            'color' => 'info',
            'translationKeyPrefix' => 'labels.flash.' . $this->value,
        ];

        if ($this === self::InactivePupils) {
            $messageObject['color'] = 'error';
            $messageObject['timeout'] = 0;
        }

        return $messageObject;
    }
}
