<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

use function config;

class CheckTestUserAuthentication
{
    /**
     * @throws AuthenticationException
     */
    public function handle(Request $request, Closure $next): mixed
    {
        if (config('test-user.token') == null) {
            Log::debug('Token is not set.');
            throw new AuthenticationException();
        }
        if (
            $request->hasHeader('sec-datadog') &&
            !Str::startsWith(
                $request->header('sec-datadog', ''),
                'Request sent by a Datadog Synthetics Browser Test (https://docs.datadoghq.com/synthetics/) - test_id:',
            )
        ) {
            Log::debug('Referer does not start with datadog url or has no sec-datadog header.');
            throw new AuthenticationException();
        }
        if (
            $request->hasHeader('referer') &&
            !Str::startsWith($request->header('referer', ''), 'https://app.datadoghq.eu')
        ) {
            Log::debug('Referer has no correct referer header.', ['referer' => $request->header('referer')]);
            throw new AuthenticationException();
        }
        if (!$request->has('datadog-token')) {
            Log::debug('Datadog token is not set.');
            throw new AuthenticationException();
        }
        if ($request->get('datadog-token') !== config('test-user.token')) {
            Log::debug('Invalid Datadog token.', ['datadog-token' => $request->get('datadog-token')]);
            throw new AuthenticationException();
        }

        return $next($request);
    }
}
