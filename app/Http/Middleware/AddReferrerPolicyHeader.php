<?php

namespace App\Http\Middleware;

use App\Constants\HttpHeaders;
use Closure;
use Illuminate\Http\Request;

class AddReferrerPolicyHeader
{
    /**
     * Handle a response by adding the Referrer-Policy header.
     *
     * @param Request $request The incoming Http request.
     * @param Closure $next Closure to be called when the middleware 'passes'.
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $response = $next($request);

        $response->headers->set(HttpHeaders::REFERRER_POLICY, 'same-origin');

        return $response;
    }
}
