<?php

namespace App\Http\Middleware;

use App\Constants\HttpHeaders;
use Closure;
use Illuminate\Http\Request;

class AddFeaturePolicyHeader
{
    /**
     * Handle a response by adding the Feature-Policy header.
     * https://scotthelme.co.uk/a-new-security-header-feature-policy/
     *
     * @param Request $request The incoming Http request.
     * @param Closure $next Closure to be called when the middleware 'passes'.
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $response = $next($request);

        $header = [
            "accelerometer 'none'",
            "ambient-light-sensor 'none'",
            "geolocation 'none'",
            "gyroscope 'none'",
            "magnetometer 'none'",
            "microphone 'none'",
            "midi 'none'",
            "payment 'none'",
            "speaker 'none'",
            "sync-xhr 'none'",
            "usb 'none'",
            "vr 'none'",
            "picture-in-picture 'none'",
            "autoplay 'none'",
            "camera 'self'",
            "encrypted-media 'self'",
            "fullscreen 'self'",
        ];

        $response->headers->set(HttpHeaders::FEATURE_POLICTY, implode('; ', $header));

        return $response;
    }
}
