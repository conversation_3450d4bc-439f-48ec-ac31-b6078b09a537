<?php

namespace App\Services\Lambda\Domain;

use Illuminate\Support\Collection;
use InvalidArgumentException;
use Override;

class ZipPathCollection extends Collection
{
    /** {@inheritdoc} */
    public function __construct($items = [])
    {
        parent::__construct($items);
        $this->each(fn($item): bool => $this->assertZipPath($item));
    }

    /** {@inheritdoc} */
    #[Override]
    public function add($item)
    {
        $this->assertZipPath($item);

        return parent::add($item);
    }

    /** {@inheritdoc} */
    #[Override]
    public function push(...$values)
    {
        foreach ($values as $value) {
            $this->assertZipPath($value);
        }

        return parent::push(...$values);
    }

    private function assertZipPath(mixed $item): bool
    {
        if (!$item instanceof ZipPath) {
            throw new InvalidArgumentException('Items must be of type ZipPath');
        }

        return true;
    }
}
