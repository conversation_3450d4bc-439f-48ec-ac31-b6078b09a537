<?php

namespace App\Services\MediaLibrary;

use Exception;
use Spatie\Image\Enums\CropPosition;
use Spatie\Image\Enums\Fit;
use Spatie\MediaLibrary\HasMedia;

use function config;

class MediaConversions
{
    public const THUMBNAIL = 'thumb';

    public const NORMAL = 'normal';

    public const LARGE = 'large';

    /**
     * @throws Exception
     */
    public static function registerConversions(string $modelType, HasMedia $model): void
    {
        $config = config('media-library.conversions.' . $modelType);
        if (!is_array($config)) {
            throw new Exception('medialibrary registerConversions config not found');
        }

        foreach ($config as $conversion => $setting) {
            $mediaConversion = $model->addMediaConversion($conversion);
            if (isset($setting['manipulations'])) {
                foreach ($setting['manipulations'] as $manipulation) {
                    if ($manipulation['type'] instanceof CropPosition) {
                        $mediaConversion
                            ->fit(Fit::Contain, $manipulation['width'], $manipulation['height'])
                            ->crop($manipulation['width'], $manipulation['height'], $manipulation['type']);
                    }
                }
            }

            if (isset($setting['width'])) {
                $mediaConversion->width($setting['width']);
            }
            if (isset($setting['height'])) {
                $mediaConversion->height($setting['height']);
            }
        }
    }
}
