<?php

namespace App\Services\File;

use App\Models\File\FileUsage;
use Cfa\Common\Domain\School\School;

use function tenant;

class FilePathService
{
    public function getStoragePathForFile(FileUsage $usage, School $school, ?string $fileName = null): ?string
    {
        switch ($usage->value) {
            case FileUsage::ReportPrints->value:
                return tenant()->uid . '/report_prints/' . $school->uid . '/' . uuid() . '/' . $fileName;
            case FileUsage::CareExports->value:
                return tenant()->uid . '/care_exports/' . $school->uid . '/' . $fileName;
            default:
                return null;
        }
    }
}
