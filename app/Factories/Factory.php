<?php

namespace App\Factories;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\Factory as LaravelFactory;
use Illuminate\Database\Eloquent\Model;
use Override;

/**
 * Factory
 *
 * @SuppressWarnings(PHPMD.NumberOfChildren)
 */
abstract class Factory extends LaravelFactory
{
    /** {@inheritdoc} */
    #[Override]
    public function create($attributes = [], ?Model $parent = null)
    {
        return Model::withoutEvents(fn(): Collection|Model => parent::create($attributes, $parent));
    }

    public function createWithEvents(array $attributes = [], ?Model $parent = null): Model
    {
        return parent::create($attributes, $parent);
    }
}
