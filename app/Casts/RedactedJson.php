<?php

namespace App\Casts;

use Override;

use function is_string;

class Redacted<PERSON>son extends Json
{
    /** {@inheritdoc} */
    #[Override]
    public function set($model, string $key, $value, array $attributes)
    {
        $value = parent::set($model, $key, $value, $attributes);

        if (is_string($value)) {
            // Blacklist is embedded in the string for performance reasons.
            // If it becomes unclear, we should look at this again.
            $value = preg_replace(
                '/("(password|national_register_number)":")("|[^\\\]"|.+?[^\\\]")/',
                '$1**redacted**"',
                $value,
            );
        }

        return $value;
    }
}
