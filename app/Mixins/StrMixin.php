<?php

namespace App\Mixins;

use Closure;
use Illuminate\Support\Str;

use function collect;
use function explode;
use function preg_replace;

class StrMixin
{
    /**
     * Concatenates a number of strings and cleans it so it can be used for matching
     */
    public function cleanAndNormalizeInput(): Closure
    {
        return function (string ...$strings): string {
            return collect($strings)
                ->map(function (string $name) {
                    $asciiName = Str::ascii($name);
                    $alphabeticName = preg_replace('/[^A-Za-z]/', '', $asciiName);

                    return Str::lower($alphabeticName);
                })
                ->implode('');
        };
    }

    /**
     * Concatenates a number of strings and cleans it so it can be used for matching and
     * sort the given strings and them split on spaces before cleaning.
     */
    public function cleanAndNormalizeInputWithSorting(): Closure
    {
        return function (string ...$strings): string {
            return Str::cleanAndNormalizeInput(
                ...collect($strings)
                    ->flatMap(fn(string $name): array => explode(' ', $name))
                    ->sortBy(fn(string $name): string => Str::lower($name))
                    ->values()
                    ->all(),
            );
        };
    }

    /**
     * Will remove the zero with space chars from the given string.
     *
     * @see https://gist.github.com/ahmadazimi/b1f1b8f626d73728f7aa#gistcomment-2626380
     */
    public function cleanZeroWidthSpaces(): Closure
    {
        return function (string $string): string {
            return preg_replace('/[\x{200B}-\x{200D}\x{FEFF}]/u', '', $string);
        };
    }

    public function matchWithDistance(): Closure
    {
        return function (string $first, string $second, int $allowedDistance = 2): bool {
            if ($first === $second) {
                return true;
            }

            return levenshtein($first, $second) <= $allowedDistance;
        };
    }

    public function alphaNumericSnake(): Closure
    {
        return function (string $value): string {
            $identifier = 'alphaNumericSnake';
            $key = $value;

            if (isset(static::$snakeCache[$key][$identifier])) {
                return static::$snakeCache[$key][$identifier];
            }

            if (!ctype_lower($value)) {
                $value = Str::of($value)
                    ->ascii()
                    ->replaceMatches('/[^A-Za-z0-9]/u', '_')
                    ->squish()
                    ->replaceMatches('/(\d+)/u', '_$1_')
                    ->replaceMatches('/([A-Z][a-z]+)/u', '_$1_')
                    ->replaceMatches('/([a-z])([A-Z]+)/u', '$1_$2')
                    ->replaceMatches('/_+/u', '_')
                    ->trim('_')
                    ->lower()
                    ->toString();
            }

            return static::$snakeCache[$key][$identifier] = $value;
        };
    }
}
