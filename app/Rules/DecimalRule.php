<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use InvalidArgumentException;
use Override;

/**
 * Class DecimalRule
 */
class DecimalRule implements Rule
{
    /**
     * @var int
     */
    private $wholeNumbers;

    /**
     * @var int
     */
    private $decimalNumbers;

    /**
     * @var bool
     */
    private $allowIntegers;

    /**
     * DecimalRule constructor.
     *
     * @throws InvalidArgumentException
     * @SuppressWarnings(PHPMD.BooleanArgumentFlag)
     */
    public function __construct(int $wholeNumbers = 8, int $decimalNumbers = 2, bool $allowIntegers = true)
    {
        if ($wholeNumbers < 1 || $decimalNumbers < 1) {
            throw new InvalidArgumentException(trans('exceptions.invalid_argument_exception.decimal-rule'));
        }
        $this->wholeNumbers = $wholeNumbers;
        $this->decimalNumbers = $decimalNumbers;
        $this->allowIntegers = $allowIntegers;
    }

    /**
     * Determine if the validation rule passes.
     *
     * {@inheritdoc}
     */
    #[Override]
    public function passes($attribute, $value): bool
    {
        if (!is_string($value) && !is_numeric($value)) {
            return false;
        }

        $decimalsOptional = $this->allowIntegers ? '?' : '';

        return preg_match(
            "/^\d{1,{$this->wholeNumbers}}(\.\d{1,{$this->decimalNumbers}})$decimalsOptional$/",
            (string) $value,
        );
    }

    /**
     * Get the validation error message.
     */
    #[Override]
    public function message(): string
    {
        return trans('validation.decimal.invalid');
    }
}
