<?php

namespace App\Console\Commands\Translations;

use DirectoryIterator;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Process;

use function base_path;
use function config;

class GenerateVueI18n extends Command
{
    /** @var string */
    protected $signature = 'tms:build:generate-vue-i18n';

    /** @var string */
    protected $description = 'Generates a Vue compatible JSON out of project translations.';

    protected array $files = [];

    public function handle(): int
    {
        $languagePaths = $this->getLanguagePaths();
        $allConvertedTranslations = [];

        foreach ($languagePaths as $language => $path) {
            $translations = $this->getAllTranslationsForLanguage($path);
            $convertedTranslations = $this->convertTranslations($translations);

            $allConvertedTranslations[$language] = $convertedTranslations;
        }

        $frontendTranslations = 'export default ' .
            json_encode($allConvertedTranslations, JSON_UNESCAPED_UNICODE) .
            PHP_EOL;

        $filePath = base_path() . config('vue-i18n-generator.jsFile');
        file_put_contents($filePath, $frontendTranslations);

        Process::run(['node_modules/.bin/prettier', '--write', $filePath])->throw();

        return 0;
    }

    private function getLanguagePaths(): array
    {
        $root = base_path() . config('vue-i18n-generator.langPath');
        $rootDirectory = new DirectoryIterator($root);

        $excludeDirectories = config('vue-i18n-generator.excludes');

        $languages = [];

        foreach ($rootDirectory as $languageDirectory) {
            if ($languageDirectory->isDot() || $languageDirectory->isDir() === false) {
                continue;
            }

            $directory = $languageDirectory->getFilename();

            if (in_array($directory, $excludeDirectories)) {
                continue;
            }

            $languages[$languageDirectory->getFilename()] = $languageDirectory->getRealPath();
        }

        ksort($languages);

        return $languages;
    }

    private function getAllTranslationsForLanguage(string $path): array
    {
        $languageDirectory = new DirectoryIterator($path);

        $filesToConvert = config('vue-i18n-generator.langFiles');

        $translations = [];

        foreach ($languageDirectory as $translationFile) {
            if ($translationFile->isDot() || $translationFile->isDir() === true) {
                continue;
            }

            $fileName = $this->removeExtension($languageDirectory->getFilename());

            if (!in_array($fileName, $filesToConvert)) {
                continue;
            }

            $translation = include $translationFile->getRealPath();

            if (is_array($translation) === false) {
                continue;
            }

            $translations[$fileName] = $translation;
        }

        ksort($translations);

        return $translations;
    }

    /** @SuppressWarnings(PHPMD.BooleanArgumentFlag) */
    private function convertTranslations(array $translations, bool $withIndex = false): array
    {
        $indexFiles = config('vue-i18n-generator.variableIndexFiles');

        $converted = [];

        foreach ($translations as $translationKey => $translationValue) {
            if (is_array($translationValue)) {
                if ($withIndex === false && in_array($translationKey, $indexFiles, true)) {
                    $withIndex = true;
                }

                $converted[$translationKey] = $this->convertTranslations($translationValue, $withIndex);

                if ($withIndex === true && in_array($translationKey, $indexFiles, true)) {
                    $withIndex = false;
                }
            }

            if (is_string($translationValue)) {
                $converted[$translationKey] = $this->adjustString($translationValue, $withIndex);
            }
        }

        return $converted;
    }

    /** @SuppressWarnings(PHPMD.BooleanArgumentFlag) */
    private function adjustString(string $string, bool $withIndex): string
    {
        $index = 0;

        return preg_replace_callback(
            "/(?<!mailto|tel):\w+/",
            function ($matches) use ($withIndex, &$index) {
                if ($withIndex === true) {
                    return '{' . $index++ . '}';
                }

                return '{' . mb_substr($matches[0], 1) . '}';
            },
            $string,
        );
    }

    private function removeExtension(string $filename): string
    {
        $pos = mb_strrpos($filename, '.');
        if ($pos === false) {
            return $filename;
        }

        return mb_substr($filename, 0, $pos);
    }
}
