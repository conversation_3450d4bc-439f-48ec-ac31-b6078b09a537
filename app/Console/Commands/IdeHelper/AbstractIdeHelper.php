<?php

namespace App\Console\Commands\IdeHelper;

use Barryvdh\Reflection\DocBlock;
use Barryvdh\Reflection\DocBlock\Serializer as DocBlockSerializer;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

/**
 * Class AbstractIdeHelper
 *
 * @codeCoverageIgnore
 */
abstract class AbstractIdeHelper extends Command
{
    /**
     * Reference to the local disk storage.
     *
     * @var Storage
     */
    protected $localDiskStorage;

    /**
     * Create a new ide helper instance.
     */
    public function __construct()
    {
        parent::__construct();

        $this->localDiskStorage = Storage::createLocalDriver(
            [
                'root' => '/',
            ],
        );
    }

    /**
     * Write the given PhpDoc area to the given file.
     *
     * @param string $fileName File to write the doc to.
     * @param DocBlock $docBlock Doc block to write.
     * @param string|null $originalDoc Original doc block (if any).
     * @param string $className Name of the class.
     */
    protected function writePhpDocToFile(
        string $fileName,
        DocBlock $docBlock,
        ?string $originalDoc,
        string $className,
    ): void {
        $docComment = new DocBlockSerializer()->getDocComment($docBlock);
        $cleanedDocComment = '';
        // Remove withe space at end of line.
        foreach (preg_split("/\n/", $docComment) as $line) {
            $cleanedDocComment .= rtrim((string) $line) . "\n";
        }
        $cleanedDocComment = substr($cleanedDocComment, 0, -1);
        $contents = $this->localDiskStorage->get($fileName);
        $contents = $originalDoc ? Str::replaceFirst($originalDoc, $cleanedDocComment, $contents) :
            $this->insertNewPhpDoc($className, $cleanedDocComment, $contents);
        if ($this->localDiskStorage->put($fileName, $contents)) {
            $this->info('Written new phpDocBlock to ' . $fileName);
        }
    }

    /**
     * Insert a new PhpDoc area in the given file contents.
     *
     * @param string $classname Name of the class.
     * @param string $docComment Doc comment to insert.
     * @param string $fileContents Current file contents.
     */
    protected function insertNewPhpDoc(string $classname, string $docComment, string $fileContents): string
    {
        $needle = "class {$classname}";
        $replace = "{$docComment}\nclass {$classname}";
        if (Str::contains($fileContents, $needle)) {
            $fileContents = Str::replaceFirst($needle, $replace, $fileContents);
        }

        return $fileContents;
    }
}
