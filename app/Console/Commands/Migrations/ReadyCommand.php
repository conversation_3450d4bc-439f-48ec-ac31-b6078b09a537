<?php

namespace App\Console\Commands\Migrations;

use Illuminate\Database\Console\Migrations\StatusCommand;
use Illuminate\Support\Collection;
use Override;

use function in_array;

class ReadyCommand extends StatusCommand
{
    /** @var string */
    protected $name = 'tms:server:migrations-ready';

    /** @var string */
    protected $description = 'Return 0 if all migrations have been run, else return 1.';

    public function __construct()
    {
        parent::__construct(app('migrator'));
    }

    /** {@inheritdoc} */
    #[Override]
    public function handle()
    {
        return $this->migrator->usingConnection($this->option('database'), function () {
            if (!$this->migrator->repositoryExists()) {
                $this->error('Migration table not found.');

                return 1;
            }

            $ran = $this->migrator->getRepository()->getRan();

            $containsPendingMigrations = Collection::make($this->getAllMigrationFiles())
                ->contains(function ($migration) use ($ran) {
                    $migrationName = $this->migrator->getMigrationName($migration);

                    if (in_array($migrationName, $ran)) {
                        return false;
                    }

                    return $this->isMigrationAllowedToRun($migration);
                });

            return $containsPendingMigrations ? 1 : 0;
        });
    }

    private function isMigrationAllowedToRun(string $migration): bool
    {
        $this->migrator->requireFiles([$migration]);

        return $this->migrator->isAllowedToRun($migration);
    }
}
