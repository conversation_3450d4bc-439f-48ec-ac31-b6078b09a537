<?php

namespace App\Console\Commands\System;

use Dotenv\Parser\Entry;
use Dotenv\Parser\Parser;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

use function app;
use function file_exists;
use function file_get_contents;
use function file_put_contents;

class PopulateEnvFile extends Command
{
    /** @var string */
    protected $signature = 'tms:composer:populate-env';

    /** @var string */
    protected $description = 'Fills your .env file based by merging with .env.example.';

    private int $counter = 0;

    public function handle(): void
    {
        $this->laravel->loadEnvironmentFrom('.env');
        $envPath = $this->laravel->environmentFilePath();

        file_put_contents(
            $envPath,
            file_exists($envPath . '.local-overwrites') ? file_get_contents($envPath . '.local-overwrites') : '',
        );

        $currentEnvFile = file_get_contents($envPath);

        if (trim($currentEnvFile) !== '') {
            $currentEnvFile .= "\n\n";
        }

        $defaultEntries = app(Parser::class)->parse(file_get_contents($envPath . '.example'));

        collect($defaultEntries)
            ->sortBy(fn(Entry $entry): string => $entry->getName())
            ->each(function (Entry $entry) use (&$currentEnvFile): void {
                $currentEnvFile = $this->setEnvironmentVariableInFile(
                    $currentEnvFile,
                    $entry->getName(),
                    $entry->getValue()->get()->getChars(),
                );
            });

        file_put_contents($envPath, $currentEnvFile);
        $this->info('Added ' . $this->counter . ' .env variables.');
    }

    protected function setEnvironmentVariableInFile(string $currentEnvFile, string $envKey, string $envValue): string
    {
        $newEntry = "{$envKey}='{$envValue}'";

        if (Str::contains($currentEnvFile, [$envKey . '=', $envKey . ' '])) {
            $this->info($envKey . ' not added, already present', 'v');

            return $currentEnvFile;
        }

        $this->info('Added ' . $envKey, 'vv');
        $this->counter++;

        return $currentEnvFile . ($newEntry . "\n");
    }
}
