<?php

namespace App\Repositories;

use App\Models\Model;
use Illuminate\Database\Eloquent\Builder;

interface RepositoryInterface
{
    /**
     * Saves the model and clears the cache.
     */
    public function save(Model $model): bool;

    /**
     * Creates a model using the given attributes and clears the cache.
     */
    public function create(array $attributes): Model;

    /**
     * Updates the model with the given attributes and flushes the cache.
     */
    public function update(Model $model, array $attributes): bool;

    /**
     * Deletes the model and clears the cache.
     */
    public function delete(Model $model): ?bool;

    /**
     * Restores the model and flushes the cache.
     *
     * @return void
     */
    public function restore(Model $model): ?bool;

    /**
     * Archives the model and clears the cache.
     */
    public function archive(Model $model): bool;

    /**
     * Dearchive the model and clears the cache.
     */
    public function dearchive(Model $model): bool;

    /**
     * Force deletes the given model or builder and clears the cache.
     */
    public function forceDelete(Model|Builder $modelOrBuilder): int;
}
