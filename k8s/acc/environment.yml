apiVersion: v1
kind: ConfigMap
metadata:
  name: environment
data:
  AWS_ACCOUNT: acc
  AWS_ACCOUNT_ID: '************'
  AWS_CERTIFICATE_ARN: arn:aws:acm:eu-west-1:************:certificate/bf792071-8a4b-4b7e-8d66-41a5a151484b
  AWS_S3_BUCKET_LOGS: bingeltms-fl-$(ENV)-logs-$(ENV)
  HOSTNAME: 'acc.bingel.be'
  HOSTNAME_INTERNAL: 'bingel-acc.vnnaws.net'
  HOSTNAME_CLOUDFRONT: 'acc.bingel.be'
  ENV: acc
  WHITELIST_BINGEL_CLUSTER: *************/32,************/32,************/32,**************/32,**************/32,***********/32
  # https://support.atlassian.com/bitbucket-cloud/docs/what-are-the-bitbucket-cloud-ip-addresses-i-should-use-to-configure-my-corporate-firewall/
  WHITELIST_BITBUCKET_IP_RANGE: *************/32,************/32,**************/32,*************/32,**************/32,***********/32,**************/32,************/32,************/32,**************/32,**************/32,************/32,**************/32,*************/32,*************/32,************/32,*************/32,************/32,***************/32,**************/32,************/32,*************/32,**************/32,*************/32
  WHITELIST_WISCO_IP_RANGE: ************/32,*************/32,************/32
  WHITELIST_CFA: $(WHITELIST_VPN),$(WHITELIST_BROWSERSTACK),$(WHITELIST_BINGEL_CLUSTER),$(WHITELIST_BITBUCKET_IP_RANGE),$(WHITELIST_WISCO_IP_RANGE)
