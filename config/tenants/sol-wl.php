<?php

use App\Constants\Subdomain;
use Cfa\Common\Domain\Gate\GateName;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetwork;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Common\Domain\Tenant\TenantId;
use Cfa\Planner\Domain\Collection\CollectionTemplate;
use Cfa\Planner\Domain\CurriculumNode\GradeLevelConfiguration\GradeLevelClassification;
use Cfa\Planner\Domain\Settings\Activity\ActivityType;

$targetAudienceJk = TargetAudienceType::Jk->name;
$targetAudienceOk = TargetAudienceType::Ok->name;
$targetAudienceKo = TargetAudienceType::Ko->name;
$targetAudienceLo = TargetAudienceType::Lo->name;

return [
    'id' => TenantId::SolWl->value,
    'name' => 'Wazzou TMS',
    'baseDomain' => env('BASE_DOMAIN', 'wazzou.be'),
    'subdomains' => [],
    'localeConfig' => [
        'preferredLocale' => 'fr',
        'preferredSuffix' => 'be',
        'fallbackLocale' => 'nl',
        'fallbackSuffix' => 'be',
        'timezone' => 'Europe/Brussels',
    ],
    'educationalNetworkMapping' => [
        'GO' => EducationalNetwork::GO_UID,
        'OVSG' => EducationalNetwork::OVSG_UID,
        'VVKBAO' => EducationalNetwork::VVKBAO_UID,
    ],
    'educationalNetworkConfig' => [
        // GO.
        EducationalNetwork::GO_UID => [
            'learningTrailConfigs' => [
                [
                    'name' => 'KK',
                    'naturalStudyYear' => null,
                    'targetAudienceType' => $targetAudienceJk,
                ],
                [
                    'name' => 'GK',
                    'naturalStudyYear' => null,
                    'targetAudienceType' => $targetAudienceOk,
                ],
                [
                    'name' => '1ste',
                    'naturalStudyYear' => 1,
                    'targetAudienceType' => $targetAudienceLo,
                ],
                [
                    'name' => '2de',
                    'naturalStudyYear' => 2,
                    'targetAudienceType' => $targetAudienceLo,
                ],
                [
                    'name' => '3de',
                    'naturalStudyYear' => 3,
                    'targetAudienceType' => $targetAudienceLo,
                ],
                [
                    'name' => '4de',
                    'naturalStudyYear' => 4,
                    'targetAudienceType' => $targetAudienceLo,
                ],
                [
                    'name' => '5de',
                    'naturalStudyYear' => 5,
                    'targetAudienceType' => $targetAudienceLo,
                ],
                [
                    'name' => '6de',
                    'naturalStudyYear' => 6,
                    'targetAudienceType' => $targetAudienceLo,
                ],
            ],
            'gradeLevelClassification' => [
                'A' => GradeLevelClassification::Introduction->value,
                'B' => GradeLevelClassification::Mandatory->value,
                'H' => GradeLevelClassification::Repetition->value,
            ],
        ],
        // OVSG.
        EducationalNetwork::OVSG_UID => [
            'learningTrailConfigs' => [
                [
                    'name' => '2,5',
                    'naturalStudyYear' => 0,
                    'targetAudienceType' => $targetAudienceKo,
                ],
                [
                    'name' => '3',
                    'naturalStudyYear' => 1,
                    'targetAudienceType' => $targetAudienceKo,
                ],
                [
                    'name' => '4',
                    'naturalStudyYear' => 2,
                    'targetAudienceType' => $targetAudienceKo,
                ],
                [
                    'name' => '5',
                    'naturalStudyYear' => 3,
                    'targetAudienceType' => $targetAudienceKo,
                ],
                [
                    'name' => 'l1',
                    'naturalStudyYear' => 1,
                    'targetAudienceType' => $targetAudienceLo,
                ],
                [
                    'name' => 'l2',
                    'naturalStudyYear' => 2,
                    'targetAudienceType' => $targetAudienceLo,
                ],
                [
                    'name' => 'l3',
                    'naturalStudyYear' => 3,
                    'targetAudienceType' => $targetAudienceLo,
                ],
                [
                    'name' => 'l4',
                    'naturalStudyYear' => 4,
                    'targetAudienceType' => $targetAudienceLo,
                ],
                [
                    'name' => 'l5',
                    'naturalStudyYear' => 5,
                    'targetAudienceType' => $targetAudienceLo,
                ],
                [
                    'name' => 'l6',
                    'naturalStudyYear' => 6,
                    'targetAudienceType' => $targetAudienceLo,
                ],
            ],
            'gradeLevelClassification' => [
                'a' => GradeLevelClassification::Introduction->value,
                'd' => GradeLevelClassification::Continuous->value,
                's' => GradeLevelClassification::Mandatory->value,
                'u' => GradeLevelClassification::Optional->value,
                'v' => GradeLevelClassification::Repetition->value,
            ],
        ],
        // VVKBaO.
        EducationalNetwork::VVKBAO_UID => [
            'learningTrailConfigs' => [
                [
                    'name' => 'JK',
                    'naturalStudyYear' => null,
                    'targetAudienceType' => $targetAudienceJk,
                ],
                [
                    'name' => 'OK',
                    'naturalStudyYear' => null,
                    'targetAudienceType' => $targetAudienceOk,
                ],
                [
                    'name' => '1ste',
                    'naturalStudyYear' => 1,
                    'targetAudienceType' => $targetAudienceLo,
                ],
                [
                    'name' => '2de',
                    'naturalStudyYear' => 2,
                    'targetAudienceType' => $targetAudienceLo,
                ],
                [
                    'name' => '3de',
                    'naturalStudyYear' => 3,
                    'targetAudienceType' => $targetAudienceLo,
                ],
                [
                    'name' => '4de',
                    'naturalStudyYear' => 4,
                    'targetAudienceType' => $targetAudienceLo,
                ],
                [
                    'name' => '5de',
                    'naturalStudyYear' => 5,
                    'targetAudienceType' => $targetAudienceLo,
                ],
                [
                    'name' => '6de',
                    'naturalStudyYear' => 6,
                    'targetAudienceType' => $targetAudienceLo,
                ],
            ],
            'gradeLevelClassification' => [
                'STIPPEL' => GradeLevelClassification::Introduction->value,
                'GEEN_STIPPEL' => GradeLevelClassification::Introduction->value,
                'STIPPEL_VOL' => GradeLevelClassification::Optional->value,
                'VOL' => GradeLevelClassification::Mandatory->value,
                'KERN' => GradeLevelClassification::Mandatory->value,
                'DUBBEL' => GradeLevelClassification::Continuous->value,
                'RASTER' => GradeLevelClassification::Repetition->value,
                'UITBREIDING' => GradeLevelClassification::Optional->value,
            ],
        ],
    ],
    'externalUrls' => [
        'help' => 'https://www.wazzou.vanin.be/fr/besoin-aide',
        'contact' => 'https://www.wazzou.vanin.be/fr/contact',
        'enable-mfa' => config('cfa.url.helpdocs') .
            '/hoe-stel-ik-de-tweestapsverificatie-opnieuw-in-voor-het-gebruik-van-bingel-zorg/',
        'configure-mfa' => config('cfa.url.helpdocs') .
            '/hoe-stel-ik-de-tweestapsverificatie-opnieuw-in-voor-het-gebruik-van-bingel-zorg/',
        'reset-mfa' => config('cfa.url.helpdocs') .
            '/hoe-stel-ik-de-tweestapsverificatie-opnieuw-in-voor-het-gebruik-van-bingel-zorg/',
        'calculate-synthesis-totals' => config('cfa.url.helpdocs') .
            '/evaluatie-beheer/een-rapport-instellen-hoe#Syntheseberekeningen',
        'logout' => config('cfa.url.bingel') . '/login#planner',
        'profile' => config('cfa.url.bingel') . '/teacher/profile/edit#planner',
        'return' => config('cfa.url.bingel') . '/teacher#planner',
        'schools' => config('cfa.url.bingel') . '/teacher/profile/switch#planner',
        'settings' => config('cfa.url.bingel') . '/teacher/admin#planner',
        'user-settings' => config('cfa.url.bingel') . '/teacher/profile/user-settings#planner',
        'manage-pupil' => config('cfa.url.bingel') . '/teacher/admin/pupils#planner',
    ],
    'internalUrls' => [
        'apiPlannerPrefix' => config('cfa.api.planner.pathprefix'),
    ],
    'activitySettings' => [
        [
            'activity_type' => ActivityType::Administration->value,
            'is_active' => true,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => true,
        ],
        [
            'activity_type' => ActivityType::Absent->value,
            'is_active' => true,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => true,
        ],
        [
            'activity_type' => ActivityType::CareAndSupport->value,
            'is_active' => true,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => true,
        ],
        [
            'activity_type' => ActivityType::Evaluations->value,
            'is_active' => false,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => true,
        ],
        [
            'activity_type' => ActivityType::NoClass->value,
            'is_active' => false,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => true,
        ],
        [
            'activity_type' => ActivityType::OtherWithParticipants->value,
            'is_active' => true,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => true,
        ],
        [
            'activity_type' => ActivityType::OtherRelevantForPupil->value,
            'is_active' => true,
            'is_relevant_for_pupil' => true,
            'personal_calendar' => true,
        ],
        [
            'activity_type' => ActivityType::Deliberation->value,
            'is_active' => true,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => true,
        ],
        [
            'activity_type' => ActivityType::Break->value,
            'is_active' => false,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => true,
        ],
        [
            'activity_type' => ActivityType::SportAndCulture->value,
            'is_active' => true,
            'is_relevant_for_pupil' => true,
            'personal_calendar' => true,
        ],
        [
            'activity_type' => ActivityType::Internship->value,
            'is_active' => true,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => true,
        ],
        [
            'activity_type' => ActivityType::ThemeProject->value,
            'is_active' => true,
            'is_relevant_for_pupil' => true,
            'personal_calendar' => true,
        ],
        [
            'activity_type' => ActivityType::Surveillance->value,
            'is_active' => true,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => true,
        ],
        [
            'activity_type' => ActivityType::Trip->value,
            'is_active' => true,
            'is_relevant_for_pupil' => true,
            'personal_calendar' => true,
        ],
        [
            'activity_type' => ActivityType::Training->value,
            'is_active' => true,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => true,
        ],
        [
            'activity_type' => ActivityType::Administration->value,
            'is_active' => true,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => false,
        ],
        [
            'activity_type' => ActivityType::Absent->value,
            'is_active' => true,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => false,
        ],
        [
            'activity_type' => ActivityType::CareAndSupport->value,
            'is_active' => false,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => false,
        ],
        [
            'activity_type' => ActivityType::Evaluations->value,
            'is_active' => true,
            'is_relevant_for_pupil' => true,
            'personal_calendar' => false,
        ],
        [
            'activity_type' => ActivityType::NoClass->value,
            'is_active' => true,
            'is_relevant_for_pupil' => true,
            'personal_calendar' => false,
        ],
        [
            'activity_type' => ActivityType::OtherWithParticipants->value,
            'is_active' => true,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => false,
        ],
        [
            'activity_type' => ActivityType::OtherRelevantForPupil->value,
            'is_active' => true,
            'is_relevant_for_pupil' => true,
            'personal_calendar' => false,
        ],
        [
            'activity_type' => ActivityType::Deliberation->value,
            'is_active' => false,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => false,
        ],
        [
            'activity_type' => ActivityType::Break->value,
            'is_active' => true,
            'is_relevant_for_pupil' => true,
            'personal_calendar' => false,
        ],
        [
            'activity_type' => ActivityType::SportAndCulture->value,
            'is_active' => true,
            'is_relevant_for_pupil' => true,
            'personal_calendar' => false,
        ],
        [
            'activity_type' => ActivityType::Internship->value,
            'is_active' => true,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => false,
        ],
        [
            'activity_type' => ActivityType::ThemeProject->value,
            'is_active' => true,
            'is_relevant_for_pupil' => true,
            'personal_calendar' => false,
        ],
        [
            'activity_type' => ActivityType::Surveillance->value,
            'is_active' => true,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => false,
        ],
        [
            'activity_type' => ActivityType::Trip->value,
            'is_active' => true,
            'is_relevant_for_pupil' => true,
            'personal_calendar' => false,
        ],
        [
            'activity_type' => ActivityType::Training->value,
            'is_active' => true,
            'is_relevant_for_pupil' => false,
            'personal_calendar' => false,
        ],
    ],
    'appSwitcherConfig' => [
        [
            'app' => 'home',
            'icon' => '$mdiHome',
            'href' => config('cfa.url.bingel') . '/teacher/dashboard#planner',
        ],
        [
            'app' => 'methods',
            'icon' => 'sol:sol-bingel-collections',
            'href' => config('cfa.url.bingel') . '/teacher/my-methods#planner',
        ],
        [
            'app' => 'island',
            'icon' => 'sol:sol-bingel-island',
            'href' => config('cfa.url.bingel') . '/teacher/bingel-island#planner',
        ],
        [
            'app' => Subdomain::PLANNER,
            'icon' => 'today',
            'permission' => PermissionName::HasAccessToPlanner->value,
        ],
        [
            'app' => 'bingel-settings',
            'icon' => '$mdiWrench',
            'href' => '/settings',
            'gate' => GateName::CanAccessCareAndSettings->value,
        ],
    ],
    'chapters' => [
        CollectionTemplate::Default->name => [
            'Chapitre 1',
        ],
        CollectionTemplate::Activity->name => [
            'Collection des activités',
        ],
        CollectionTemplate::Theme->name => [
            'Activités',
            'Coin',
        ],
        CollectionTemplate::Corner->name => [
            'Coin 1',
        ],
    ],
];
