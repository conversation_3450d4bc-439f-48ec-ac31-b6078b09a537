<?php

use Carbon\Carbon;

return [
    /*
    |--------------------------------------------------------------------------
    | Horizon Redis Connection
    |--------------------------------------------------------------------------
    |
    | This is the name of the Redis connection where Horizon will store the
    | meta information required for it to function. It includes the list
    | of supervisors, failed jobs, job metrics, and other information.
    |
    */

    'use' => 'queue',

    /*
    |--------------------------------------------------------------------------
    | Horizon Redis Prefix
    |--------------------------------------------------------------------------
    |
    | This prefix will be used when storing all Horizon data in Redis. You
    | may modify the prefix when you are running multiple installations
    | of Horizon on the same server so that they don't have problems.
    |
    */

    'prefix' => env('HORIZON_PREFIX', 'horizon:'),

    /*
    |--------------------------------------------------------------------------
    | Queue Wait Time Thresholds
    |--------------------------------------------------------------------------
    |
    | This option allows you to configure when the LongWaitDetected event
    | will be fired. Every connection / queue combination may have its
    | own, unique threshold (in seconds) before this event is fired.
    |
    */

    // Reporting-filler only gets spun up on demand, so there we generally want no warnings at all.
    'waits' => [
        'redis:default' => 300,
        'redis:medium-priority' => 900,
        'redis:low-priority' => 1200,
        'redis:smd' => 900,
        'redis:reporting' => 900,
        'redis:reporting-filler' => 86400,
    ],

    /*
    |--------------------------------------------------------------------------
    | Job Trimming Times
    |--------------------------------------------------------------------------
    |
    | Here you can configure for how long (in minutes) you desire Horizon to
    | persist the recent and failed jobs. Typically, recent jobs are kept
    | for one hour while all failed jobs are stored for an entire week.
    |
    */

    'trim' => [
        'recent' => Carbon::MINUTES_PER_HOUR * Carbon::HOURS_PER_DAY,
        'failed' => 10080,
    ],

    /*
    |--------------------------------------------------------------------------
    | Queue Worker Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may define the queue worker settings used by your application
    | in all environments. These supervisors and settings handle all your
    | queued jobs and will be provisioned by Horizon during deployment.
    |
    */

    'defaults' => [
        'supervisor' => [
            'connection' => env('HORIZON_CONNECTION', 'redis'),
            'queue' => env('HORIZON_QUEUE') ? explode(',', env('HORIZON_QUEUE')) : null,
            'tries' => env('HORIZON_TRIES', 1),
            'memory' => env('HORIZON_MEMORY', 512),
            'timeout' => env('HORIZON_TIMEOUT', 1803),
            'maxTime' => env('HORIZON_MAX_TIME', 900),
            'force' => env('HORIZON_FORCE', true),
        ],
    ],

    'environments' => [
        env('APP_ENV') => ['supervisor' => []],
    ],

    'slack' => [
        'url' => env('SLACK_WEBHOOK_URL'),
        'channel' => env('HORIZON_SLACK_CHANNEL'),
    ],

    // Routes to identify horizon requests.
    'routes' => ['horizon', 'horizon/*'],
];
