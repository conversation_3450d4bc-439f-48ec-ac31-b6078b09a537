<?php

use Cfa\Common\Domain\Tenant\TenantId;
use Cfa\Evaluation\Domain\QuotationSystem\QuotationSystemType;

$colors = [
    'black' => 'black',
    'red' => 'red-darken-4',
    'orange' => 'orange-darken-2',
    'darkgreen' => 'teal-darken-2',
    'lightgreen' => 'lime',
    'green' => 'light-green',
];

$text = [
    TenantId::SolFl->value => [
        'Excellent' => 'Uitstekend',
        'Good' => 'Goed',
        'Adequate' => 'Voldoende',
        'Inadequate' => 'Onvoldoende',
        'Super' => 'Super',
        'Varying' => 'Wisselend',
        'Problematic' => 'Problematisch',
    ],
];

$iconPerScore = QuotationSystemType::IconPerScore;
$scaled = QuotationSystemType::Scaled;

return [
    TenantId::SolFl->value => [
        'Smileys 4 niveaus' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Excellent'],
                    'color' => $colors['darkgreen'],
                    'icon' => 'fas fa-laugh',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Good'],
                    'color' => $colors['lightgreen'],
                    'icon' => 'fas fa-smile',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Adequate'],
                    'color' => $colors['orange'],
                    'icon' => 'fas fa-meh',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Inadequate'],
                    'color' => $colors['red'],
                    'icon' => 'fas fa-frown',
                    'iconCount' => 1,
                ],
            ],
            'type' => $iconPerScore,
        ],
        'Smileys 3 niveaus' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Super'],
                    'color' => $colors['darkgreen'],
                    'icon' => 'fas fa-smile',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Varying'],
                    'color' => $colors['orange'],
                    'icon' => 'fas fa-meh',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Problematic'],
                    'color' => $colors['red'],
                    'icon' => 'fas fa-frown',
                    'iconCount' => 1,
                ],
            ],
            'type' => $iconPerScore,
        ],
        'Sterren 4 niveaus' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Excellent'],
                    'color' => $colors['darkgreen'],
                    'icon' => 'fas fa-star',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Good'],
                    'color' => $colors['lightgreen'],
                    'icon' => 'fas fa-star',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Adequate'],
                    'color' => $colors['orange'],
                    'icon' => 'fas fa-star',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Inadequate'],
                    'color' => $colors['red'],
                    'icon' => 'fas fa-star',
                    'iconCount' => 1,
                ],
            ],
            'type' => $iconPerScore,
        ],
        'Sterren 3 niveaus' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Super'],
                    'color' => $colors['darkgreen'],
                    'icon' => 'fas fa-star',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Varying'],
                    'color' => $colors['orange'],
                    'icon' => 'fas fa-star',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Problematic'],
                    'color' => $colors['red'],
                    'icon' => 'fas fa-star',
                    'iconCount' => 1,
                ],
            ],
            'type' => $iconPerScore,
        ],
        'Sterren 4 niveaus eenkleurig' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Excellent'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-star',
                    'iconCount' => 4,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Good'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-star',
                    'iconCount' => 3,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Adequate'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-star',
                    'iconCount' => 2,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Inadequate'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-star',
                    'iconCount' => 1,
                ],
            ],
            'type' => $scaled,
        ],
        'Sterren 3 niveaus eenkleurig' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Super'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-star',
                    'iconCount' => 3,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Varying'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-star',
                    'iconCount' => 2,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Problematic'],
                    'color' => $colors['red'],
                    'icon' => 'fas fa-star',
                    'iconCount' => 1,
                ],
            ],
            'type' => $scaled,
        ],
        'Plus/Min' => [
            'quotations' => [
                [
                    'label' => '++',
                    'color' => $colors['darkgreen'],
                    'icon' => 'fas fa-plus',
                    'iconCount' => 2,
                ],
                [
                    'label' => '+',
                    'color' => $colors['green'],
                    'icon' => 'fas fa-plus',
                    'iconCount' => 1,
                ],
                [
                    'label' => '-',
                    'color' => $colors['orange'],
                    'icon' => 'fas fa-minus',
                    'iconCount' => 1,
                ],
            ],
            'type' => $scaled,
        ],
        'Vinkjes 4 niveaus' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Excellent'],
                    'color' => $colors['darkgreen'],
                    'icon' => 'fas fa-check',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Good'],
                    'color' => $colors['green'],
                    'icon' => 'fas fa-check',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Adequate'],
                    'color' => $colors['orange'],
                    'icon' => 'fas fa-check',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Inadequate'],
                    'color' => $colors['red'],
                    'icon' => 'fas fa-check',
                    'iconCount' => 1,
                ],
            ],
            'type' => $iconPerScore,
        ],
        'Vinkjes 3 niveaus' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Super'],
                    'color' => $colors['darkgreen'],
                    'icon' => 'fas fa-check',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Varying'],
                    'color' => $colors['orange'],
                    'icon' => 'fas fa-check',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Problematic'],
                    'color' => $colors['red'],
                    'icon' => 'fas fa-check',
                    'iconCount' => 1,
                ],
            ],
            'type' => $iconPerScore,
        ],
        'Vinkjes 4 niveaus eenkleurig' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Excellent'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-check',
                    'iconCount' => 4,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Good'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-check',
                    'iconCount' => 3,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Adequate'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-check',
                    'iconCount' => 2,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Inadequate'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-check',
                    'iconCount' => 1,
                ],
            ],
            'type' => $scaled,
        ],
        'Vinkjes 3 niveaus eenkleurig' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Super'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-check',
                    'iconCount' => 3,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Varying'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-check',
                    'iconCount' => 2,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Problematic'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-check',
                    'iconCount' => 1,
                ],
            ],
            'type' => $scaled,
        ],
        'Handen 4 niveaus' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Excellent'],
                    'color' => $colors['darkgreen'],
                    'icon' => 'fas fa-hand-spock',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Good'],
                    'color' => $colors['green'],
                    'icon' => 'fas fa-hand-spock',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Adequate'],
                    'color' => $colors['orange'],
                    'icon' => 'fas fa-hand-spock',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Inadequate'],
                    'color' => $colors['red'],
                    'icon' => 'fas fa-hand-spock',
                    'iconCount' => 1,
                ],
            ],
            'type' => $iconPerScore,
        ],
        'Handen 3 niveaus' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Super'],
                    'color' => $colors['darkgreen'],
                    'icon' => 'fas fa-hand-spock',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Varying'],
                    'color' => $colors['orange'],
                    'icon' => 'fas fa-hand-spock',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Problematic'],
                    'color' => $colors['red'],
                    'icon' => 'fas fa-hand-spock',
                    'iconCount' => 1,
                ],
            ],
            'type' => $iconPerScore,
        ],
        'Handen 4 niveaus eenkleurig' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Excellent'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-hand-spock',
                    'iconCount' => 4,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Good'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-hand-spock',
                    'iconCount' => 3,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Adequate'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-hand-spock',
                    'iconCount' => 2,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Inadequate'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-hand-spock',
                    'iconCount' => 1,
                ],
            ],
            'type' => $scaled,
        ],
        'Handen 3 niveaus eenkleurig' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Super'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-hand-spock',
                    'iconCount' => 3,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Varying'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-hand-spock',
                    'iconCount' => 2,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Problematic'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-hand-spock',
                    'iconCount' => 1,
                ],
            ],
            'type' => $scaled,
        ],
        'Pluim/Duim' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Super'],
                    'color' => $colors['darkgreen'],
                    'icon' => 'fas fa-feather',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Varying'],
                    'color' => $colors['orange'],
                    'icon' => 'fas fa-thumbs-up',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Problematic'],
                    'color' => $colors['red'],
                    'icon' => 'fas fa-exclamation-circle',
                    'iconCount' => 1,
                ],
            ],
            'type' => $iconPerScore,
        ],
        'Veren 4 niveaus' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Excellent'],
                    'color' => $colors['darkgreen'],
                    'icon' => 'fal fa-feather-alt',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Good'],
                    'color' => $colors['green'],
                    'icon' => 'fal fa-feather-alt',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Adequate'],
                    'color' => $colors['orange'],
                    'icon' => 'fal fa-feather-alt',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Inadequate'],
                    'color' => $colors['red'],
                    'icon' => 'fal fa-feather-alt',
                    'iconCount' => 1,
                ],
            ],
            'type' => $iconPerScore,
        ],
        'Veren 3 niveaus' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Super'],
                    'color' => $colors['darkgreen'],
                    'icon' => 'fal fa-feather-alt',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Varying'],
                    'color' => $colors['orange'],
                    'icon' => 'fal fa-feather-alt',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Problematic'],
                    'color' => $colors['red'],
                    'icon' => 'fal fa-feather-alt',
                    'iconCount' => 1,
                ],
            ],
            'type' => $iconPerScore,
        ],
        'Veren 4 niveaus eenkleurig' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Excellent'],
                    'color' => $colors['black'],
                    'icon' => 'fal fa-feather-alt',
                    'iconCount' => 4,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Good'],
                    'color' => $colors['black'],
                    'icon' => 'fal fa-feather-alt',
                    'iconCount' => 3,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Adequate'],
                    'color' => $colors['black'],
                    'icon' => 'fal fa-feather-alt',
                    'iconCount' => 2,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Inadequate'],
                    'color' => $colors['black'],
                    'icon' => 'fal fa-feather-alt',
                    'iconCount' => 1,
                ],
            ],
            'type' => $scaled,
        ],
        'Veren 3 niveaus eenkleurig' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Super'],
                    'color' => $colors['black'],
                    'icon' => 'fal fa-feather-alt',
                    'iconCount' => 3,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Varying'],
                    'color' => $colors['black'],
                    'icon' => 'fal fa-feather-alt',
                    'iconCount' => 2,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Problematic'],
                    'color' => $colors['black'],
                    'icon' => 'fal fa-feather-alt',
                    'iconCount' => 1,
                ],
            ],
            'type' => $scaled,
        ],
        'Kronen 4 niveaus' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Excellent'],
                    'color' => $colors['darkgreen'],
                    'icon' => 'fas fa-crown',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Good'],
                    'color' => $colors['green'],
                    'icon' => 'fas fa-crown',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Adequate'],
                    'color' => $colors['orange'],
                    'icon' => 'fas fa-crown',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Inadequate'],
                    'color' => $colors['red'],
                    'icon' => 'fas fa-crown',
                    'iconCount' => 1,
                ],
            ],
            'type' => $iconPerScore,
        ],
        'Kronen 3 niveaus' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Super'],
                    'color' => $colors['darkgreen'],
                    'icon' => 'fas fa-crown',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Varying'],
                    'color' => $colors['orange'],
                    'icon' => 'fas fa-crown',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Problematic'],
                    'color' => $colors['red'],
                    'icon' => 'fas fa-crown',
                    'iconCount' => 1,
                ],
            ],
            'type' => $iconPerScore,
        ],
        'Kronen 4 niveaus eenkleurig' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Excellent'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-crown',
                    'iconCount' => 4,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Good'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-crown',
                    'iconCount' => 3,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Adequate'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-crown',
                    'iconCount' => 2,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Inadequate'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-crown',
                    'iconCount' => 1,
                ],
            ],
            'type' => $scaled,
        ],
        'Kronen 3 niveaus eenkleurig' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Super'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-crown',
                    'iconCount' => 3,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Varying'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-crown',
                    'iconCount' => 2,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Problematic'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-crown',
                    'iconCount' => 1,
                ],
            ],
            'type' => $scaled,
        ],
        'Groeizaad 4 niveaus eenkleurig' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Excellent'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-hand-holding-seedling',
                    'iconCount' => 4,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Good'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-hand-holding-seedling',
                    'iconCount' => 3,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Adequate'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-hand-holding-seedling',
                    'iconCount' => 2,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Inadequate'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-hand-holding-seedling',
                    'iconCount' => 1,
                ],
            ],
            'type' => $scaled,
        ],
        'Groeizaad 3 niveaus eenkleurig' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Super'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-hand-holding-seedling',
                    'iconCount' => 3,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Varying'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-hand-holding-seedling',
                    'iconCount' => 2,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Problematic'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-hand-holding-seedling',
                    'iconCount' => 1,
                ],
            ],
            'type' => $scaled,
        ],
        'Duimpjes 4 niveaus' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Excellent'],
                    'color' => $colors['darkgreen'],
                    'icon' => 'fas fa-thumbs-up',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Good'],
                    'color' => $colors['green'],
                    'icon' => 'fas fa-thumbs-up',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Adequate'],
                    'color' => $colors['orange'],
                    'icon' => 'fas fa-thumbs-up',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Inadequate'],
                    'color' => $colors['red'],
                    'icon' => 'fas fa-thumbs-up',
                    'iconCount' => 1,
                ],
            ],
            'type' => $iconPerScore,
        ],
        'Duimpjes 3 niveaus' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Super'],
                    'color' => $colors['darkgreen'],
                    'icon' => 'fas fa-thumbs-up',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Varying'],
                    'color' => $colors['orange'],
                    'icon' => 'fas fa-thumbs-up',
                    'iconCount' => 1,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Problematic'],
                    'color' => $colors['red'],
                    'icon' => 'fas fa-thumbs-up',
                    'iconCount' => 1,
                ],
            ],
            'type' => $iconPerScore,
        ],
        'Duimpjes 4 niveaus eenkleurig' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Excellent'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-thumbs-up',
                    'iconCount' => 4,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Good'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-thumbs-up',
                    'iconCount' => 3,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Adequate'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-thumbs-up',
                    'iconCount' => 2,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Inadequate'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-thumbs-up',
                    'iconCount' => 1,
                ],
            ],
            'type' => $scaled,
        ],
        'Duimpjes 3 niveaus eenkleurig' => [
            'quotations' => [
                [
                    'label' => $text[TenantId::SolFl->value]['Super'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-thumbs-up',
                    'iconCount' => 3,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Varying'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-thumbs-up',
                    'iconCount' => 2,
                ],
                [
                    'label' => $text[TenantId::SolFl->value]['Problematic'],
                    'color' => $colors['black'],
                    'icon' => 'fas fa-thumbs-up',
                    'iconCount' => 1,
                ],
            ],
            'type' => $scaled,
        ],
    ],
    TenantId::SolWl->value => [],
];
