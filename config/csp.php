<?php

use Spatie\Csp\Nonce\RandomString;

return [
    /*
     * A policy will determine which CSP headers will be set. A valid CSP policy is
     * any class that extends `<PERSON>tie\Csp\Policies\Policy`
     */

    'policy' => 'App\Infrastructure\Csp\Policies\\' . env('CSP_PROFILE', 'Local') . 'Policy',

    /*
     * This policy which will be put in report only mode. This is great for testing out
     * a new policy or changes to existing csp policy without breaking anyting.
     */

    'report_only_policy' => '',

    /*
     * All violations against the policy will be reported to this url.
     * A great service you could use for this is https://report-uri.com/
     *
     * You can override this setting by calling `reportTo` on your policy.
     */

    'report_uri' => env('CSP_REPORT_URI', ''),

    /*
     * Headers will only be added if this setting is set to true.
     */

    'enabled' => env('CSP_ENABLED', true),

    /*
     * The class responsible for generating the nonces used in inline tags and headers.
     */

    'nonce_generator' => RandomString::class,
];
