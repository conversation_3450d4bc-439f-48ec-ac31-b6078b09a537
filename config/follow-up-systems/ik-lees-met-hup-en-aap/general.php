<?php

use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;
use Cfa\Evaluation\Domain\FollowUpSystem\Zone\LvsZone;

return [
    // Target audiences the follow up system should be available in.
    'targetAudiences' => [
        TargetAudienceType::Lo->value => [
            1,
        ],
    ],
    // Possible audiences coming from Bingel. Ik lees met hup en aap is only used in LO-J1.
    // These are mapped to TMS test audiences in IkLeesMetHupEnAapCmsReference::getTestAudience
    // The translations are available in enums.php.
    'testAudiences' => [
        TestAudience::LoJ1->value,
    ],
    // Possible test moments coming from Bingel, e.g. ILMHEA_cntrldictee_1 and ILMHEA_cntrldictee_2
    // These are mapped to TMS test moments in IkLeesMetHupEnAapCmsReference::getTestMoment
    // The translations are available in enums.php.
    'testMoments' => [
        TestMoment::DictationRevision1->value,
        TestMoment::DictationRevision2->value,
    ],
    // Possible zones the scores coming from Bingel can map to, you can find these in the provided Excel document.
    // The translations are available in enums.php.
    'zones' => [
        LvsZone::A->value,
        LvsZone::B->value,
        LvsZone::C->value,
        LvsZone::D->value,
        LvsZone::E->value,
    ],
    // Which translation key should be used for the translation of zones.
    'zoneName' => 'lvs-zone',
];
