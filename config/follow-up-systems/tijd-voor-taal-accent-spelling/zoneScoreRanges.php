<?php

use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;
use Cfa\Evaluation\Domain\FollowUpSystem\Zone\LvsZone;

// Generated from the Excel provided by the author team.
// Summarizes the percentile and zone config files by providing the ranges of scores that map to a zone.
// The first level contains every subtype.
// The second level contains every test audience.
// The third level contains every test moment.
// ZoneScoreRangeConfigTest tests if this config is correct based on the other config files.

return [
    FollowUpSystemSubType::Default->value => [
        TestAudience::LoJ1->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [40, 40],
                LvsZone::B->value => [38, 39],
                LvsZone::C->value => [36, 37],
                LvsZone::D->value => [32, 35],
                LvsZone::E->value => [0, 31],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [40, 40],
                LvsZone::B->value => [39, 39],
                LvsZone::C->value => [35, 38],
                LvsZone::D->value => [32, 34],
                LvsZone::E->value => [0, 31],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [40, 40],
                LvsZone::B->value => [39, 39],
                LvsZone::C->value => [36, 38],
                LvsZone::D->value => [32, 35],
                LvsZone::E->value => [0, 31],
            ],
        ],
        TestAudience::LoJ2->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [49, 50],
                LvsZone::B->value => [47, 48],
                LvsZone::C->value => [43, 46],
                LvsZone::D->value => [41, 42],
                LvsZone::E->value => [0, 40],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [48, 50],
                LvsZone::B->value => [46, 47],
                LvsZone::C->value => [42, 45],
                LvsZone::D->value => [39, 41],
                LvsZone::E->value => [0, 38],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [47, 50],
                LvsZone::B->value => [44, 46],
                LvsZone::C->value => [39, 43],
                LvsZone::D->value => [35, 38],
                LvsZone::E->value => [0, 34],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [47, 50],
                LvsZone::B->value => [43, 46],
                LvsZone::C->value => [37, 42],
                LvsZone::D->value => [33, 36],
                LvsZone::E->value => [0, 32],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [47, 50],
                LvsZone::B->value => [44, 46],
                LvsZone::C->value => [39, 43],
                LvsZone::D->value => [35, 38],
                LvsZone::E->value => [0, 34],
            ],
        ],
        TestAudience::LoJ3->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [38, 40],
                LvsZone::B->value => [35, 37],
                LvsZone::C->value => [30, 34],
                LvsZone::D->value => [27, 29],
                LvsZone::E->value => [0, 26],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [45, 50],
                LvsZone::B->value => [40, 44],
                LvsZone::C->value => [35, 39],
                LvsZone::D->value => [32, 34],
                LvsZone::E->value => [0, 31],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [38, 40],
                LvsZone::B->value => [35, 37],
                LvsZone::C->value => [31, 34],
                LvsZone::D->value => [28, 30],
                LvsZone::E->value => [0, 27],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [48, 50],
                LvsZone::B->value => [44, 47],
                LvsZone::C->value => [39, 43],
                LvsZone::D->value => [36, 38],
                LvsZone::E->value => [0, 35],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [46, 50],
                LvsZone::B->value => [42, 45],
                LvsZone::C->value => [36, 41],
                LvsZone::D->value => [33, 35],
                LvsZone::E->value => [0, 32],
            ],
        ],
        TestAudience::LoJ4->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [57, 60],
                LvsZone::B->value => [52, 56],
                LvsZone::C->value => [47, 51],
                LvsZone::D->value => [42, 46],
                LvsZone::E->value => [0, 41],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [54, 60],
                LvsZone::B->value => [50, 53],
                LvsZone::C->value => [46, 49],
                LvsZone::D->value => [42, 45],
                LvsZone::E->value => [0, 41],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [56, 60],
                LvsZone::B->value => [52, 55],
                LvsZone::C->value => [46, 51],
                LvsZone::D->value => [41, 45],
                LvsZone::E->value => [0, 40],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [54, 60],
                LvsZone::B->value => [50, 53],
                LvsZone::C->value => [44, 49],
                LvsZone::D->value => [40, 43],
                LvsZone::E->value => [0, 39],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [55, 60],
                LvsZone::B->value => [52, 54],
                LvsZone::C->value => [46, 51],
                LvsZone::D->value => [41, 45],
                LvsZone::E->value => [0, 40],
            ],
        ],
        TestAudience::LoJ5->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [54, 60],
                LvsZone::B->value => [50, 53],
                LvsZone::C->value => [43, 49],
                LvsZone::D->value => [38, 42],
                LvsZone::E->value => [0, 37],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [55, 60],
                LvsZone::B->value => [51, 54],
                LvsZone::C->value => [46, 50],
                LvsZone::D->value => [42, 45],
                LvsZone::E->value => [0, 41],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [50, 60],
                LvsZone::B->value => [46, 49],
                LvsZone::C->value => [38, 45],
                LvsZone::D->value => [34, 37],
                LvsZone::E->value => [0, 33],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [52, 60],
                LvsZone::B->value => [47, 51],
                LvsZone::C->value => [41, 46],
                LvsZone::D->value => [36, 40],
                LvsZone::E->value => [0, 35],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [52, 60],
                LvsZone::B->value => [47, 51],
                LvsZone::C->value => [40, 46],
                LvsZone::D->value => [35, 39],
                LvsZone::E->value => [0, 34],
            ],
        ],
        TestAudience::LoJ6->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [55, 60],
                LvsZone::B->value => [51, 54],
                LvsZone::C->value => [45, 50],
                LvsZone::D->value => [40, 44],
                LvsZone::E->value => [0, 39],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [54, 60],
                LvsZone::B->value => [51, 53],
                LvsZone::C->value => [46, 50],
                LvsZone::D->value => [43, 45],
                LvsZone::E->value => [0, 42],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [55, 60],
                LvsZone::B->value => [51, 54],
                LvsZone::C->value => [45, 50],
                LvsZone::D->value => [41, 44],
                LvsZone::E->value => [0, 40],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [53, 60],
                LvsZone::B->value => [48, 52],
                LvsZone::C->value => [42, 47],
                LvsZone::D->value => [38, 41],
                LvsZone::E->value => [0, 37],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [52, 60],
                LvsZone::B->value => [47, 51],
                LvsZone::C->value => [41, 46],
                LvsZone::D->value => [36, 40],
                LvsZone::E->value => [0, 35],
            ],
        ],
    ],
    FollowUpSystemSubType::SeparateWords->value => [
        TestAudience::LoJ1->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::C->value => [10, 10],
                LvsZone::D->value => [9, 9],
                LvsZone::E->value => [0, 8],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::C->value => [9, 10],
                LvsZone::E->value => [0, 8],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::B->value => [10, 10],
                LvsZone::C->value => [9, 9],
                LvsZone::E->value => [0, 8],
            ],
        ],
        TestAudience::LoJ2->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [30, 30],
                LvsZone::B->value => [29, 29],
                LvsZone::C->value => [26, 28],
                LvsZone::D->value => [24, 25],
                LvsZone::E->value => [0, 23],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [20, 20],
                LvsZone::B->value => [19, 19],
                LvsZone::C->value => [17, 18],
                LvsZone::D->value => [15, 16],
                LvsZone::E->value => [0, 14],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [29, 30],
                LvsZone::B->value => [27, 28],
                LvsZone::C->value => [23, 26],
                LvsZone::D->value => [21, 22],
                LvsZone::E->value => [0, 20],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [20, 20],
                LvsZone::B->value => [18, 19],
                LvsZone::C->value => [15, 17],
                LvsZone::D->value => [12, 14],
                LvsZone::E->value => [0, 11],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [19, 20],
                LvsZone::B->value => [17, 18],
                LvsZone::C->value => [15, 16],
                LvsZone::D->value => [13, 14],
                LvsZone::E->value => [0, 12],
            ],
        ],
        TestAudience::LoJ3->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::B->value => [10, 10],
                LvsZone::C->value => [8, 9],
                LvsZone::D->value => [7, 7],
                LvsZone::E->value => [0, 6],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [18, 20],
                LvsZone::B->value => [16, 17],
                LvsZone::C->value => [13, 15],
                LvsZone::D->value => [11, 12],
                LvsZone::E->value => [0, 10],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::B->value => [10, 10],
                LvsZone::C->value => [9, 9],
                LvsZone::D->value => [8, 8],
                LvsZone::E->value => [0, 7],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [20, 20],
                LvsZone::B->value => [19, 19],
                LvsZone::C->value => [16, 18],
                LvsZone::D->value => [14, 15],
                LvsZone::E->value => [0, 13],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [19, 20],
                LvsZone::B->value => [17, 18],
                LvsZone::C->value => [15, 16],
                LvsZone::D->value => [13, 14],
                LvsZone::E->value => [0, 12],
            ],
        ],
        TestAudience::LoJ4->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [15, 15],
                LvsZone::B->value => [14, 14],
                LvsZone::C->value => [12, 13],
                LvsZone::D->value => [11, 11],
                LvsZone::E->value => [0, 10],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [15, 15],
                LvsZone::B->value => [14, 14],
                LvsZone::C->value => [12, 13],
                LvsZone::D->value => [11, 11],
                LvsZone::E->value => [0, 10],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::B->value => [14, 15],
                LvsZone::C->value => [12, 13],
                LvsZone::D->value => [11, 11],
                LvsZone::E->value => [0, 10],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [15, 15],
                LvsZone::B->value => [14, 14],
                LvsZone::C->value => [12, 13],
                LvsZone::D->value => [10, 11],
                LvsZone::E->value => [0, 9],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [15, 15],
                LvsZone::B->value => [14, 14],
                LvsZone::C->value => [12, 13],
                LvsZone::D->value => [11, 11],
                LvsZone::E->value => [0, 10],
            ],
        ],
        TestAudience::LoJ5->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [15, 15],
                LvsZone::B->value => [14, 14],
                LvsZone::C->value => [13, 13],
                LvsZone::D->value => [12, 12],
                LvsZone::E->value => [0, 11],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [15, 15],
                LvsZone::B->value => [14, 14],
                LvsZone::C->value => [13, 13],
                LvsZone::D->value => [11, 12],
                LvsZone::E->value => [0, 10],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [14, 15],
                LvsZone::B->value => [13, 13],
                LvsZone::C->value => [11, 12],
                LvsZone::D->value => [10, 10],
                LvsZone::E->value => [0, 9],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [15, 15],
                LvsZone::B->value => [14, 14],
                LvsZone::C->value => [12, 13],
                LvsZone::D->value => [10, 11],
                LvsZone::E->value => [0, 9],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [14, 15],
                LvsZone::B->value => [13, 13],
                LvsZone::C->value => [11, 12],
                LvsZone::D->value => [10, 10],
                LvsZone::E->value => [0, 9],
            ],
        ],
        TestAudience::LoJ6->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [14, 15],
                LvsZone::B->value => [12, 13],
                LvsZone::C->value => [10, 11],
                LvsZone::D->value => [9, 9],
                LvsZone::E->value => [0, 8],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [15, 15],
                LvsZone::B->value => [14, 14],
                LvsZone::C->value => [12, 13],
                LvsZone::D->value => [11, 11],
                LvsZone::E->value => [0, 10],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [15, 15],
                LvsZone::B->value => [14, 14],
                LvsZone::C->value => [13, 13],
                LvsZone::D->value => [11, 12],
                LvsZone::E->value => [0, 10],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [15, 15],
                LvsZone::B->value => [14, 14],
                LvsZone::C->value => [12, 13],
                LvsZone::D->value => [11, 11],
                LvsZone::E->value => [0, 10],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [15, 15],
                LvsZone::B->value => [13, 14],
                LvsZone::C->value => [11, 12],
                LvsZone::D->value => [10, 10],
                LvsZone::E->value => [0, 9],
            ],
        ],
    ],
    FollowUpSystemSubType::WordsInSentence->value => [
        TestAudience::LoJ1->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::C->value => [9, 10],
                LvsZone::D->value => [8, 8],
                LvsZone::E->value => [0, 7],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::B->value => [10, 10],
                LvsZone::C->value => [9, 9],
                LvsZone::E->value => [0, 8],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::B->value => [10, 10],
                LvsZone::C->value => [9, 9],
                LvsZone::E->value => [0, 8],
            ],
        ],
        TestAudience::LoJ2->value => [
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [10, 10],
                LvsZone::B->value => [9, 9],
                LvsZone::C->value => [8, 8],
                LvsZone::D->value => [7, 7],
                LvsZone::E->value => [0, 6],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [10, 10],
                LvsZone::B->value => [9, 9],
                LvsZone::C->value => [7, 8],
                LvsZone::D->value => [6, 6],
                LvsZone::E->value => [0, 5],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::B->value => [10, 10],
                LvsZone::C->value => [9, 9],
                LvsZone::D->value => [8, 8],
                LvsZone::E->value => [0, 7],
            ],
        ],
        TestAudience::LoJ3->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [10, 10],
                LvsZone::C->value => [8, 9],
                LvsZone::D->value => [7, 7],
                LvsZone::E->value => [0, 6],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [10, 10],
                LvsZone::B->value => [9, 9],
                LvsZone::C->value => [8, 8],
                LvsZone::D->value => [7, 7],
                LvsZone::E->value => [0, 6],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [10, 10],
                LvsZone::C->value => [8, 9],
                LvsZone::D->value => [7, 7],
                LvsZone::E->value => [0, 6],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [10, 10],
                LvsZone::B->value => [9, 9],
                LvsZone::C->value => [8, 8],
                LvsZone::D->value => [7, 7],
                LvsZone::E->value => [0, 6],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [10, 10],
                LvsZone::B->value => [9, 9],
                LvsZone::C->value => [7, 8],
                LvsZone::D->value => [6, 6],
                LvsZone::E->value => [0, 5],
            ],
        ],
        TestAudience::LoJ4->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [15, 15],
                LvsZone::B->value => [14, 14],
                LvsZone::C->value => [13, 13],
                LvsZone::D->value => [11, 12],
                LvsZone::E->value => [0, 10],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [14, 15],
                LvsZone::B->value => [12, 13],
                LvsZone::C->value => [10, 11],
                LvsZone::D->value => [9, 9],
                LvsZone::E->value => [0, 8],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [15, 15],
                LvsZone::B->value => [13, 14],
                LvsZone::C->value => [11, 12],
                LvsZone::D->value => [10, 10],
                LvsZone::E->value => [0, 9],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [13, 15],
                LvsZone::B->value => [11, 12],
                LvsZone::C->value => [9, 10],
                LvsZone::D->value => [8, 8],
                LvsZone::E->value => [0, 7],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [14, 15],
                LvsZone::B->value => [13, 13],
                LvsZone::C->value => [12, 12],
                LvsZone::D->value => [11, 11],
                LvsZone::E->value => [0, 10],
            ],
        ],
        TestAudience::LoJ5->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [14, 15],
                LvsZone::B->value => [13, 13],
                LvsZone::C->value => [11, 12],
                LvsZone::D->value => [9, 10],
                LvsZone::E->value => [0, 8],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [14, 15],
                LvsZone::B->value => [13, 13],
                LvsZone::C->value => [11, 12],
                LvsZone::D->value => [10, 10],
                LvsZone::E->value => [0, 9],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [13, 15],
                LvsZone::B->value => [11, 12],
                LvsZone::C->value => [9, 10],
                LvsZone::D->value => [8, 8],
                LvsZone::E->value => [0, 7],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [14, 15],
                LvsZone::B->value => [13, 13],
                LvsZone::C->value => [11, 12],
                LvsZone::D->value => [10, 10],
                LvsZone::E->value => [0, 9],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [14, 15],
                LvsZone::B->value => [12, 13],
                LvsZone::C->value => [11, 11],
                LvsZone::D->value => [9, 10],
                LvsZone::E->value => [0, 8],
            ],
        ],
        TestAudience::LoJ6->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [15, 15],
                LvsZone::B->value => [13, 14],
                LvsZone::C->value => [12, 12],
                LvsZone::D->value => [11, 11],
                LvsZone::E->value => [0, 10],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [15, 15],
                LvsZone::B->value => [13, 14],
                LvsZone::C->value => [11, 12],
                LvsZone::D->value => [10, 10],
                LvsZone::E->value => [0, 9],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [14, 15],
                LvsZone::B->value => [13, 13],
                LvsZone::C->value => [11, 12],
                LvsZone::D->value => [9, 10],
                LvsZone::E->value => [0, 8],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [14, 15],
                LvsZone::B->value => [12, 13],
                LvsZone::C->value => [11, 11],
                LvsZone::D->value => [9, 10],
                LvsZone::E->value => [0, 8],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [14, 15],
                LvsZone::B->value => [12, 13],
                LvsZone::C->value => [10, 11],
                LvsZone::D->value => [9, 9],
                LvsZone::E->value => [0, 8],
            ],
        ],
    ],
    FollowUpSystemSubType::Sentences->value => [
        TestAudience::LoJ1->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [20, 20],
                LvsZone::B->value => [19, 19],
                LvsZone::C->value => [16, 18],
                LvsZone::D->value => [13, 15],
                LvsZone::E->value => [0, 12],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::B->value => [20, 20],
                LvsZone::C->value => [18, 19],
                LvsZone::D->value => [16, 17],
                LvsZone::E->value => [0, 15],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::B->value => [20, 20],
                LvsZone::C->value => [18, 19],
                LvsZone::D->value => [16, 17],
                LvsZone::E->value => [0, 15],
            ],
        ],
        TestAudience::LoJ2->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [20, 20],
                LvsZone::B->value => [19, 19],
                LvsZone::C->value => [17, 18],
                LvsZone::D->value => [15, 16],
                LvsZone::E->value => [0, 14],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [20, 20],
                LvsZone::B->value => [19, 19],
                LvsZone::C->value => [16, 18],
                LvsZone::D->value => [14, 15],
                LvsZone::E->value => [0, 13],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [19, 20],
                LvsZone::B->value => [17, 18],
                LvsZone::C->value => [14, 16],
                LvsZone::D->value => [11, 13],
                LvsZone::E->value => [0, 10],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [19, 20],
                LvsZone::B->value => [18, 18],
                LvsZone::C->value => [15, 17],
                LvsZone::D->value => [13, 14],
                LvsZone::E->value => [0, 12],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [20, 20],
                LvsZone::B->value => [18, 19],
                LvsZone::C->value => [16, 17],
                LvsZone::D->value => [14, 15],
                LvsZone::E->value => [0, 13],
            ],
        ],
        TestAudience::LoJ3->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [19, 20],
                LvsZone::B->value => [17, 18],
                LvsZone::C->value => [14, 16],
                LvsZone::D->value => [12, 13],
                LvsZone::E->value => [0, 11],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [19, 20],
                LvsZone::B->value => [17, 18],
                LvsZone::C->value => [14, 16],
                LvsZone::D->value => [12, 13],
                LvsZone::E->value => [0, 11],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [19, 20],
                LvsZone::B->value => [18, 18],
                LvsZone::C->value => [15, 17],
                LvsZone::D->value => [12, 14],
                LvsZone::E->value => [0, 11],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [19, 20],
                LvsZone::B->value => [18, 18],
                LvsZone::C->value => [16, 17],
                LvsZone::D->value => [14, 15],
                LvsZone::E->value => [0, 13],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [20, 20],
                LvsZone::B->value => [18, 19],
                LvsZone::C->value => [16, 17],
                LvsZone::D->value => [14, 15],
                LvsZone::E->value => [0, 13],
            ],
        ],
        TestAudience::LoJ4->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [28, 30],
                LvsZone::B->value => [25, 27],
                LvsZone::C->value => [21, 24],
                LvsZone::D->value => [19, 20],
                LvsZone::E->value => [0, 18],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [28, 30],
                LvsZone::B->value => [26, 27],
                LvsZone::C->value => [23, 25],
                LvsZone::D->value => [21, 22],
                LvsZone::E->value => [0, 20],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [28, 30],
                LvsZone::B->value => [26, 27],
                LvsZone::C->value => [22, 25],
                LvsZone::D->value => [19, 21],
                LvsZone::E->value => [0, 18],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [29, 30],
                LvsZone::B->value => [27, 28],
                LvsZone::C->value => [24, 26],
                LvsZone::D->value => [21, 23],
                LvsZone::E->value => [0, 20],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [28, 30],
                LvsZone::B->value => [26, 27],
                LvsZone::C->value => [22, 25],
                LvsZone::D->value => [20, 21],
                LvsZone::E->value => [0, 19],
            ],
        ],
        TestAudience::LoJ5->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [27, 30],
                LvsZone::B->value => [24, 26],
                LvsZone::C->value => [20, 23],
                LvsZone::D->value => [17, 19],
                LvsZone::E->value => [0, 16],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [27, 30],
                LvsZone::B->value => [25, 26],
                LvsZone::C->value => [22, 24],
                LvsZone::D->value => [19, 21],
                LvsZone::E->value => [0, 18],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [26, 30],
                LvsZone::B->value => [23, 25],
                LvsZone::C->value => [19, 22],
                LvsZone::D->value => [16, 18],
                LvsZone::E->value => [0, 15],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [25, 30],
                LvsZone::B->value => [22, 24],
                LvsZone::C->value => [18, 21],
                LvsZone::D->value => [15, 17],
                LvsZone::E->value => [0, 14],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [25, 30],
                LvsZone::B->value => [22, 24],
                LvsZone::C->value => [18, 21],
                LvsZone::D->value => [16, 17],
                LvsZone::E->value => [0, 15],
            ],
        ],
        TestAudience::LoJ6->value => [
            TestMoment::DictationRevision1->value => [
                LvsZone::A->value => [28, 30],
                LvsZone::B->value => [25, 27],
                LvsZone::C->value => [22, 24],
                LvsZone::D->value => [20, 21],
                LvsZone::E->value => [0, 19],
            ],
            TestMoment::DictationRevision2->value => [
                LvsZone::A->value => [27, 30],
                LvsZone::B->value => [24, 26],
                LvsZone::C->value => [20, 23],
                LvsZone::D->value => [18, 19],
                LvsZone::E->value => [0, 17],
            ],
            TestMoment::DictationRevision3->value => [
                LvsZone::A->value => [28, 30],
                LvsZone::B->value => [25, 27],
                LvsZone::C->value => [21, 24],
                LvsZone::D->value => [19, 20],
                LvsZone::E->value => [0, 18],
            ],
            TestMoment::DictationRevision4->value => [
                LvsZone::A->value => [25, 30],
                LvsZone::B->value => [22, 24],
                LvsZone::C->value => [18, 21],
                LvsZone::D->value => [15, 17],
                LvsZone::E->value => [0, 14],
            ],
            TestMoment::DictationRevision5->value => [
                LvsZone::A->value => [25, 30],
                LvsZone::B->value => [22, 24],
                LvsZone::C->value => [17, 21],
                LvsZone::D->value => [14, 16],
                LvsZone::E->value => [0, 13],
            ],
        ],
    ],
];
