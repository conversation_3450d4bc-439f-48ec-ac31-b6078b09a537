<?php

use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;
use Cfa\Evaluation\Domain\FollowUpSystem\Zone\CitoZone;

return [
    FollowUpSystemSubType::Default->value => [
        TestAudience::KoJ2->value => [
            TestMoment::October->value => [
                CitoZone::A->value => [29, 43],
                CitoZone::B->value => [23, 28],
                CitoZone::C->value => [18, 22],
                CitoZone::D->value => [13, 17],
                CitoZone::E->value => [0, 12],
            ],
            TestMoment::March->value => [
                CitoZone::A->value => [32, 43],
                CitoZone::B->value => [27, 31],
                CitoZone::C->value => [21, 26],
                CitoZone::D->value => [15, 20],
                CitoZone::E->value => [0, 14],
            ],
        ],
        TestAudience::KoJ3->value => [
            TestMoment::October->value => [
                CitoZone::A->value => [37, 56],
                CitoZone::B->value => [32, 36],
                CitoZone::C->value => [25, 31],
                CitoZone::D->value => [19, 24],
                CitoZone::E->value => [0, 18],
            ],
            TestMoment::March->value => [
                CitoZone::A->value => [43, 56],
                CitoZone::B->value => [37, 43],
                CitoZone::C->value => [29, 36],
                CitoZone::D->value => [20, 28],
                CitoZone::E->value => [0, 19],
            ],
        ],
    ],
];
