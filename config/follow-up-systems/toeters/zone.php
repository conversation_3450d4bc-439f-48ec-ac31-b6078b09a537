<?php

use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;
use Cfa\Evaluation\Domain\FollowUpSystem\Zone\ToetersZone;

// percentile => zone
return [
    FollowUpSystemSubType::Default->value => [
        TestAudience::SeptemberDecember->value => [
            TestMoment::Once->value => [
                0 => ToetersZone::RiskZone2->value,
                1 => ToetersZone::RiskZone2->value,
                3 => ToetersZone::RiskZone2->value,
                5 => ToetersZone::RiskZone2->value,
                7 => ToetersZone::RiskZone2->value,
                10 => ToetersZone::RiskZone2->value,
                15 => ToetersZone::RiskZone1->value,
                20 => ToetersZone::RiskZone1->value,
                25 => ToetersZone::LowOrNoRisk->value,
                30 => ToetersZone::LowOrNoRisk->value,
                35 => ToetersZone::LowOrNoRisk->value,
                40 => ToetersZone::LowOrNoRisk->value,
                45 => ToetersZone::LowOrNoRisk->value,
                50 => ToetersZone::LowOrNoRisk->value,
                55 => ToetersZone::LowOrNoRisk->value,
                60 => ToetersZone::LowOrNoRisk->value,
                65 => ToetersZone::LowOrNoRisk->value,
                70 => ToetersZone::LowOrNoRisk->value,
                75 => ToetersZone::LowOrNoRisk->value,
                80 => ToetersZone::LowOrNoRisk->value,
                85 => ToetersZone::LowOrNoRisk->value,
                90 => ToetersZone::LowOrNoRisk->value,
                95 => ToetersZone::LowOrNoRisk->value,
                99 => ToetersZone::LowOrNoRisk->value,
            ],
        ],
        TestAudience::JanuaryApril->value => [
            TestMoment::Once->value => [
                0 => ToetersZone::RiskZone2->value,
                1 => ToetersZone::RiskZone2->value,
                3 => ToetersZone::RiskZone2->value,
                5 => ToetersZone::RiskZone2->value,
                7 => ToetersZone::RiskZone2->value,
                10 => ToetersZone::RiskZone2->value,
                15 => ToetersZone::RiskZone1->value,
                20 => ToetersZone::RiskZone1->value,
                25 => ToetersZone::LowOrNoRisk->value,
                30 => ToetersZone::LowOrNoRisk->value,
                35 => ToetersZone::LowOrNoRisk->value,
                40 => ToetersZone::LowOrNoRisk->value,
                45 => ToetersZone::LowOrNoRisk->value,
                50 => ToetersZone::LowOrNoRisk->value,
                55 => ToetersZone::LowOrNoRisk->value,
                60 => ToetersZone::LowOrNoRisk->value,
                65 => ToetersZone::LowOrNoRisk->value,
                70 => ToetersZone::LowOrNoRisk->value,
                75 => ToetersZone::LowOrNoRisk->value,
                80 => ToetersZone::LowOrNoRisk->value,
                85 => ToetersZone::LowOrNoRisk->value,
                90 => ToetersZone::LowOrNoRisk->value,
                95 => ToetersZone::LowOrNoRisk->value,
                99 => ToetersZone::LowOrNoRisk->value,
            ],
        ],
        TestAudience::MayAugust->value => [
            TestMoment::Once->value => [
                0 => ToetersZone::RiskZone2->value,
                1 => ToetersZone::RiskZone2->value,
                3 => ToetersZone::RiskZone2->value,
                5 => ToetersZone::RiskZone2->value,
                7 => ToetersZone::RiskZone2->value,
                10 => ToetersZone::RiskZone2->value,
                15 => ToetersZone::RiskZone1->value,
                20 => ToetersZone::RiskZone1->value,
                25 => ToetersZone::LowOrNoRisk->value,
                30 => ToetersZone::LowOrNoRisk->value,
                35 => ToetersZone::LowOrNoRisk->value,
                40 => ToetersZone::LowOrNoRisk->value,
                45 => ToetersZone::LowOrNoRisk->value,
                50 => ToetersZone::LowOrNoRisk->value,
                55 => ToetersZone::LowOrNoRisk->value,
                60 => ToetersZone::LowOrNoRisk->value,
                65 => ToetersZone::LowOrNoRisk->value,
                70 => ToetersZone::LowOrNoRisk->value,
                75 => ToetersZone::LowOrNoRisk->value,
                80 => ToetersZone::LowOrNoRisk->value,
                85 => ToetersZone::LowOrNoRisk->value,
                90 => ToetersZone::LowOrNoRisk->value,
                95 => ToetersZone::LowOrNoRisk->value,
                99 => ToetersZone::LowOrNoRisk->value,
            ],
        ],
    ],
];
