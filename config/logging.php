<?php

use App\Logging\ClusterExceptionFormatter;
use App\Logging\CustomLevelFormatter;
use App\Logging\DataDogJsonFormatter;
use App\Logging\QueryCountFormatter;
use App\Logging\RequestDataFormatter;

return [
    /*
    |--------------------------------------------------------------------------
    | Default Log Channel
    |--------------------------------------------------------------------------
    |
    | This option defines the default log channel that gets used when writing
    | messages to the logs. The name specified in this option should match
    | one of the channels defined in the "channels" configuration array.
    |
    */

    'default' => env('LOG_CHANNEL', 'cluster'),

    'deprecations' => env('LOG_DEPRECATIONS_CHANNEL', env('LOG_CHANNEL', 'cluster')),

    /*
    |--------------------------------------------------------------------------
    | Log Channels
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log channels for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Drivers: "single", "daily", "slack", "syslog",
    |                    "errorlog", "custom", "stack"
    |
    */

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => explode(',', env('LOG_CHANNELS', 'single')),
        ],
        'cluster' => [
            'driver' => 'stack',
            'tap' => [
                ClusterExceptionFormatter::class,
                CustomLevelFormatter::class,
                RequestDataFormatter::class,
                QueryCountFormatter::class,
            ],
            'channels' => ['stderr', 'logfile_cluster'],
        ],
        'local' => [
            'driver' => 'stack',
            'channels' => 'single',
        ],
        'logfile_cluster' => [
            'driver' => 'single',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'formatter' => DataDogJsonFormatter::class,
            'formatter_with' => [
                'applicationName' => 'bingel-tms',
            ],
        ],
        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL'),
        ],
        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/laravel.log'),
            'level' => 'debug',
            'days' => 7,
        ],
        'syslog' => [
            'driver' => 'syslog',
            'level' => 'debug',
        ],
        'errorlog' => [
            'driver' => 'errorlog',
            'level' => 'debug',
        ],
        'stderr' => [
            'driver' => 'single',
            'path' => 'php://stderr',
            'level' => env('LOG_LEVEL', 'debug'),
            'formatter' => DataDogJsonFormatter::class,
            'formatter_with' => [
                'applicationName' => 'bingel-tms',
            ],
        ],
        'commands' => [
            'driver' => 'daily',
            'path' => storage_path('logs/commands.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'days' => 14,
        ],
    ],
];
