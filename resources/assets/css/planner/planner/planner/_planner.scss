// TODO: Planner styling should go into the toolkit, the directives also, and made more general!
// The specifics can also be worked out here in the project, so we can have a better planner.

// Because using bootstrap, which isn't using relative units, and we need to print the planner, we'll going to use
// our own base font-size and using em units to size everything relative to that! So we can scale for printing.

$plr-planner-font-size-screen: 14px;
$plr-planner-font-size-print: 10px;
$plr-planner-days-height: 2em;
$plr-planner-hours-width: 4.07em;
$plr-planner-hours-in-view: 10;
$plr-planner-hours-in-view-zoomed: 5;
$plr-planner-hours-to-start: 8;
$plr-planner-grid-color: #d8d8d8;
$plr-planner-hour-text-color: #3f464c;

@media screen {
  .plr-planner {
    font-size: $plr-planner-font-size-screen;
  }
  .plr-planner-items-body {
    overflow-y: scroll;
  }
  .plr-planner-items-body-inner {
    position: relative;
  }
}

@media print {
  .plr-planner {
    font-size: $plr-planner-font-size-print;
  }
  .plr-planner-items-body {
    // overflow-y: hidden; // Can't be used, or body-inner can't be positioned!
    // >> we'll have another container around it, .plr-planner-items-body-container!
  }
  .plr-planner-items-body-inner,
  .plr-planner-items-body-inner.zoomed {
    position: absolute;
    left: 0;
    right: 0;
    z-index: -1;
    top: calc(-100% * #{$plr-planner-hours-to-start} / #{$plr-planner-hours-in-view});
    height: calc(100% * 24 / $plr-planner-hours-in-view) !important;
    min-height: auto !important;
  }

  .prl-planner-indicators .plr-planner-indicator-hours .plr-planner-time-indicator-label {
    display: none !important;
  }
}

*:focus {
  outline: none;
}

.plr-planner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
}

.plr-planner-days {
  flex: 0 0 auto;
  display: flex;
  height: $plr-planner-days-height;
  border-bottom: 1px solid #c3ccd5; // TODO: use correct var when placed in toolkit!
  padding-left: $plr-planner-hours-width;
}

.plr-planner-day {
  flex: 1;
  line-height: $plr-planner-days-height;
  text-align: center;
  padding: 0 0.6em;
  text-overflow: ellipsis;
  overflow: hidden;
  font-size: 12px;
  position: relative;
}

.plr-planner-day-items-body {
  flex: 0 0 auto;
  position: relative;
  padding-left: $plr-planner-hours-width;
  border-bottom: 2px solid #d4d4d4; // TODO: use correct var when placed in toolkit!
  height: $plr-planner-days-height * 1.4;
  min-height: $plr-planner-days-height * 1.4;
  transition: 0.3s ease;
  transition-property: height;
  // height will be overwritten on element when expanded!
}

.plr-planner-day-items-expand {
  position: absolute;
  left: 0;
  bottom: 0;
  width: $plr-planner-hours-width;
  height: $plr-planner-days-height;
  text-align: center;
}

.plr-planner-day-items {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  user-select: none;

  &:not(.plr-planner-day-items-expanded):after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 10px;
    background: linear-gradient(rgba(#ffffff, 0), rgba(#ffffff, 0.8));
  }
}

.plr-planner-items-body-container {
  flex: 1 1 auto;
  height: 0;
  position: relative;
  overflow: visible;
}

.plr-planner-items-body {
  height: 100%;
  width: 100%;
  position: absolute;
}

.plr-planner-items-body-inner {
  height: calc(100% * 24 / $plr-planner-hours-in-view);
  min-height: 70px * 24;
  padding-left: $plr-planner-hours-width;
  margin-top: 10px;
  &.zoomed {
    height: calc(100% * 24 / $plr-planner-hours-in-view-zoomed);
  }
}

.prl-planner-indicators {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: -1;

  .plr-planner-indicator-hours {
    width: 4.07em;
    height: 100%;
    float: left;
    display: flex;
    flex-direction: column;
    z-index: -1;
    position: relative;

    .plr-planner-hour-label {
      transition-delay: 100ms;
      display: inline-block;
      width: 100%;
      font-size: 0.8em;
      text-align: right;
      padding-right: 5px;
      flex: 1;
      color: $plr-planner-hour-text-color;

      span {
        position: relative;
        top: -0.6em;
      }
    }
    .plr-planner-time-indicator-label {
      color: #ff0000;
      position: absolute;
      padding-right: 5px;
      text-align: right;
      right: 0;
      z-index: 1;
      span {
        background: white;
        box-shadow: 0px 0px 6px #ffffff;
      }
    }
  }

  .plr-planner-indicator-grid {
    transition-delay: 100ms;
    float: left;
    position: relative;
    width: calc(100% - 4.07em);
    height: 100%;
    z-index: -1;

    .plr-planner-time-indicator {
      transition-delay: 100ms;
      position: absolute;
      left: 0;
      right: 0;
      width: 100%;
      height: 1px;
      background-color: #ff9090;
      z-index: 1;
    }

    .plr-planner-indicator-grid-hours {
      transition-delay: 100ms;
      height: 100%;
      display: flex;
      flex-direction: column;

      .plr-planner-indicator-grid-hour {
        transition-delay: 100ms;
        width: 100%;
        border-top: solid 1px #d8d8d8;
        flex: 1;

        &:nth-child(-n + 8):nth-last-child(n + 15),
        &:nth-child(n + 19):nth-last-child(-n + 9) {
          background: mix(#f0f4f7, #ffffff); // TODO: use correct var when placed in toolkit!
        }
        &:last-child {
          border-bottom: solid 1px #d8d8d8;
        }
      }
    }

    .plr-planner-indicator-grid-days {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      display: flex;
      flex-direction: row;

      .plr-planner-indicator-grid-day {
        float: left;
        height: 100%;
        border-left: solid 1px #d8d8d8;
        flex: 1;

        &:nth-child(n + 6) {
          background: mix(#f0f4f7, #ffffff);
        }
        &:last-child {
          border-right: solid 1px #d8d8d8;
        }
      }
    }
  }
}

.plr-planner-hour {
  flex: 1;
  border-bottom: 1px solid #d8d8d8; // TODO: use correct var when placed in toolkit!

  &:nth-child(-n + 8):nth-last-child(n + 15),
  &:nth-child(n + 19):nth-last-child(-n + 9) {
    background: mix(#f0f4f7, #ffffff); // TODO: use correct var when placed in toolkit!
  }
}

.plr-planner-items {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
  user-select: none;
}

// TODO: place the following in toolkit specific part for event styling!
// Relative sizing so we can have a good print!

.plr-planner .c-calendar-event-title,
.plr-planner .c-calendar-event-group {
  font-size: 0.9em;
  line-height: 1em;
}

.plr-planner .c-calendar-event-hints,
.plr-planner .c-calendar-event-group-long,
.plr-planner .c-calendar-event-subtitle,
.plr-planner .c-calendar-event-time,
.plr-planner .c-calendar-event-smart-indicator {
  font-size: 0.8em;
  line-height: 1em;
}
.plr-planner-popover.popover {
  z-index: $zindex-modal - 1;
}
plr-planner-popover.popover {
  display: none;
  top: 40px;
  opacity: 0;

  min-width: 0;
  max-width: none;
  width: 420px;

  &,
  input,
  textarea,
  keygen,
  select,
  button,
  .btn {
    font-size: 13px;
  }

  .form-group:not(:last-child) {
    margin-bottom: 8px;
  }

  .form-control-static {
    min-height: 30px;
  }

  .btn {
    height: 30px;
  }

  plr-calendar-item-course-records-row:not(:last-of-type) .input-group {
    margin-bottom: 4px;
  }

  &,
  &.right,
  &.left {
    margin: 0 $plr-planner-hours-width;
  }

  &.popover-prepare {
    display: block;
  }

  &.popover-visible {
    opacity: 1;
    transition: 400ms ease;
    transition-property: opacity;
  }
}

tk-master-detail plr-calendar-item tk-loader {
  height: auto;
  min-height: 0;
}

.planner .plr-teacher-button {
  border: none;
  padding: 10px;
  width: 100%;
  min-height: $navbar-height;
  position: relative;

  * {
    font-size: 11px;
  }

  &:focus {
    border: none;
  }
}

.slidingElement {
  height: auto;
  overflow: hidden;
  transition: all 500ms ease;
  max-height: 0;

  &.shown {
    max-height: 100vh;
  }
}
