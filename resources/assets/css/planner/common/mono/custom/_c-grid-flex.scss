/* ==========================================================================
  c-grid-flex
   ========================================================================== */

.c-row-flex {
  display: flex;
}

.c-row-flex-gutter {
  .c-col-flex,
  .c-col-shrink {
    padding: 0 10px;

    &:first-child {
      padding-left: 0;
    }

    &:last-child {
      padding-right: 0;
    }
  }
}

.c-row-flex-gutter-small {
  .c-col-flex,
  .c-col-shrink {
    padding: 0 5px;
  }
}

.c-row-flex-vertical {
  flex-direction: column;
}

.c-flex-center-vertically {
  align-items: center;
}

.c-flex-center-horizontally {
  justify-content: center;
}

.c-col-flex {
  flex: 1 auto;
  width: 100%;
}

.c-row-flex-element {
  flex: 1 auto;
}

.c-row-flex-shrink {
  flex: 0;
}

.c-col-shrink {
  flex: 0;
  width: 100%;
}

.c-col-flex-fixed-xs {
  width: 180px;
  flex: 1 0 180px;
}

.c-col-flex-fixed-s {
  width: 240px;
  flex: 1 0 240px;
}

.c-col-flex-fixed-m {
  width: 300px;
  flex: 1 0 300px;
}
