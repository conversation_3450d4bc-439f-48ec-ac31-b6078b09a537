/* ==========================================================================
  c-icon-nav
   ========================================================================== */

/* Component
   ========================================================================== */

.c-icon-nav {
  a {
    width: 44px;
    height: 44px;
    line-height: 44px;
    margin: 2px;
    &:hover,
    &:focus {
      background: #fff;
      transition: background 0.18s;
    }
    &:focus {
      outline: 0;
      box-shadow: 0 0 0 2px $brand-primary;
    }
    display: block;
    border-radius: 2px;
    svg {
      // svg is 24x24
      margin: 10px;
    }
  }
  .active a {
    background: #fff;
  }
}

/* Skins
   ========================================================================== */

.c-icon-nav-primary {
  a {
    &:hover,
    &:focus {
      background: rgba(#000, 0.2);
    }
    &:focus {
      box-shadow: 0 0 0 2px darken($brand-primary, 20%);
    }
  }
  svg * {
    fill: #fff;
  }
}
