@import './settings.scss';

@mixin elevation-min($type: 'default') {
  @if $type == 'important' {
    -webkit-box-shadow: $elevation-min-shadow !important;
    box-shadow: $elevation-min-shadow !important;
  }

  @if $type == 'default' {
    -webkit-box-shadow: $elevation-min-shadow;
    box-shadow: $elevation-min-shadow;
  }
}

.elevation-min {
  @include elevation-min('important');
}

// Revert back to original .v-card-text properties.
.v-dialog > .v-overlay__content > .v-card > .v-card-text {
  font-size: 0.875rem !important;
  letter-spacing: 0.0178571429em !important;
}

.v-no-border {
  border: none !important;
}

.v-border {
  border: 1px solid rgba(0, 0, 0, 0.12) !important;
}

@each $side in $sides {
  .v-border-#{$side} {
    border-#{$side}: 1px solid rgba(0, 0, 0, 0.12) !important;
  }
}

.v-card:not(.v-card--flat) {
  @include elevation-min();
}

.v-card.v-card--flat.v-card--hover:hover {
  @include elevation-min();
}

.v-text-field.v-text-field--solo:not(.v-text-field--solo-flat)
  > .v-input__control
  > .v-input__slot {
  @include elevation-min();
}

.v-btn,
.v-tabs__item {
  text-transform: none !important;
}

.v-btn {
  &.no-focus:focus:not(:hover),
  &.no-hover:hover {
    &::before {
      background-color: transparent !important;
    }
  }
}

.v-date-picker-table .v-btn {
  min-width: 0;
  font-size: 16px;
}

.wrap-btn .v-btn__content {
  text-align: center;
  white-space: normal;
}

.ellipsis-btn {
  max-width: calc(100% - 16px); //reduce margin from max-width

  .v-btn__content {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.ellipsis-menu {
  max-width: 100%;
  display: block;

  .v-btn {
    display: block;
    max-width: 100%;

    .v-btn__content {
      display: block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-right: 24px;

      .v-icon {
        position: absolute;
        right: 0;
      }
    }
  }
}

.mainToolbar,
.secondToolbar {
  .v-toolbar__extension {
    padding: 0;
  }

  .v-toolbar__content {
    padding: 0 16px;
  }
}

.mainToolbar {
  .v-toolbar__content {
    padding-left: 0;
  }
}

.v-toolbar {
  @include elevation-min();

  .v-menu {
    height: 100%;

    // Fix to align options button in notes top bar
    .v-menu__activator {
      height: 100%;
    }
  }
}

.toolbarBtn,
.v-toolbar .v-menu .v-btn:not(.v-btn--icon) {
  height: 60px !important;
  margin-top: 0;
  margin-bottom: 0;
  min-width: 0 !important;
  width: auto;
  border-radius: 0 !important;
}

.v-btn--icon:not(.v-btn--small) {
  min-width: 36px;
  min-height: 36px;
}

.ellipsis-select {
  max-width: 100%;
  display: inline-block;

  .v-select__slot {
    .v-select__selections,
    label {
      max-width: calc(100% - 24px);
    }
  }

  .v-select__selection--comma {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
  }

  &.clearable {
    .v-select__slot {
      .v-select__selections {
        max-width: calc(100% - 24px - 24px);

        .v-select__selection {
          padding-right: 0;
          margin-right: 0;
          max-width: 100%;
        }

        input {
          max-width: 100%;
        }
      }
    }
  }
}

.chip,
.chip .chip__content {
  max-width: 100%;
}

.v-messages__message::first-letter {
  text-transform: capitalize;
}

.v-chip[role='button']:hover:not(.v-chip--disabled) {
  border-color: rgba(0, 0, 0, 0.13);
  box-shadow:
    0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 2px 2px 0 rgba(0, 0, 0, 0.14),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);

  &::after {
    background: currentColor;
    border-radius: inherit;
    content: '';
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    transition: inherit;
    width: 100%;
    pointer-events: none;
    opacity: 0.13;
  }
}

.sticky {
  position: fixed;
}

.sticky-table-400 > .v-table__wrapper {
  max-height: calc(100vh - 400px);
  overflow: auto;
}

table {
  th,
  td {
    transition: background 0.3s cubic-bezier(0.25, 0.8, 0.5, 1);

    &.hover {
      background-color: rgba(0, 0, 0, 0.07);
    }

    &.sticky-header {
      position: -webkit-sticky; /* for Safari */
      position: sticky;
      z-index: 1;
      top: 0;
      background-color: #fff;
      border-bottom: 1px solid #ccc;

      &.hover {
        background-color: #eee;
      }
    }

    &.sticky-column {
      position: -webkit-sticky; /* for Safari */
      position: sticky;
      z-index: 1;
      left: 0;
      background-color: #fff;
      border-right: 1px solid #ccc;

      &.hover {
        background-color: #eee;
      }
    }

    &.group-start {
      border-left: 1px solid #ccc;
    }

    ul {
      &.invisible-list {
        list-style: none;
        padding: 0;
        margin: 0;
        li {
          padding-bottom: 10px;
        }
      }
    }

    &.report-goal-level {
      width: 80px;
      background-repeat: repeat-x;

      padding: 0;
      margin: 0;
      vertical-align: middle;

      img {
        vertical-align: middle;
        float: left;
        margin: 0;
        padding: 0;
      }
    }

    &.report-small-column {
      width: 80px;
    }
  }
}

// treeview that looks like the expansionPanels
.panelTree {
  .v-list-item__append {
    .node-toggle {
      margin-right: $marginsMultiplier * 4;
    }
  }

  .v-list-group--open {
    padding-bottom: $marginsMultiplier * 4;
  }

  .v-list-group,
  .v-list-item--one-line {
    border-top: 1px solid rgba(0, 0, 0, 0.12);

    .v-list-item {
      height: auto;
      min-height: 48px;
    }

    &:first-of-type {
      border-top: none !important;
    }

    .v-list-item__content {
      max-width: 100%;
    }

    .v-list-item-title {
      font-size: 1em;
    }
  }

  .v-list-group__items {
    --indent-padding: 0px;
    margin-bottom: 5px;
    margin-inline-start: calc(var(--parent-padding)) !important;
    box-shadow:
      0 2px 1px -1px rgba(0, 0, 0, 0.2),
      0 1px 1px 0 rgba(0, 0, 0, 0.14),
      0 1px 3px 0 rgba(0, 0, 0, 0.12);

    .v-list-item {
      margin-top: 1px;
    }
  }
}

.scroll-y {
  overflow-y: auto;
  scroll-behavior: smooth;
}

.scroll-x {
  overflow-x: auto;
  scroll-behavior: smooth;
}

.smooth-scroll {
  scroll-behavior: smooth;
}

@media (max-width: 959px) {
  .v-stepper:not(.v-stepper--vertical) {
    .v-stepper__step {
      flex-basis: 75px;
    }

    &.v-stepper--alt-labels .v-stepper__header .v-divider {
      margin: 35px -15px 0;
    }
  }
}

.v-tour-highlighted {
  position: relative;

  &:after {
    content: '';
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
  &.vertical:after {
    border: 2px solid rgba(0, 0, 0, 0.4);
    border-top: none;
    border-bottom: none;
  }
  &.horizontal:after {
    border: 2px solid rgba(0, 0, 0, 0.4);
    border-left: none;
    border-right: none;
  }

  &.first {
    &.vertical:after {
      border-top: 2px solid rgba(0, 0, 0, 0.4);
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
    }
    &.horizontal:after {
      border-left: 2px solid rgba(0, 0, 0, 0.4);
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;
    }
  }

  &.last {
    &.vertical:after {
      border-bottom: 2px solid rgba(0, 0, 0, 0.4);
      border-bottom-left-radius: 10px;
      border-bottom-right-radius: 10px;
    }
    &.horizontal:after {
      border-right: 2px solid rgba(0, 0, 0, 0.4);
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;
    }
  }
}

.v-tour__target--highlighted {
  box-shadow: none !important;
}

//use .no-opacity to disable inputs without changing the style
.theme--light.v-input--selection-controls.v-input--is-disabled:not(
    .v-input--indeterminate
  ).no-opacity
  .v-icon {
  color: rgba(0, 0, 0, 0.57) !important;
}

.planner .form-group .v-input {
  label,
  .v-label {
    font-weight: normal;
    font-size: 14px;
  }
}

// fixes for Froala stylings
.fr-view {
  p {
    line-height: 1.7em;
  }

  table {
    margin-top: 4px;
    margin-bottom: 4px;

    &.hide-borders {
      td,
      th {
        border-style: hidden;
      }
    }
  }

  .checkListIcon {
    margin-left: 4px;
    margin-right: 4px;
  }

  // Fix broken copy-pasted notes.
  & > div > div > div {
    height: auto !important;
  }
}

// add min-width class so that we can give a min width to a flex element (vuetify grid uses max-width)
@mixin createMinWidthValues($prefix: null) {
  @if $prefix == null {
    @for $i from 1 through 12 {
      .min-#{$i} {
        min-width: calc(100% / 12 * $i) !important;
      }
    }
  } @else {
    @for $i from 1 through 12 {
      .min-#{$prefix}-#{$i} {
        min-width: calc(100% / 12 * $i) !important;
      }
    }
  }
}

@include createMinWidthValues();

@media (min-width: 600px) {
  @include createMinWidthValues('sm');
}

@media (min-width: 960px) {
  @include createMinWidthValues('md');
}

@media (min-width: 1264px) {
  @include createMinWidthValues('lg');
}

@media (min-width: 1904px) {
  @include createMinWidthValues('xl');
}

.v-infinite-scroll--vertical {
  overflow-y: visible;
}

// Prevent cutting off the base of labels in an expansion panel (font-size: 1rem).
.v-expansion-panel-title .v-label {
  height: 1.1rem;
}
