import { DOMWrapper, enableAutoUnmount, mount } from '@vue/test-utils';
import lodashDefaults from 'lodash/defaults';
import EvaluationDialog from '../../../../../IPages/Evaluation/FollowUpSystemGoals/Partials/EvaluationDialog.vue';
import { createTestingPinia } from '@pinia/testing';
import createTestingVuetify from '../../../../helpers/CreateTestingVuetify.js';

let wrapper;

const rows = [];
const rowLabel = 'rowLabel';
const bulkCommentLabel = 'bulkCommentLabel';
let notifyMock;

jest.useFakeTimers();

enableAutoUnmount(afterEach);

describe('EvaluationDialog', () => {
  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('renders the component correctly', () => {
    wrapper = mountWrapper();
    expect(wrapper.vm).toBeTruthy();
  });

  describe('dialog opening and closing', () => {
    it('shows the dialog after triggering the show', async () => {
      wrapper = mountWrapper();
      expect(wrapper.find('.v-card').exists()).toBe(false);
      await wrapper.vm.openDialog();
      expect(wrapper.find('.v-card').exists()).toBe(true);
    });

    it('calls closeDialog when clicking the close button', async () => {
      const closeDialogSpy = jest
        .spyOn(EvaluationDialog.methods, 'closeDialog')
        .mockReturnValue(Promise.resolve(true));
      wrapper = mountWrapper();
      await wrapper.vm.openDialog();
      await wrapper.find('.v-toolbar button').trigger('click');
      expect(closeDialogSpy).toHaveBeenCalled();
    });

    it('closes the dialog after calling closeDialog when the form is valid', async () => {
      const saveSpy = jest.spyOn(EvaluationDialog.methods, 'save');
      saveSpy.mockResolvedValueOnce();
      wrapper = mountWrapper();
      await wrapper.vm.closeDialog();
      expect(saveSpy).toHaveBeenCalledTimes(1);
      expect(wrapper.vm.isOpen).toBe(false);
      expect(wrapper.vm.sending).toBe(false);
    });

    it('does not close the dialog when the save is rejected', async () => {
      const saveSpy = jest.spyOn(EvaluationDialog.methods, 'save');
      saveSpy.mockRejectedValueOnce('rejection');
      wrapper = mountWrapper();
      await wrapper.vm.openDialog();
      let actualRejection;
      await wrapper.vm.closeDialog().catch((rejection) => (actualRejection = rejection));
      expect(actualRejection).toStrictEqual('rejection');
      expect(saveSpy).toHaveBeenCalledTimes(1);
      expect(wrapper.vm.isOpen).toBe(true);
      expect(wrapper.vm.sending).toBe(false);
    });

    it('notifies the user when the form is invalid', async () => {
      const saveSpy = jest.spyOn(EvaluationDialog.methods, 'save');
      saveSpy.mockRejectedValue('INVALID_FORM');
      wrapper = mountWrapper();
      await wrapper.vm.openDialog();
      await wrapper.vm.closeDialog();
      expect(saveSpy).toHaveBeenCalledTimes(1);
      expect(wrapper.vm.isOpen).toBe(true);
      expect(wrapper.vm.sending).toBe(false);
      expect(notifyMock).toHaveBeenCalledTimes(1);
    });
  });

  describe('autosave', () => {
    it('does not notify the user when the form is invalid', async () => {
      const saveSpy = jest.spyOn(EvaluationDialog.methods, 'save');
      saveSpy.mockRejectedValue('INVALID_FORM');
      wrapper = mountWrapper();
      await wrapper.vm.openDialog();
      await wrapper.vm.autoSaveImmediate();
      expect(saveSpy).toHaveBeenCalledTimes(1);
      expect(wrapper.vm.isOpen).toBe(true);
      expect(wrapper.vm.sending).toBe(false);
      expect(notifyMock).not.toHaveBeenCalled();
    });

    it('calls the autoSaveImmediate 30 seconds after autoSave', async () => {
      const autoSaveImmediateSpy = jest.spyOn(EvaluationDialog.methods, 'autoSaveImmediate');
      wrapper = mountWrapper();
      await wrapper.vm.openDialog();
      jest.spyOn(wrapper.vm.$refs.validationObserver, 'validate').mockResolvedValue(true);
      await wrapper.vm.autoSave();
      await jest.advanceTimersByTime(29000);
      expect(autoSaveImmediateSpy).not.toHaveBeenCalled();
      await jest.advanceTimersByTime(1001);
      expect(autoSaveImmediateSpy).toHaveBeenCalledTimes(1);
    });
  });

  describe('save', () => {
    it('calls the saveHandler if there are updates', async () => {
      const mockSaveHandler = jest.fn();
      mockSaveHandler.mockResolvedValue();
      wrapper = mountWrapper({ saveHandler: mockSaveHandler });
      await wrapper.vm.openDialog();
      jest.spyOn(wrapper.vm.$refs.validationObserver, 'validate').mockResolvedValue(true);
      jest.spyOn(wrapper.vm, 'hasUpdates').mockReturnValue(true);
      await wrapper.vm.save();
      expect(mockSaveHandler).toHaveBeenCalledTimes(1);
      expect(wrapper.vm.sending).toBe(false);
    });

    it('does not save again while already saving', async () => {
      const mockSaveHandler = jest.fn();
      mockSaveHandler.mockResolvedValue();
      wrapper = mountWrapper({ saveHandler: mockSaveHandler });
      await wrapper.vm.openDialog();
      wrapper.vm.sending = true;
      await wrapper.vm.save();
      expect(mockSaveHandler).not.toHaveBeenCalled();
    });

    it('passes on saveHandler rejections', async () => {
      const mockSaveHandler = jest.fn();
      mockSaveHandler.mockRejectedValue('rejection');
      wrapper = mountWrapper({ saveHandler: mockSaveHandler });
      await wrapper.vm.openDialog();
      jest.spyOn(wrapper.vm.$refs.validationObserver, 'validate').mockResolvedValue(true);
      jest.spyOn(wrapper.vm, 'hasUpdates').mockReturnValue(true);
      let actualRejection;
      await wrapper.vm.save().catch((rejection) => (actualRejection = rejection));
      expect(mockSaveHandler).toHaveBeenCalledTimes(1);
      expect(wrapper.vm.sending).toBe(false);
      expect(actualRejection).toStrictEqual('rejection');
    });

    it('does not call the saveHandler if there are no updates', async () => {
      const mockSaveHandler = jest.fn();
      wrapper = mountWrapper({ saveHandler: mockSaveHandler });
      await wrapper.vm.openDialog();
      jest.spyOn(wrapper.vm.$refs.validationObserver, 'validate').mockResolvedValue(true);
      jest.spyOn(wrapper.vm, 'hasUpdates').mockReturnValue(false);
      await wrapper.vm.save();
      expect(mockSaveHandler).not.toHaveBeenCalled();
      expect(wrapper.vm.sending).toBe(false);
    });

    it('does not call the saveHandler if there are validation errors', async () => {
      const mockSaveHandler = jest.fn();
      wrapper = mountWrapper({ saveHandler: mockSaveHandler });
      await wrapper.vm.openDialog();
      jest.spyOn(wrapper.vm.$refs.validationObserver, 'validate').mockResolvedValue(false);
      let rejectedValue;
      await wrapper.vm.save().catch((rejectReason) => (rejectedValue = rejectReason));
      expect(mockSaveHandler).not.toHaveBeenCalled();
      expect(wrapper.vm.sending).toBe(false);
      expect(rejectedValue).toStrictEqual('INVALID_FORM');
    });

    it('compares scores to determine if there are updates', async () => {
      const rows = [{ score: 3 }, { score: 5 }];
      wrapper = mountWrapper({ rows: rows });
      await wrapper.vm.openDialog();
      await jest.advanceTimersByTime(1);
      expect(wrapper.vm.hasUpdates()).toStrictEqual(false);
      rows[0].score = 2;
      expect(wrapper.vm.hasUpdates()).toStrictEqual(true);
      rows[0].score = 3;
      expect(wrapper.vm.hasUpdates()).toStrictEqual(false);
      rows[1].score = 3;
      expect(wrapper.vm.hasUpdates()).toStrictEqual(true);
    });

    it('compares quotation_uids to determine if there are updates', async () => {
      const rows = [{ quotation_uid: 'a' }, { quotation_uid: null }];
      wrapper = mountWrapper({ rows: rows });
      await wrapper.vm.openDialog();
      await jest.advanceTimersByTime(1);
      expect(wrapper.vm.hasUpdates()).toStrictEqual(false);
      rows[0].quotation_uid = 'b';
      expect(wrapper.vm.hasUpdates()).toStrictEqual(true);
      rows[0].quotation_uid = 'a';
      expect(wrapper.vm.hasUpdates()).toStrictEqual(false);
      rows[1].quotation_uid = 'a';
      expect(wrapper.vm.hasUpdates()).toStrictEqual(true);
    });

    it('compares comments to determine if there are updates', async () => {
      const rows = [{ comment: 'a' }, { comment: null }];
      wrapper = mountWrapper({ rows: rows });
      await wrapper.vm.openDialog();
      await jest.advanceTimersByTime(1);
      expect(wrapper.vm.hasUpdates()).toStrictEqual(false);
      rows[0].comment = 'b';
      expect(wrapper.vm.hasUpdates()).toStrictEqual(true);
      rows[0].comment = 'a';
      expect(wrapper.vm.hasUpdates()).toStrictEqual(false);
      rows[1].comment = 'a';
      expect(wrapper.vm.hasUpdates()).toStrictEqual(true);
    });
  });

  describe('loading indicators', () => {
    it('shows a loading indicator instead of rendering rows if loading is true', async () => {
      wrapper = mountWrapper({ loading: true });
      await wrapper.vm.openDialog();
      const cardText = wrapper.find('.v-card-text');
      expect(cardText.text()).toBe('');
      expect(cardText.find('.v-progress-circular').isVisible()).toBe(true);
      await wrapper.setProps({ loading: false });
      expect(cardText.find('.v-progress-circular').isVisible()).toBe(false);
    });

    it('disables the close button if sendingIndicator is true', async () => {
      wrapper = mountWrapper({ sendingIndicator: true });
      await wrapper.vm.openDialog();
      wrapper.vm.closeDialog();
      expect(wrapper.vm.sending).toBe(true);
      expect(wrapper.vm.isSending).toBe(true);
      const closeButton = wrapper.findComponent({ name: 'VBtn' });
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.isSending).toBe(true);
      expect(closeButton.vm.loading).toBe(true);
      expect(closeButton.vm.disabled).toBe(true);
    });

    it('does not disable the close button if sendingIndicator is false', async () => {
      wrapper = mountWrapper();
      await wrapper.vm.openDialog();
      wrapper.vm.closeDialog();
      expect(wrapper.vm.sending).toBe(true);
      expect(wrapper.vm.isSending).toBe(false);
      const closeButton = wrapper.findComponent({ name: 'VBtn' });
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.isSending).toBe(false);
      expect(closeButton.vm.loading).toBe(false);
      expect(closeButton.vm.disabled).toBe(false);
    });
  });

  describe('headers', () => {
    it('shows the disabled pupils warning if there are disabled rows', async () => {
      const rows = [
        { description: 'row1', disabled: false },
        { description: 'row2', disabled: true },
      ];
      wrapper = mountWrapper({ rows: rows });
      await wrapper.vm.openDialog();
      const cardText = wrapper.find('.v-card-text');
      expect(cardText.text()).toContain('You are not able to evaluate inactive pupils.');
      await wrapper.setProps({
        rows: [
          { description: 'row1', disabled: false },
          { description: 'row2', disabled: false },
        ],
      });
      expect(cardText.text()).not.toContain('You are not able to evaluate inactive pupils.');
    });

    it('allows setting the row label', async () => {
      wrapper = mountWrapper();
      await wrapper.vm.openDialog();
      expect(getRow(1).html()).toContain('<tr class="v-data-table-rows-no-data">');
      let headers = getRow(0).findAll('th');
      expect(headers.length).toBe(1);
      expect(headers.at(0).text()).toBe('rowLabel');
      await wrapper.setProps({ rowLabel: 'my label' });
      headers = getRow(0).findAll('th');
      expect(headers.length).toBe(1);
      expect(headers.at(0).text()).toBe('my label');
    });

    it('shows the scores header if showScoresColumn is true', async () => {
      wrapper = mountWrapper({ showScoresColumn: true });
      await wrapper.vm.openDialog();
      const headers = getRow(0).findAll('th');
      expect(headers.length).toBe(2);
      expect(headers.at(1).text()).toBe('Score');
      expect(getRow(1).findAll('th').length).toBe(2);
    });

    it('shows and hides the bulk score edit field correctly if showScoresColumn is true', async () => {
      wrapper = mountWrapper({ showScoresColumn: true });
      await wrapper.vm.openDialog();
      let headers = getRow(1).findAll('th');
      expect(headers.length).toBe(2);
      let massInputConfirmButton = headers.at(1).findAllComponents({ name: 'VBtn' }).at(0);
      let massInputEditButton = headers.at(1).findAllComponents({ name: 'VBtn' }).at(1);
      expect(massInputEditButton.isVisible()).toBe(true);
      expect(massInputEditButton.findComponent({ name: 'VIcon' }).text()).toBe('edit');
      expect(massInputConfirmButton.isVisible()).toBe(false);
      expect(massInputConfirmButton.findComponent({ name: 'VIcon' }).text()).toContain('check');

      await massInputEditButton.trigger('click');

      headers = getRow(1).findAll('th');
      expect(headers.length).toBe(2);
      massInputConfirmButton = headers.at(1).findAllComponents({ name: 'VBtn' }).at(0);
      massInputEditButton = headers.at(1).findAllComponents({ name: 'VBtn' }).at(1);
      expect(massInputEditButton.isVisible()).toBe(false);
      expect(massInputConfirmButton.isVisible()).toBe(true);
    });

    it('uses the massInputScore data property as model for the bulk edit field', async () => {
      wrapper = mountWrapper({ showScoresColumn: true });
      wrapper.vm.massInputScore = 3;
      await wrapper.vm.openDialog();
      expect(getRow(1).findComponent({ name: 'NumericVTextField' }).vm.modelValue).toBe(3);
    });

    it('hides the bulk edit button for scores if scoresAreImported is true', async () => {
      wrapper = mountWrapper({
        showScoresColumn: true,
        scoresAreImported: true,
      });
      await wrapper.vm.openDialog();
      let headers = getRow(1).findAll('th');
      expect(headers.length).toBe(2);
      expect(headers.at(1).findAllComponents({ name: 'VBtn' }).length).toBe(0);
    });

    it('shows the history header if showHistoryColumn is true', async () => {
      wrapper = mountWrapper({ showHistoryColumn: true });
      await wrapper.vm.openDialog();
      const headers = getRow(0).findAll('th');
      expect(headers.length).toBe(2);
      expect(headers.at(1).text()).toBe('History');
      expect(getRow(1).findAll('th').length).toBe(2);
    });

    it('shows the quotation labels if showQuotationColumn is true', async () => {
      const quotationSystem = {
        quotations: [
          { uid: 'quotation-1', label: 'quotation-1' },
          { uid: 'quotation-2', label: 'quotation-2' },
        ],
        type: { value: 'named' },
      };
      wrapper = mountWrapper({
        showQuotationColumn: true,
        quotationSystem: quotationSystem,
      });
      await wrapper.vm.openDialog();
      const headers = getRow(0).findAll('th');
      expect(headers.length).toBe(3);
      expect(headers.at(1).text()).toBe('quotation-1');
      expect(headers.at(2).text()).toBe('quotation-2');
      expect(getRow(1).findAll('th').length).toBe(3);
    });

    it('shows the quotation icons if showQuotationColumn is true', async () => {
      const quotationSystem = {
        quotations: [
          { uid: 'quotation-1', icon: 'quotation-1' },
          { uid: 'quotation-2', icon: 'quotation-2' },
        ],
        type: { value: 'named' },
      };
      wrapper = mountWrapper({
        showQuotationColumn: true,
        quotationSystem: quotationSystem,
      });
      await wrapper.vm.openDialog();
      const headers = getRow(0).findAll('th');
      expect(headers.length).toBe(3);
      expect(headers.at(1).text()).toBe('');
      expect(headers.at(1).findComponent({ name: 'QuotationDisplay' }).isVisible()).toBe(true);
      expect(headers.at(2).text()).toBe('');
      expect(headers.at(2).findComponent({ name: 'QuotationDisplay' }).isVisible()).toBe(true);

      const quotationDisplay = headers.at(1).findComponent({ name: 'QuotationDisplay' });
      expect(quotationDisplay.vm.quotationSystem).toStrictEqual(quotationSystem);
      expect(quotationDisplay.vm.quotation).toStrictEqual(quotationSystem.quotations[0]);
      expect(quotationDisplay.vm.small).toBe(true);
    });

    it('shows the quotation bulk edit checkboxes if showQuotationColumn is true', async () => {
      const quotationSystem = {
        quotations: [
          { uid: 'quotation-1', icon: 'quotation-1' },
          { uid: 'quotation-2', icon: 'quotation-2' },
        ],
        type: { value: 'named' },
      };
      wrapper = mountWrapper({
        showQuotationColumn: true,
        quotationSystem: quotationSystem,
      });
      await wrapper.vm.openDialog();
      const headers = getRow(1).findAll('th');
      expect(headers.length).toBe(3);

      expect(headers.at(1).findComponent({ name: 'VCheckbox' }).isVisible()).toBe(true);
      expect(headers.at(2).findComponent({ name: 'VCheckbox' }).isVisible()).toBe(true);
    });

    it('hides the quotation header tooltip if the description is empty', async () => {
      const quotationSystem = {
        quotations: [
          { uid: 'quotation-1', label: 'quotation-1', description: '' },
          {
            uid: 'quotation-2',
            label: 'quotation-2',
            description: 'something',
          },
        ],
        type: { value: 'named' },
      };
      wrapper = mountWrapper({
        showQuotationColumn: true,
        quotationSystem: quotationSystem,
      });
      await wrapper.vm.openDialog();
      const headers = getRow(0).findAll('th');
      expect(headers.length).toBe(3);
      expect(headers.at(1).findComponent({ name: 'VTooltip' }).vm.disabled).toBe(true);
      expect(headers.at(2).findComponent({ name: 'VTooltip' }).vm.disabled).toBe(false);
    });

    it('shows the comment header if showCommentsColumn is true', async () => {
      wrapper = mountWrapper({ showCommentsColumn: true });
      await wrapper.vm.openDialog();
      const headers = getRow(0).findAll('th');
      expect(headers.length).toBe(2);
      expect(headers.at(1).text()).toBe('');
      expect(headers.at(1).html()).toContain('width: 200px');
    });

    it('shows the bulk comment edit button if showCommentsColumn is true', async () => {
      wrapper = mountWrapper({
        showCommentsColumn: true,
        bulkCommentLabel: 'bulk comment label',
        commentValidationRules: { rule: 'required' },
      });
      await wrapper.vm.openDialog();
      let headers = getRow(1).findAll('th');
      expect(headers.length).toBe(2);
      const bulkWarningDialog = headers.at(1).findComponent({ name: 'BulkWarningDialog' });
      expect(bulkWarningDialog.isVisible()).toBe(true);
      expect(bulkWarningDialog.vm.hoverMessage).toBe('bulk comment label');
      expect(bulkWarningDialog.vm.commentValidationRules.rule).toBe('required');
    });
  });

  describe('tree view', () => {
    it('can open and close goals in the tree view', async () => {
      const rootRows = [
        { description: 'root1', level: 1, open: true },
        { description: 'root2', level: 1, open: true },
      ];
      const parentRows = [
        { description: 'parent1', level: 2, parent: rootRows[0], open: true },
        { description: 'parent2', level: 2, parent: rootRows[1] },
        { description: 'parent3', level: 2, parent: rootRows[1] },
      ];
      rootRows[0].children = [parentRows[0]];
      rootRows[1].children = [parentRows[1], parentRows[2]];
      const childRows = [{ description: 'child1', level: 3, parent: parentRows[0] }];
      parentRows[0].children = [childRows[0]];
      const flatGoalsList = [
        rootRows[0],
        parentRows[0],
        childRows[0],
        rootRows[1],
        parentRows[1],
        parentRows[2],
      ];
      wrapper = mountWrapper({ rows: flatGoalsList });
      await wrapper.vm.openDialog();

      expect(getRows().length).toBe(7);
      assertGoalRowContent(getRow(1), 'root1', 16, 'keyboard_arrow_up');
      assertGoalRowContent(getRow(2), 'parent1', 32, 'keyboard_arrow_up');
      assertGoalRowContent(getRow(3), 'child1', 48, null);
      assertGoalRowContent(getRow(4), 'root2', 16, 'keyboard_arrow_up');
      assertGoalRowContent(getRow(5), 'parent2', 32, null);
      assertGoalRowContent(getRow(6), 'parent3', 32, null);

      await getRow(4).findAll('td').at(0).trigger('click');
      expect(getRows().length).toBe(5);
      assertGoalRowContent(getRow(4), 'root2', 16, 'keyboard_arrow_down');

      await getRow(1).findAll('td').at(0).trigger('click');
      expect(getRows().length).toBe(3);
      assertGoalRowContent(getRow(1), 'root1', 16, 'keyboard_arrow_down');

      await getRow(1).findAll('td').at(0).trigger('click');
      expect(getRows().length).toBe(4);
      assertGoalRowContent(getRow(1), 'root1', 16, 'keyboard_arrow_up');
      assertGoalRowContent(getRow(2), 'parent1', 32, 'keyboard_arrow_down');
    });

    it('uses a flat structure if no children and parents are provided', async () => {
      wrapper = mountWrapper({
        rows: [{ description: 'pupil1' }, { description: 'pupil2' }],
      });
      await wrapper.vm.openDialog();

      expect(getRows().length).toBe(3);

      let row = getRow(1);
      expect(row.text()).toContain('pupil1');
      expect(row.html()).not.toContain('padding-left');
      expect(row.html()).toContain('cursor: default');
      expect(row.findComponent({ name: 'VBtn' }).exists()).toBe(false);

      row = getRow(2);
      expect(row.text()).toContain('pupil2');
      expect(row.html()).not.toContain('padding-left');
      expect(row.html()).toContain('cursor: default');
      expect(row.findComponent({ name: 'VBtn' }).exists()).toBe(false);
    });
  });

  describe('disabled pupils', () => {
    it('uses the correct column count when showScoresColumn is true', async () => {
      const rows = [
        { description: 'row1', disabled: false },
        { description: 'row2', disabled: true },
      ];
      wrapper = mountWrapper({ rows: rows, showScoresColumn: true });
      await wrapper.vm.openDialog();

      expect(getRow(2).text()).toBe('row1');
      expect(getRow(2).findAll('td').at(1).html()).not.toContain('colspan');
      expect(getRow(3).text()).toBe('row2warning');
      expect(getRow(3).findAll('td').at(1).html()).toContain('colspan="1"');
    });

    it('uses the correct column count when showQuotationColumn and showHistoryColumn are true', async () => {
      const rows = [
        { description: 'row1', disabled: false },
        { description: 'row2', disabled: true },
      ];
      const quotationSystem = {
        quotations: [
          { uid: 'quotation-1', label: 'quotation-1' },
          { uid: 'quotation-2', label: 'quotation-2' },
        ],
        type: { value: 'named' },
      };
      wrapper = mountWrapper({
        rows: rows,
        quotationSystem: quotationSystem,
        showQuotationColumn: true,
        showHistoryColumn: true,
      });
      await wrapper.vm.openDialog();

      expect(getRow(2).text()).toContain('row1');
      expect(getRow(2).findAll('td').at(1).html()).not.toContain('colspan');
      expect(getRow(3).text()).toBe('row2warning');
      expect(getRow(3).findComponent({ name: 'VIcon' }).text()).toBe('warning');
      expect(getRow(3).findAll('td').at(1).html()).toContain('colspan="3"');
    });

    it('uses the correct column count when all columns are shown', async () => {
      const rows = [
        { description: 'row1', disabled: false },
        { description: 'row2', disabled: true },
      ];
      const quotationSystem = {
        quotations: [
          { uid: 'quotation-1', label: 'quotation-1' },
          { uid: 'quotation-2', label: 'quotation-2' },
        ],
        type: { value: 'named' },
      };
      wrapper = mountWrapper({
        rows: rows,
        quotationSystem: quotationSystem,
        showScoresColumn: true,
        showQuotationColumn: true,
        showHistoryColumn: true,
        showCommentsColumn: true,
      });
      await wrapper.vm.openDialog();

      expect(getRow(2).text()).toContain('row1');
      expect(getRow(2).findAll('td').at(1).html()).not.toContain('colspan');
      expect(getRow(3).text()).toBe('row2warning');
      expect(getRow(3).findAll('td').at(1).html()).toContain('colspan="5"');
    });
  });

  describe('scores column ', () => {
    it('shows the scores', async () => {
      wrapper = mountWrapper({
        rows: [
          { description: 'pupil1', score: null },
          { description: 'pupil2', score: 5.5 },
        ],
        showScoresColumn: true,
      });
      await wrapper.vm.openDialog();

      expect(getRows().length).toBe(4);
      expect(getRow(2).findComponent({ name: 'NumericVTextField' }).vm.modelValue).toBe(null);
      expect(getRow(2).findComponent({ name: 'NumericVTextField' }).html()).not.toContain(
        'text-warning'
      );
      expect(getRow(2).findComponent({ name: 'NumericVTextField' }).html()).not.toContain(
        'text-success'
      );
      expect(getRow(3).findComponent({ name: 'NumericVTextField' }).vm.modelValue).toBe(5.5);
      expect(getRow(3).findComponent({ name: 'NumericVTextField' }).html()).not.toContain(
        'text-warning'
      );
      expect(getRow(3).findComponent({ name: 'NumericVTextField' }).html()).not.toContain(
        'text-success'
      );
    });

    it('disables the scores for imported tests', async () => {
      wrapper = mountWrapper({
        rows: [
          { description: 'pupil1', score: 4 },
          { description: 'pupil2', score: 5.5 },
        ],
        showScoresColumn: true,
        scoresAreImported: true,
        minScore: 5,
      });
      await wrapper.vm.openDialog();

      let numberField = getRow(2).findComponent({ name: 'NumericVTextField' });
      expect(numberField.vm.modelValue).toBe(4);
      expect(numberField.html()).toContain('disabled');
      expect(numberField.html()).toContain('warning');
      numberField = getRow(3).findComponent({ name: 'NumericVTextField' });
      expect(numberField.vm.modelValue).toBe(5.5);
      expect(numberField.html()).toContain('disabled');
      expect(numberField.html()).toContain('success');
    });

    it('changes the score color depending on value', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', description: 'pupil1', score: 4 },
          { uid: 'pupil2', description: 'pupil2', score: 5.5 },
          { uid: 'pupil3', description: 'pupil2', score: 'grz' },
        ],
        showScoresColumn: true,
        minScore: 5,
      });
      await wrapper.vm.openDialog();
      await wrapper.vm.$refs.validationObserver.validate();

      expect(getRows().length).toBe(5);

      let row = getRow(2);
      expect(row.findComponent({ name: 'NumericVTextField' }).html()).toContain('text-warning');
      expect(row.findComponent({ name: 'NumericVTextField' }).html()).not.toContain('text-success');
      expect(row.findComponent({ name: 'NumericVTextField' }).html()).not.toContain(
        'v-input--error'
      );

      row = getRow(3);
      expect(row.findComponent({ name: 'NumericVTextField' }).html()).toContain('text-success');
      expect(row.findComponent({ name: 'NumericVTextField' }).html()).not.toContain('text-warning');
      expect(row.findComponent({ name: 'NumericVTextField' }).html()).not.toContain(
        'v-input--error'
      );

      row = getRow(4);
      expect(row.findComponent({ name: 'NumericVTextField' }).html()).toContain('v-input--error');
      expect(row.findComponent({ name: 'NumericVTextField' }).html()).not.toContain('text-success');
      expect(row.findComponent({ name: 'NumericVTextField' }).html()).not.toContain('text-warning');
    });

    it('assigns correct tab indexes to score fields', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', description: 'pupil1', score: 4 },
          { uid: 'pupil2', description: 'pupil2', disabled: true },
          { uid: 'pupil3', description: 'pupil2', score: null },
        ],
        showScoresColumn: true,
      });
      await wrapper.vm.openDialog();
      expect(getRow(2).findComponent({ name: 'NumericVTextField' }).vm.tabindexOnInput).toBe(1);
      expect(getRow(4).findComponent({ name: 'NumericVTextField' }).vm.tabindexOnInput).toBe(2);
    });

    it('passes on the maxScore property to the score fields', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', description: 'pupil1', score: 4 },
          { uid: 'pupil3', description: 'pupil2', score: null },
        ],
        showScoresColumn: true,
        maxScore: 5,
      });
      await wrapper.vm.openDialog();
      expect(getRow(1).findComponent({ name: 'NumericVTextField' }).vm.max).toBe(5);
      expect(getRow(2).findComponent({ name: 'NumericVTextField' }).vm.max).toBe(5);
      expect(getRow(3).findComponent({ name: 'NumericVTextField' }).vm.max).toBe(5);
    });

    it('calls autoSave', async () => {
      wrapper = mountWrapper({
        rows: [{ uid: 'pupil1', description: 'pupil1', score: 4 }],
        showScoresColumn: true,
      });
      await wrapper.vm.openDialog();
      const autoSaveSpy = jest.spyOn(wrapper.vm, 'autoSave');
      getRow(2).findComponent({ name: 'NumericVTextField' }).vm.$emit('update:modelValue', 3);
      expect(autoSaveSpy).toBeCalledTimes(1);
    });
  });

  describe('set scores in bulk', () => {
    it('calls setAllToScore when the bulk score edit field is blurred', async () => {
      const setAllToScoreSpy = jest.spyOn(EvaluationDialog.methods, 'setAllToScore');
      wrapper = mountWrapper({ showScoresColumn: true });
      await wrapper.vm.openDialog();

      await getRow(1).findComponent({ name: 'NumericVTextField' }).find('input').trigger('blur');
      expect(setAllToScoreSpy).toHaveBeenCalled();
    });

    it('does not open the bulk dialog if no scores have been set yet', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', description: 'pupil1', score: null },
          { uid: 'pupil2', description: 'pupil2', disabled: true, score: 4 },
          { uid: 'pupil3', description: 'pupil2', score: null },
        ],
        showScoresColumn: true,
      });
      await wrapper.vm.openDialog();
      const bulkDialogSpy = jest
        .spyOn(wrapper.vm.$refs.bulkDialog, 'saveHandler')
        .mockResolvedValue(true);
      wrapper.vm.massInputScore = 3;
      await wrapper.vm.setAllToScore();
      expect(bulkDialogSpy).not.toHaveBeenCalled();
      expect(getRow(2).findComponent({ name: 'NumericVTextField' }).vm.modelValue).toBe(3);
      expect(getRow(4).findComponent({ name: 'NumericVTextField' }).vm.modelValue).toBe(3);
      expect(wrapper.vm.rows.at(0).score).toBe(3);
      expect(wrapper.vm.rows.at(1).score).toBe(4);
      expect(wrapper.vm.rows.at(2).score).toBe(3);
      expect(wrapper.findComponent({ name: 'BulkWarningDialog' }).vm.showConfirmDialog).toBe(false);
    });

    it('fills only empty scores if the bulk dialog returns false', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', description: 'pupil1', score: 4 },
          { uid: 'pupil2', description: 'pupil2', disabled: true },
          { uid: 'pupil3', description: 'pupil2', score: null },
        ],
        showScoresColumn: true,
      });
      await wrapper.vm.openDialog();
      const bulkDialogSpy = jest
        .spyOn(wrapper.vm.$refs.bulkDialog, 'saveHandler')
        .mockResolvedValue(false);
      wrapper.vm.massInputScore = 3;
      await wrapper.vm.setAllToScore();
      expect(bulkDialogSpy).toHaveBeenCalled();

      expect(wrapper.vm.rows.at(0).score).toBe(4);
      expect(wrapper.vm.rows.at(1).score).toBeUndefined();
      expect(wrapper.vm.rows.at(2).score).toBe(3);
    });

    it('overwrites scores if the bulk dialog returns true', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', description: 'pupil1', score: 4 },
          { uid: 'pupil2', description: 'pupil2', disabled: true },
          { uid: 'pupil3', description: 'pupil2', score: null },
        ],
        showScoresColumn: true,
      });
      await wrapper.vm.openDialog();
      const bulkDialogSpy = jest
        .spyOn(wrapper.vm.$refs.bulkDialog, 'saveHandler')
        .mockResolvedValue(true);
      wrapper.vm.massInputScore = 3;
      await wrapper.vm.setAllToScore();
      expect(bulkDialogSpy).toHaveBeenCalled();

      expect(wrapper.vm.rows.at(0).score).toBe(3);
      expect(wrapper.vm.rows.at(1).score).toBeUndefined();
      expect(wrapper.vm.rows.at(2).score).toBe(3);
    });

    it('does nothing if the bulk dialog is canceled', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', description: 'pupil1', score: 4 },
          { uid: 'pupil2', description: 'pupil2', disabled: true },
          { uid: 'pupil3', description: 'pupil2', score: null },
        ],
        showScoresColumn: true,
      });
      await wrapper.vm.openDialog();
      const bulkDialogSpy = jest
        .spyOn(wrapper.vm.$refs.bulkDialog, 'saveHandler')
        .mockRejectedValue();
      wrapper.vm.massInputScore = 3;
      await wrapper.vm.setAllToScore();
      expect(bulkDialogSpy).toHaveBeenCalled();

      expect(wrapper.vm.rows.at(0).score).toBe(4);
      expect(wrapper.vm.rows.at(1).score).toBeUndefined();
      expect(wrapper.vm.rows.at(2).score).toBeNull();
    });

    it('calls autoSave', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', description: 'pupil1', score: null },
          { uid: 'pupil2', description: 'pupil2', disabled: true, score: 4 },
          { uid: 'pupil3', description: 'pupil2', score: null },
        ],
        showScoresColumn: true,
      });
      await wrapper.vm.openDialog();
      const autoSaveSpy = jest.spyOn(wrapper.vm, 'autoSave');
      wrapper.vm.massInputScore = 3;
      await wrapper.vm.setAllToScore();
      expect(autoSaveSpy).toBeCalledTimes(1);
    });
  });

  describe('history column', () => {
    it('shows a label if no history is available', async () => {
      wrapper = mountWrapper({
        rows: [{ uid: 'pupil1', showHistoryColumn: true }],
        quotationHistory: {},
        showHistoryColumn: true,
      });
      await wrapper.vm.openDialog();
      expect(getRow(2).text()).toContain('No quotations yet');
    });

    it('hides the history in rows where showHistoryColumn is false', async () => {
      wrapper = mountWrapper({
        rows: [{ uid: 'pupil1', showHistoryColumn: false }],
        quotationHistory: {},
        showHistoryColumn: true,
      });
      await wrapper.vm.openDialog();
      expect(getRow(2).text()).not.toContain('No quotations yet');
    });

    it('shows the correct history for each pupil', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', showHistoryColumn: true },
          { uid: 'pupil2', showHistoryColumn: true },
          { uid: 'pupil3', showHistoryColumn: true },
        ],
        quotationSystem: { type: { value: 'something' } },
        quotationHistory: {
          pupil1: [{ quotation: { icon_color: '#a82d41' } }],
          pupil2: [
            { quotation: { icon_color: 'history-color-1' } },
            { quotation: { icon_color: 'history-color-2' } },
            { quotation: { icon_color: 'history-color-3' } },
            { quotation: null },
            { quotation: { icon_color: 'history-color-5' } },
            { quotation: { icon_color: 'history-color-6' } },
          ],
          pupil3: [],
        },
        showHistoryColumn: true,
      });
      await wrapper.vm.openDialog();
      let historyColumns = getRow(2).findAllComponents({ name: 'VCol' });
      expect(historyColumns.length).toBe(1);
      expect(historyColumns.at(0).html()).toContain('background-color: rgb(168, 45, 65)');

      historyColumns = getRow(3).findAllComponents({ name: 'VCol' });
      expect(historyColumns.length).toBe(5);
      expect(historyColumns.at(0).html()).toContain('history-color-5');
      expect(historyColumns.at(1).html()).toContain('background-color: rgb(0, 0, 0)');
      expect(historyColumns.at(2).html()).toContain('history-color-3');
      expect(historyColumns.at(3).html()).toContain('history-color-2');
      expect(historyColumns.at(4).html()).toContain('history-color-1');

      historyColumns = getRow(4).findAllComponents({ name: 'VCol' });
      expect(historyColumns.length).toBe(0);
    });

    it('uses the quotation color for named follow up systems', async () => {
      wrapper = mountWrapper({
        rows: [{ uid: 'pupil1', showHistoryColumn: true }],
        quotationSystem: { type: { value: 'named' } },
        quotationHistory: {
          pupil1: [{ quotation: { color: '#a82d41' } }],
        },
        showHistoryColumn: true,
      });
      await wrapper.vm.openDialog();
      let historyColumns = getRow(2).findAllComponents({ name: 'VCol' });
      expect(historyColumns.length).toBe(1);
      expect(historyColumns.at(0).html()).toContain('background-color: rgb(168, 45, 65)');
    });

    it('passes all quotations to the quotation dialog', async () => {
      const quotationSystem = { type: { value: 'something' } };
      const quotations = [
        { quotation: { icon_color: 'history-color-1' } },
        { quotation: { icon_color: 'history-color-2' } },
        { quotation: { icon_color: 'history-color-3' } },
        { quotation: { icon_color: 'history-color-3' } },
        { quotation: { icon_color: 'history-color-5' } },
        { quotation: { icon_color: 'history-color-6' } },
      ];
      wrapper = mountWrapper({
        rows: [{ uid: 'pupil1', showHistoryColumn: true }],
        quotationSystem: quotationSystem,
        quotationHistory: {
          pupil1: quotations,
        },
        showHistoryColumn: true,
      });
      await wrapper.vm.openDialog();
      let quotationDialog = getRow(2).findComponent({
        name: 'QuotationDialog',
      });
      expect(quotationDialog.vm.quotations).toStrictEqual(quotations);
      expect(quotationDialog.vm.quotationSystem).toStrictEqual(quotationSystem);
    });
  });

  describe('quotation column', () => {
    it('hides the quotations in rows where showQuotationColumn is false', async () => {
      wrapper = mountWrapper({
        rows: [{ uid: 'pupil1', showQuotationColumn: false }],
        quotationSystem: {
          quotations: [{ uid: 'quotation1' }],
          type: { value: 'named' },
        },
        showQuotationColumn: true,
      });
      await wrapper.vm.openDialog();
      expect(getRow(2).findAll('input').length).toBe(0);
    });

    it('shows the quotations for rating type systems', async () => {
      const quotations = [
        { uid: 'quotation1', icon_color: 'quotation-color-1', icon_count: 1 },
        { uid: 'quotation2', icon_color: 'quotation-color-2', icon_count: 2 },
        { uid: 'quotation3', icon_color: 'quotation-color-3', icon_count: 3 },
      ];

      wrapper = mountWrapper({
        rows: [{ uid: 'pupil1', quotation_uid: 'quotation2' }],
        quotationSystem: {
          quotations: quotations,
          type: { value: 'rating' },
        },
        showQuotationColumn: true,
      });
      await wrapper.vm.openDialog();

      let checkBoxes = getRow(2).findAllComponents({ name: 'VCheckbox' });
      expect(checkBoxes.length).toBe(3);
      expect(checkBoxes.at(0).vm.color).toBe('quotation-color-1');
      expect(checkBoxes.at(0).find('input').element.checked).toBe(true);
      expect(checkBoxes.at(1).vm.color).toBe('quotation-color-2');
      expect(checkBoxes.at(1).find('input').element.checked).toBe(true);
      expect(checkBoxes.at(2).vm.color).toBe('quotation-color-3');
      expect(checkBoxes.at(2).find('input').element.checked).toBe(false);

      await checkBoxes.at(2).trigger('input');
      checkBoxes = getRow(2).findAllComponents({ name: 'VCheckbox' });
      expect(checkBoxes.at(0).find('input').element.checked).toBe(true);
      expect(checkBoxes.at(1).find('input').element.checked).toBe(true);
      expect(checkBoxes.at(2).find('input').element.checked).toBe(true);
      expect(wrapper.vm.rows[0].quotation_uid).toBe('quotation3');
      expect(wrapper.emitted('update-item-quotation')).toStrictEqual([
        [
          {
            item: { quotation_uid: 'quotation3', uid: 'pupil1' },
            quotation: quotations[2],
          },
        ],
      ]);

      await checkBoxes.at(2).trigger('input');
      checkBoxes = getRow(2).findAllComponents({ name: 'VCheckbox' });
      expect(checkBoxes.at(0).find('input').element.checked).toBe(false);
      expect(checkBoxes.at(1).find('input').element.checked).toBe(false);
      expect(checkBoxes.at(2).find('input').element.checked).toBe(false);
      expect(wrapper.vm.rows[0].quotation_uid).toBe(null);
      expect(wrapper.emitted('update-item-quotation').length).toBe(2);
      expect(wrapper.emitted('update-item-quotation')[1]).toStrictEqual([
        {
          item: { quotation_uid: null, uid: 'pupil1' },
          quotation: quotations[2],
        },
      ]);
    });

    it('shows the quotations for non rating type systems', async () => {
      const quotations = [
        { uid: 'quotation1', color: 'quotation-color-1' },
        { uid: 'quotation2', color: 'quotation-color-2' },
        { uid: 'quotation3', color: 'quotation-color-3' },
      ];

      wrapper = mountWrapper({
        rows: [{ uid: 'pupil1', quotation_uid: 'quotation2' }],
        quotationSystem: {
          quotations: quotations,
          type: { value: 'named' },
        },
        showQuotationColumn: true,
      });
      await wrapper.vm.openDialog();

      let checkBoxes = getRow(2).findAllComponents({ name: 'VCheckbox' });
      expect(checkBoxes.length).toBe(3);
      expect(checkBoxes.at(0).vm.color).toBe('quotation-color-1');
      expect(checkBoxes.at(0).find('input').element.checked).toBe(false);
      expect(checkBoxes.at(1).vm.color).toBe('quotation-color-2');
      expect(checkBoxes.at(1).find('input').element.checked).toBe(true);
      expect(checkBoxes.at(2).vm.color).toBe('quotation-color-3');
      expect(checkBoxes.at(2).find('input').element.checked).toBe(false);

      await checkBoxes.at(2).find('input').trigger('click');
      checkBoxes = getRow(2).findAllComponents({ name: 'VCheckbox' });
      expect(checkBoxes.at(0).find('input').element.checked).toBe(false);
      expect(checkBoxes.at(1).find('input').element.checked).toBe(false);
      expect(checkBoxes.at(2).find('input').element.checked).toBe(true);
      expect(wrapper.vm.rows[0].quotation_uid).toBe('quotation3');
      expect(wrapper.emitted('update-item-quotation')).toStrictEqual([
        [
          {
            item: { quotation_uid: 'quotation3', uid: 'pupil1' },
            quotation: quotations[2],
          },
        ],
      ]);

      await checkBoxes.at(2).find('input').trigger('click');
      checkBoxes = getRow(2).findAllComponents({ name: 'VCheckbox' });
      expect(checkBoxes.at(0).find('input').element.checked).toBe(false);
      expect(checkBoxes.at(1).find('input').element.checked).toBe(false);
      expect(checkBoxes.at(2).find('input').element.checked).toBe(false);
      expect(wrapper.vm.rows[0].quotation_uid).toBe(null);
      expect(wrapper.emitted('update-item-quotation').length).toBe(2);
      expect(wrapper.emitted('update-item-quotation')[1]).toStrictEqual([
        {
          item: { quotation_uid: null, uid: 'pupil1' },
          quotation: quotations[2],
        },
      ]);
    });

    describe('respects the readOnly property', () => {
      const quotationSystems = [['rating'], ['named']];
      it.each(quotationSystems)('%s system', async (type) => {
        wrapper = mountWrapper({
          readOnly: true,
          rows: [{ uid: 'pupil1', quotation_uid: null }],
          quotationSystem: {
            quotations: [{ uid: 'quotation1' }],
            type: { value: type },
          },
          showQuotationColumn: true,
        });
        await wrapper.vm.openDialog();

        let checkBoxes = getRow(2).findAllComponents({ name: 'VCheckbox' });
        expect(checkBoxes.length).toBe(1);
        expect(checkBoxes.at(0).props().modelValue).toBe(null);
        await checkBoxes.at(0).trigger('click');
        expect(checkBoxes.at(0).props().modelValue).toBe(null);
        expect(wrapper.emitted()).toStrictEqual({});
      });
    });

    it('calls autoSave', async () => {
      wrapper = mountWrapper({
        rows: [{ uid: 'pupil1', quotation_uid: null }],
        quotationSystem: {
          quotations: [{ uid: 'quotation1' }],
          type: { value: 'named' },
        },
        showQuotationColumn: true,
      });
      await wrapper.vm.openDialog();
      const autoSaveSpy = jest.spyOn(wrapper.vm, 'autoSave');

      let checkBoxes = getRow(2).findAllComponents({ name: 'VCheckbox' });
      await checkBoxes.at(0).find('input').trigger('click');
      expect(autoSaveSpy).toBeCalledTimes(1);
    });
  });

  describe('set quotations in bulk', () => {
    describe('calls setAllToQuotation when the bulk checkbox is clicked', () => {
      const quotationSystems = [['rating'], ['named']];
      it.each(quotationSystems)('%s system', async (type) => {
        const setAllToQuotationSpy = jest.spyOn(EvaluationDialog.methods, 'setAllToQuotation');
        wrapper = mountWrapper({
          quotationSystem: {
            quotations: [
              { uid: 'quotation1', icon_count: 1 },
              { uid: 'quotation2', icon_count: 2 },
            ],
            type: { value: 'rating' },
          },
          showQuotationColumn: true,
        });
        await wrapper.vm.openDialog();
        await getRow(1)
          .findAllComponents({ name: 'VCheckbox' })
          .at(1)
          .find('input')
          .trigger('click');
        expect(setAllToQuotationSpy).toHaveBeenCalledWith({
          uid: 'quotation2',
          icon_count: 2,
        });
      });
    });

    describe('correctly shows the different bulk checkbox states', () => {
      const quotationSystems = [
        [
          'rating',
          [
            { uid: 'quotation1', icon_count: 1 },
            { uid: 'quotation2', icon_count: 2 },
          ],
        ],
        ['named', [{ uid: 'quotation1' }, { uid: 'quotation2' }]],
      ];
      it.each(quotationSystems)('%s system', async (type, quotations) => {
        wrapper = mountWrapper({
          rows: [
            { uid: 'pupil1', quotation_uid: 'quotation2' },
            { uid: 'pupil2', quotation_uid: null },
            { uid: 'pupil3', quotation_uid: null, disabled: true },
            { uid: 'pupil4', quotation_uid: 'quotation2' },
          ],
          quotationSystem: {
            quotations: quotations,
            type: { value: type },
          },
          showQuotationColumn: true,
        });

        await wrapper.vm.openDialog();
        expect(
          getRow(1).findAllComponents({ name: 'VCheckbox' }).at(0).find('input').element.checked
        ).toBe(false);
        expect(getRow(1).findAllComponents({ name: 'VCheckbox' }).at(1).html()).toContain(
          'aria-checked="mixed"'
        );

        await getRow(3)
          .findAllComponents({ name: 'VCheckbox' })
          .at(1)
          .find('input')
          .trigger('click');
        expect(wrapper.vm.rows.at(1).quotation_uid).toBe('quotation2');
        await wrapper.vm.$nextTick();
        expect(
          getRow(1).findAllComponents({ name: 'VCheckbox' }).at(0).find('input').element.checked
        ).toBe(false);
        expect(
          getRow(1).findAllComponents({ name: 'VCheckbox' }).at(1).find('input').element.checked
        ).toBe(true);
        await getRow(2)
          .findAllComponents({ name: 'VCheckbox' })
          .at(0)
          .find('input')
          .trigger('click');
        await getRow(3)
          .findAllComponents({ name: 'VCheckbox' })
          .at(0)
          .find('input')
          .trigger('click');
        await wrapper.vm.$nextTick();
        expect(getRow(1).findAllComponents({ name: 'VCheckbox' }).at(0).html()).toContain(
          'aria-checked="mixed"'
        );
        expect(
          getRow(1).findAllComponents({ name: 'VCheckbox' }).at(1).find('input').element.checked
        ).toBe(false);

        await getRow(5)
          .findAllComponents({ name: 'VCheckbox' })
          .at(0)
          .find('input')
          .trigger('click');
        await getRow(1)
          .findAllComponents({ name: 'VCheckbox' })
          .at(0)
          .find('input')
          .trigger('click');
        await wrapper.vm.$nextTick();
        expect(
          getRow(1).findAllComponents({ name: 'VCheckbox' }).at(0).find('input').element.checked
        ).toBe(false);
        expect(
          getRow(1).findAllComponents({ name: 'VCheckbox' }).at(1).find('input').element.checked
        ).toBe(false);
      });
    });

    it('does not open the bulk dialog if no quotations have been set yet', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', quotation_uid: null },
          { uid: 'pupil2', disabled: true, quotation_uid: 'quotation2' },
          { uid: 'pupil3', quotation_uid: null },
        ],
        quotationSystem: {
          quotations: [{ uid: 'quotation1' }, { uid: 'quotation2' }],
          type: { value: 'rating' },
        },
        showQuotationColumn: true,
      });
      await wrapper.vm.openDialog();
      const bulkDialogSpy = jest
        .spyOn(wrapper.vm.$refs.bulkDialog, 'saveHandler')
        .mockResolvedValue(true);
      await wrapper.vm.setAllToQuotation({ uid: 'quotation1' });
      expect(bulkDialogSpy).not.toHaveBeenCalled();
      expect(getRow(2).findComponent({ name: 'VCheckbox' }).vm.modelValue).toBe('quotation1');
      expect(getRow(4).findComponent({ name: 'VCheckbox' }).vm.modelValue).toBe('quotation1');
      expect(wrapper.vm.rows.at(0).quotation_uid).toBe('quotation1');
      expect(wrapper.vm.rows.at(1).quotation_uid).toBe('quotation2');
      expect(wrapper.vm.rows.at(2).quotation_uid).toBe('quotation1');
      expect(wrapper.findComponent({ name: 'BulkWarningDialog' }).vm.showConfirmDialog).toBe(false);
      expect(wrapper.emitted('update-bulk-item-quotation')).toStrictEqual([
        [
          {
            items: [
              { quotation_uid: 'quotation1', uid: 'pupil1' },
              { quotation_uid: 'quotation1', uid: 'pupil3' },
            ],
            quotation: { uid: 'quotation1' },
          },
        ],
      ]);
    });

    it('does not open the bulk dialog if every quotation has the same value', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', quotation_uid: 'quotation1' },
          { uid: 'pupil2', disabled: true, quotation_uid: 'quotation2' },
          { uid: 'pupil3', quotation_uid: null },
        ],
        quotationSystem: {
          quotations: [{ uid: 'quotation1' }, { uid: 'quotation2' }],
          type: { value: 'rating' },
        },
        showQuotationColumn: true,
      });
      await wrapper.vm.openDialog();
      const bulkDialogSpy = jest
        .spyOn(wrapper.vm.$refs.bulkDialog, 'saveHandler')
        .mockResolvedValue(true);
      await wrapper.vm.setAllToQuotation({ uid: 'quotation1' });
      expect(bulkDialogSpy).not.toHaveBeenCalled();
      expect(getRow(2).findComponent({ name: 'VCheckbox' }).vm.value).toBe('quotation1');
      expect(getRow(4).findComponent({ name: 'VCheckbox' }).vm.value).toBe('quotation1');
      expect(wrapper.vm.rows.at(0).quotation_uid).toBe('quotation1');
      expect(wrapper.vm.rows.at(1).quotation_uid).toBe('quotation2');
      expect(wrapper.vm.rows.at(2).quotation_uid).toBe('quotation1');
      expect(wrapper.findComponent({ name: 'BulkWarningDialog' }).vm.showConfirmDialog).toBe(false);
      expect(wrapper.emitted('update-bulk-item-quotation')).toStrictEqual([
        [
          {
            items: [{ quotation_uid: 'quotation1', uid: 'pupil3' }],
            quotation: { uid: 'quotation1' },
          },
        ],
      ]);
    });

    it('fills only empty quotations if the bulk dialog returns false', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', quotation_uid: 'quotation2' },
          { uid: 'pupil2', disabled: true },
          { uid: 'pupil3', quotation_uid: null },
        ],
        quotationSystem: {
          quotations: [{ uid: 'quotation1' }, { uid: 'quotation2' }],
          type: { value: 'rating' },
        },
        showQuotationColumn: true,
      });
      await wrapper.vm.openDialog();
      const bulkDialogSpy = jest
        .spyOn(wrapper.vm.$refs.bulkDialog, 'saveHandler')
        .mockResolvedValue(false);
      await wrapper.vm.setAllToQuotation({ uid: 'quotation1' });
      expect(bulkDialogSpy).toHaveBeenCalled();

      expect(wrapper.vm.rows.at(0).quotation_uid).toBe('quotation2');
      expect(wrapper.vm.rows.at(1).quotation_uid).toBeUndefined();
      expect(wrapper.vm.rows.at(2).quotation_uid).toBe('quotation1');
      expect(wrapper.emitted('update-bulk-item-quotation')).toStrictEqual([
        [
          {
            items: [{ quotation_uid: 'quotation1', uid: 'pupil3' }],
            quotation: { uid: 'quotation1' },
          },
        ],
      ]);
    });

    it('overwrites quotations if the bulk dialog returns true', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', quotation_uid: 'quotation2' },
          { uid: 'pupil2', disabled: true },
          { uid: 'pupil3', quotation_uid: null },
          { uid: 'pupil4', quotation_uid: 'quotation1' },
        ],
        quotationSystem: {
          quotations: [{ uid: 'quotation1' }, { uid: 'quotation2' }],
          type: { value: 'rating' },
        },
        showQuotationColumn: true,
      });
      await wrapper.vm.openDialog();

      const bulkDialogSpy = jest
        .spyOn(wrapper.vm.$refs.bulkDialog, 'saveHandler')
        .mockResolvedValue(true);
      await wrapper.vm.setAllToQuotation({ uid: 'quotation1' });
      expect(bulkDialogSpy).toHaveBeenCalled();

      expect(wrapper.vm.rows.at(0).quotation_uid).toBe('quotation1');
      expect(wrapper.vm.rows.at(1).quotation_uid).toBeUndefined();
      expect(wrapper.vm.rows.at(2).quotation_uid).toBe('quotation1');
      expect(wrapper.vm.rows.at(3).quotation_uid).toBe('quotation1');
      expect(wrapper.emitted('update-bulk-item-quotation')).toStrictEqual([
        [
          {
            items: [
              { quotation_uid: 'quotation1', uid: 'pupil1' },
              { quotation_uid: 'quotation1', uid: 'pupil3' },
            ],
            quotation: { uid: 'quotation1' },
          },
        ],
      ]);
    });

    it('clears quotations if every value is the same as the given quotation', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', quotation_uid: 'quotation1' },
          { uid: 'pupil2', disabled: true },
          { uid: 'pupil3', quotation_uid: 'quotation1' },
        ],
        quotationSystem: {
          quotations: [{ uid: 'quotation1' }, { uid: 'quotation2' }],
          type: { value: 'rating' },
        },
        showQuotationColumn: true,
      });
      await wrapper.vm.openDialog();

      const bulkDialogSpy = jest.spyOn(wrapper.vm.$refs.bulkDialog, 'saveHandler');
      await wrapper.vm.setAllToQuotation({ uid: 'quotation1' });
      expect(bulkDialogSpy).not.toHaveBeenCalled();

      expect(wrapper.vm.rows.at(0).quotation_uid).toBeNull();
      expect(wrapper.vm.rows.at(1).quotation_uid).toBeUndefined();
      expect(wrapper.vm.rows.at(2).quotation_uid).toBeNull();
      expect(wrapper.emitted('update-bulk-item-quotation')).toStrictEqual([
        [
          {
            items: [
              { quotation_uid: null, uid: 'pupil1' },
              { quotation_uid: null, uid: 'pupil3' },
            ],
            quotation: { uid: 'quotation1' },
          },
        ],
      ]);
    });

    it('does nothing if the bulk dialog is canceled', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', quotation_uid: 'quotation2' },
          { uid: 'pupil2', disabled: true },
          { uid: 'pupil3', quotation_uid: null },
        ],
        quotationSystem: {
          quotations: [{ uid: 'quotation1' }, { uid: 'quotation2' }],
          type: { value: 'rating' },
        },
        showQuotationColumn: true,
      });
      await wrapper.vm.openDialog();

      const bulkDialogSpy = jest
        .spyOn(wrapper.vm.$refs.bulkDialog, 'saveHandler')
        .mockRejectedValue();
      await wrapper.vm.setAllToQuotation({ uid: 'quotation1' });
      expect(bulkDialogSpy).toHaveBeenCalled();

      expect(wrapper.vm.rows.at(0).quotation_uid).toBe('quotation2');
      expect(wrapper.vm.rows.at(1).quotation_uid).toBeUndefined();
      expect(wrapper.vm.rows.at(2).quotation_uid).toBeNull();
      expect(wrapper.emitted()).toStrictEqual({});
    });

    it('respects the readOnly property', async () => {
      wrapper = mountWrapper({
        readOnly: true,
        rows: [
          { uid: 'pupil1', quotation_uid: 'quotation2' },
          { uid: 'pupil2', disabled: true },
          { uid: 'pupil3', quotation_uid: null },
        ],
        quotationSystem: {
          quotations: [{ uid: 'quotation1' }, { uid: 'quotation2' }],
          type: { value: 'rating' },
        },
        showQuotationColumn: true,
      });
      await wrapper.vm.openDialog();

      const bulkDialogSpy = jest.spyOn(wrapper.vm.$refs.bulkDialog, 'saveHandler');
      await wrapper.vm.setAllToQuotation({ uid: 'quotation1' });
      expect(bulkDialogSpy).not.toHaveBeenCalled();
      expect(wrapper.emitted()).toStrictEqual({});
    });

    it('calls autoSave', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', quotation_uid: null },
          { uid: 'pupil2', disabled: true, quotation_uid: 'quotation2' },
          { uid: 'pupil3', quotation_uid: null },
        ],
        quotationSystem: {
          quotations: [{ uid: 'quotation1' }, { uid: 'quotation2' }],
          type: { value: 'rating' },
        },
        showQuotationColumn: true,
      });
      await wrapper.vm.openDialog();
      const autoSaveSpy = jest.spyOn(wrapper.vm, 'autoSave');
      await wrapper.vm.setAllToQuotation({ uid: 'quotation1' });
      expect(autoSaveSpy).toBeCalledTimes(1);
    });
  });

  describe('comment column', () => {
    it('hides the comments in rows where showCommentsColumn is false', async () => {
      wrapper = mountWrapper({
        rows: [{ uid: 'pupil1', comment: 'my comment', showCommentsColumn: false }],
        showCommentsColumn: true,
      });
      await wrapper.vm.openDialog();
      expect(getRow(2).findComponent({ name: 'QuotationCommentDialog' }).exists()).toBe(false);
    });

    it('shows the comments', async () => {
      wrapper = mountWrapper({
        rows: [{ uid: 'pupil1', comment: 'my comment 1' }],
        showCommentsColumn: true,
      });
      await wrapper.vm.openDialog();

      let commentDialog = getRow(2).findComponent({
        name: 'QuotationCommentDialog',
      });
      expect(commentDialog.exists()).toBe(true);
      expect(commentDialog.vm.elementUid).toBe('pupil1');
      expect(commentDialog.vm.modelValue).toBe('my comment 1');
      expect(commentDialog.vm.canSave).toBe(true);
      await commentDialog.vm.saveHandler('pupil1', 'my new comment');
      expect(wrapper.vm.rows.at(0).comment).toBe('my new comment');
      expect(wrapper.emitted('update-item-comment')).toStrictEqual([
        [
          {
            item: { comment: 'my new comment', uid: 'pupil1' },
            comment: 'my new comment',
          },
        ],
      ]);
    });

    it('respects the readOnly property', async () => {
      wrapper = mountWrapper({
        readOnly: true,
        rows: [{ uid: 'pupil1', comment: 'my comment 1' }],
        showCommentsColumn: true,
      });
      await wrapper.vm.openDialog();

      let commentDialog = getRow(2).findComponent({
        name: 'QuotationCommentDialog',
      });
      expect(commentDialog.exists()).toBe(true);
      expect(commentDialog.vm.elementUid).toBe('pupil1');
      expect(commentDialog.vm.modelValue).toBe('my comment 1');
      expect(commentDialog.vm.canSave).toBe(false);
      await commentDialog.vm.saveHandler('pupil1', 'my new comment');
      expect(wrapper.vm.rows.at(0).comment).toBe('my comment 1');
      expect(wrapper.emitted()).toStrictEqual({});
    });

    it('calls autoSave', async () => {
      wrapper = mountWrapper({
        rows: [{ uid: 'pupil1', comment: 'my comment 1' }],
        showCommentsColumn: true,
      });
      await wrapper.vm.openDialog();
      const autoSaveSpy = jest.spyOn(wrapper.vm, 'autoSave');
      let commentDialog = getRow(2).findComponent({
        name: 'QuotationCommentDialog',
      });
      await commentDialog.vm.saveHandler('pupil1', 'my new comment');
      expect(autoSaveSpy).toBeCalledTimes(1);
    });
  });

  describe('set comments in bulk', () => {
    it('calls updateAllComments when the bulk warning dialog update comment is triggered', async () => {
      wrapper = mountWrapper({ showCommentsColumn: true });
      const updateAllCommentsSpy = jest.spyOn(wrapper.vm, 'updateAllComments');
      await wrapper.vm.openDialog();
      getRow(1).findComponent({
        name: 'BulkWarningDialog',
      }).vm.overwriteAll = true;
      await getRow(1).findComponent({ name: 'BulkWarningDialog' }).vm.updateComment('new comment');
      expect(updateAllCommentsSpy).toHaveBeenCalledWith('new comment', true);
    });

    it('fills only empty comments if the bulk dialog returns false', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', comment: 'comment 2' },
          { uid: 'pupil2', disabled: true },
          { uid: 'pupil3', comment: null },
        ],
        showCommentsColumn: true,
      });
      await wrapper.vm.openDialog();
      wrapper.vm.updateAllComments({ comment: 'comment 1' }, false);

      expect(wrapper.vm.rows.at(0).comment).toBe('comment 2');
      expect(wrapper.vm.rows.at(1).comment).toBeUndefined();
      expect(wrapper.vm.rows.at(2).comment).toBe('comment 1');
      expect(wrapper.emitted('update-bulk-item-comment')).toStrictEqual([
        [
          {
            items: [{ comment: 'comment 1', uid: 'pupil3' }],
            comment: 'comment 1',
          },
        ],
      ]);
    });

    it('overwrites comments if the bulk dialog returns true', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', comment: 'comment 2' },
          { uid: 'pupil2', disabled: true },
          { uid: 'pupil3', comment: null },
        ],
        showCommentsColumn: true,
      });
      await wrapper.vm.openDialog();

      wrapper.vm.updateAllComments({ comment: 'comment 1' }, true);

      expect(wrapper.vm.rows.at(0).comment).toBe('comment 1');
      expect(wrapper.vm.rows.at(1).comment).toBeUndefined();
      expect(wrapper.vm.rows.at(2).comment).toBe('comment 1');
      expect(wrapper.emitted('update-bulk-item-comment')).toStrictEqual([
        [
          {
            items: [
              { comment: 'comment 1', uid: 'pupil1' },
              { comment: 'comment 1', uid: 'pupil3' },
            ],
            comment: 'comment 1',
          },
        ],
      ]);
    });

    it('respects the readOnly property', async () => {
      wrapper = mountWrapper({
        readOnly: true,
        rows: [
          { uid: 'pupil1', quotation_uid: 'quotation2' },
          { uid: 'pupil2', disabled: true },
          { uid: 'pupil3', quotation_uid: null },
        ],
        quotationSystem: {
          quotations: [{ uid: 'quotation1' }, { uid: 'quotation2' }],
          type: { value: 'rating' },
        },
        showQuotationColumn: true,
      });
      await wrapper.vm.openDialog();

      const bulkDialogSpy = jest.spyOn(wrapper.vm.$refs.bulkDialog, 'saveHandler');
      await wrapper.vm.setAllToQuotation({ uid: 'quotation1' });
      expect(bulkDialogSpy).not.toHaveBeenCalled();
      expect(wrapper.emitted()).toStrictEqual({});
    });

    it('sets hasFilledComments to true if there are any filled comments', async () => {
      wrapper = mountWrapper({
        rows: [{ uid: 'pupil1', comment: 'my comment 1' }],
        showCommentsColumn: true,
      });
      await wrapper.vm.openDialog();
      expect(getRow(1).findComponent({ name: 'BulkWarningDialog' }).vm.hasFilledComments).toBe(
        true
      );
    });

    it('sets hasFilledComments to false if there are no filled comments', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', comment: ' ' },
          { uid: 'pupil2', comment: null },
          { uid: 'pupil3', disabled: true, comment: 'something' },
        ],
        showCommentsColumn: true,
      });
      await wrapper.vm.openDialog();
      expect(getRow(1).findComponent({ name: 'BulkWarningDialog' }).vm.hasFilledComments).toBe(
        false
      );
    });

    it('calls autoSave', async () => {
      wrapper = mountWrapper({
        rows: [
          { uid: 'pupil1', comment: 'comment 2' },
          { uid: 'pupil2', disabled: true },
          { uid: 'pupil3', comment: null },
        ],
        showCommentsColumn: true,
      });
      await wrapper.vm.openDialog();
      const autoSaveSpy = jest.spyOn(wrapper.vm, 'autoSave');
      wrapper.vm.updateAllComments({ comment: 'comment 1' }, true);
      expect(autoSaveSpy).toBeCalledTimes(1);
    });
  });
});

function getRows() {
  return wrapper.findAll('tr');
}

function getRow(index) {
  return getRows()[index];
}

function assertGoalRowContent(row, description, padding, buttonText) {
  expect(row.text()).toContain(description);
  expect(row.html()).toContain('padding-left: ' + padding + 'px');

  if (!buttonText) {
    expect(row.findComponent({ name: 'VBtn' }).exists()).toBe(false);
    expect(row.html()).toContain('cursor: default');

    return;
  }

  expect(row.findComponent({ name: 'VBtn' }).findComponent({ name: 'VIcon' }).text()).toBe(
    buttonText
  );
  expect(row.html()).toContain('cursor: pointer');
}

function mountWrapper(data = {}) {
  notifyMock = jest.fn();
  const component = mount(EvaluationDialog, {
    props: lodashDefaults(data, {
      rows: rows,
      commentValidationRules: {},
      rowLabel: rowLabel,
      bulkCommentLabel: bulkCommentLabel,
    }),
    attachTo: document.body,
    global: {
      plugins: [createTestingPinia(), createTestingVuetify()],
      mocks: {
        $notify: notifyMock,
      },
    },
  });

  const domWrapper = new DOMWrapper(document.body);
  component.find = (selector) => domWrapper.find(selector);
  component.findAll = (selector) => domWrapper.findAll(selector);

  return component;
}
