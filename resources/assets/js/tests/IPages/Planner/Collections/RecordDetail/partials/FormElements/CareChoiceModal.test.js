import { mount } from '@vue/test-utils';
import lodashDefaults from 'lodash/defaults';
import CareChoiceModal from '../../../../../../../components/Planner/Collections/Record/components/CareChoiceModal.vue';
import DialogMock from '../../../../../../mocks/components/DialogMock.vue';
import createTestingVuetify from '../../../../../../helpers/CreateTestingVuetify.js';

const careTypeOptions = [
  { title: 'action', href: 'actionLink' },
  { title: 'Observatie', href: 'ObservatieLink' },
];

let wrapper;

describe('CareChoiceModal', () => {
  it('renders the component correctly', () => {
    expect(mountWrapper().vm).toBeTruthy();
  });

  it('renders the dialog content correctly', async () => {
    wrapper = mountWrapper();
    wrapper.vm.isOpen = true;
    await wrapper.vm.$nextTick();
    expect(wrapper.findAll('.v-list-item')).toHaveLength(careTypeOptions.length);
    for (let i = 0; i < careTypeOptions.length; i++) {
      expect(wrapper.findAll('.v-list-item')[i].text()).toContain(careTypeOptions[i].title);
      expect(wrapper.findAll('.v-list-item')[i].attributes().href).toBe(careTypeOptions[i].href);
    }
  });
});

function mountWrapper(data = {}) {
  return mount(CareChoiceModal, {
    props: lodashDefaults(data, {
      careTypeOptions: careTypeOptions,
    }),
    global: {
      stubs: {
        VDialog: DialogMock,
      },
      plugins: [createTestingVuetify()],
    },
  });
}
