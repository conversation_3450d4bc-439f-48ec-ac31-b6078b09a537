import CollectionDetailRecordOptions from '../../../../../../IPages/Planner/Collections/CollectionDetail/partials/CollectionDetailRecordOptions.vue';
import { DOMWrapper, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import lodashDefaults from 'lodash/defaults';
import User from '../../../../../mocks/User';
import Collection from '../../../../../mocks/planner/Collection';
import TargetAudience from '../../../../../mocks/TargetAudience';
import CollectionLibrary from '../../../../../../models/dataModels/CollectionLibrary';
import TargetAudiences from '../../../../../../models/dataModels/TargetAudiences';
import { default as CollectionDataModel } from '../../../../../../models/dataModels/Collection';
import ConfirmButton from '../../../../../../components/ConfirmButton.vue';
import { useCollectionDetailStore } from '../../../../../../stores/collectionDetailStore';
import createTestingVuetify from '../../../../../helpers/CreateTestingVuetify.js';
import MenuMock from '../../../../../mocks/components/MenuMock.vue';

let collectionDetailStore;

const user = new User();
const collection = new Collection({ owner: user.id });
const targetAudiences = TargetAudience.createMultiple();

let collectionData;
let wrapper;
let chapter;
let record;

describe('CollectionDetailRecordOptions', () => {
  it('renders the component correctly', () => {
    expect(mountWrapper().vm).toBeTruthy();
  });

  it('renders the correct elements', async () => {
    wrapper = mountWrapper();
    expect(wrapper.findComponent({ name: 'VBtn' }).find('i').text()).toBe('more_horiz');
    await wrapper.findComponent({ name: 'VBtn' }).trigger('click');

    const menuContent = wrapper.findComponent(MenuMock);
    expect(menuContent.findAll('.v-list-item')[0].text()).toBe(
      i18n.t('MODULE.COLLECTIONS.COLLECTION.EDIT')
    );
    expect(menuContent.findAll('.v-list-item')[1].text()).toBe(
      i18n.t('MODULE.COLLECTIONS.COLLECTION.DUPLICATE')
    );

    expect(menuContent.findComponent(ConfirmButton).exists()).toBeTruthy();
  });

  it('disables the menu when edit is set', async () => {
    wrapper = mountWrapper();
    await collectionDetailStore.setEdit(record);
    expect(wrapper.findComponent({ name: 'VBtn' }).classes()).toContain('v-btn--disabled');
  });

  describe('delete warning dialog', () => {
    it('shows the warning dialog when a collection is not activated', async () => {
      wrapper = mountWrapper();

      await collectionDetailStore.setHasBeenActivated(false);
      await wrapper.findComponent({ name: 'VBtn' }).trigger('click');
      const confirmButton = wrapper.findComponent(ConfirmButton);
      await confirmButton.findComponent({ name: 'VListItem' }).trigger('click');
      expect(confirmButton.findAllComponents({ name: 'VAlert' }).length).toBe(0);
      expect(new DOMWrapper(document.body).html()).toContain(
        'Are you sure you want to delete record'
      );
    });

    it('shows the warning dialog when a collection is activated by one colleague', async () => {
      wrapper = mountWrapper();
      await collectionDetailStore.setTotalColleagues(1);
      await collectionDetailStore.setHasBeenActivated(true);
      wrapper.vm.record.is_own_record = true;
      await wrapper.findComponent({ name: 'VBtn' }).trigger('click');
      const confirmButton = wrapper.findComponent(ConfirmButton);
      await confirmButton.findComponent({ name: 'VListItem' }).trigger('click');
      expect(confirmButton.findComponent({ name: 'VAlert' }).text()).toContain(
        'Warning! This collection is used by 1 other colleague. This lesson file will also be deleted for them.'
      );
    });

    it('shows the warning dialog when a collection is activated by multiple colleagues', async () => {
      wrapper = mountWrapper();
      await collectionDetailStore.setTotalColleagues(2);
      await collectionDetailStore.setHasBeenActivated(true);
      wrapper.vm.record.is_own_record = true;
      await wrapper.findComponent({ name: 'VBtn' }).trigger('click');
      const confirmButton = wrapper.findComponent(ConfirmButton);
      await confirmButton.findComponent({ name: 'VListItem' }).trigger('click');
      expect(confirmButton.findComponent({ name: 'VAlert' }).text()).toContain(
        'Warning! This collection is used by 2 other colleagues. This lesson file will also be deleted for them.'
      );
    });
    it('shows the correct tooltip and delete button states', () => {
      wrapper = mountWrapper();
      collectionDetailStore.collectionData.shareStatus = 'reader';
      wrapper.vm.record.is_own_record = false;
      expect(wrapper.vm.canNotDeleteRecord).toBeTruthy();
      wrapper.vm.record.is_own_record = true;
      expect(wrapper.vm.canNotDeleteRecord).toBeFalsy();
      collectionDetailStore.collectionData.shareStatus = 'co-author';
      wrapper.vm.record.is_own_record = false;
      expect(wrapper.vm.canNotDeleteRecord).toBeFalsy();
      collectionDetailStore.collectionData.shareStatus = 'co-author';
      wrapper.vm.record.is_own_record = true;
      expect(wrapper.vm.canNotDeleteRecord).toBeFalsy();
      collectionDetailStore.collectionData.shareStatus = 'owner';
      wrapper.vm.record.is_own_record = true;
      expect(wrapper.vm.canNotDeleteRecord).toBeFalsy();
    });
  });
});

function resetStore() {
  collectionDetailStore.setCollectionData({
    collectionData: collectionData,
    isInit: true,
  });
  collectionDetailStore.setTargetAudiencesData(TargetAudiences.pushAll(targetAudiences));
  collectionDetailStore.setLoading(false);
}

function mountWrapper(data = {}) {
  collectionData = new CollectionDataModel(new CollectionLibrary({}), collection);
  collectionData.enrichWithData(collectionData);
  chapter = collectionData.chapters[0];
  record = chapter.records[0];

  const mountedWrapper = mount(CollectionDetailRecordOptions, {
    props: lodashDefaults(data, {
      record: record,
    }),
    global: {
      stubs: {
        VMenu: MenuMock,
      },
      plugins: [
        createTestingPinia({
          stubActions: false,
        }),
        createTestingVuetify(),
      ],
    },
  });

  collectionDetailStore = useCollectionDetailStore();

  resetStore();

  return mountedWrapper;
}
