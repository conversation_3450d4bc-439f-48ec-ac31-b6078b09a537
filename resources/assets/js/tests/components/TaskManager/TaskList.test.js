import { mount } from '@vue/test-utils';
import lodashDefaults from 'lodash/defaults';
import User from '../../mocks/User';
import { default as UserDataModel } from '../../../models/dataModels/User';
import TaskList from '../../../components/TaskManager/TaskList.vue';
import TaskPriorityEnum from '../../../models/enums/TaskPriority';
import TaskStatusEnum from '../../../models/enums/TaskStatus';
import Task from '../../mocks/Task';
import { createTestingPinia } from '@pinia/testing';
import { useTaskStore } from '../../../stores/taskStore';
import createTestingVuetify from '../../helpers/CreateTestingVuetify.js';

const user = new UserDataModel(new User());

const fakeLoadingState = {
  done: jest.fn(),
};

const defaultTaskObject = {
  title: '',
  description: '',
  follow_up: '',
  priority: TaskPriorityEnum.NORMAL.value,
  status: TaskStatusEnum.OPEN.value,
  attachments: [],
  deadline: null,
  care_input_id: 1,
  users: [new User()],
};

let taskStore;

describe('TaskList', () => {
  it('renders correctly', () => {
    let wrapper = mountWrapper();
    expect(wrapper.vm).toBeTruthy();
  });

  it('it checks if deadlines are expired', () => {
    let wrapper = mountWrapper();
    expect(wrapper.vm.deadlineExpired('2020-01-01')).toBeTruthy();
    expect(wrapper.vm.deadlineExpired('3000-01-01')).toBeFalsy();
  });

  it('it uses task url as the next page url', () => {
    let wrapper = mountWrapper();
    wrapper.vm.getNewNextPageUrl();
    expect(wrapper.vm.nextPageUrl).toBe('/task-url');
  });

  it('it doesnt use ajax options when it doesnt use filters', () => {
    let wrapper = mountWrapper({ useFilters: false });
    const empty = {};
    expect(wrapper.vm.getAjaxOptions()).toEqual(empty);
  });

  it('it does use ajax options when it uses filters', () => {
    let wrapper = mountWrapper();
    const expectedOptions = {
      params: {
        start: null,
        end: null,
        priority: null,
        sort_direction: 'desc',
        sort_field: 'deadline',
        status: null,
      },
    };
    expect(wrapper.vm.getAjaxOptions()).toEqual(expectedOptions);
  });

  it('it resets the infinite scroll when filtering and there are no tasks', () => {
    let wrapper = mountWrapper();
    taskStore.setTasks([]);
    wrapper.vm.resetInfiniteScroll = jest.fn();

    wrapper.vm.applyFilters();

    expect(wrapper.vm.nextPageUrl).toBe('/task-url');
    expect(wrapper.vm.itemsToAnimate).toBe(0);
    expect(wrapper.vm.resetInfiniteScroll).toBeCalled();
  });

  it('it resets the filters when filtering and there are tasks', () => {
    let wrapper = mountWrapper();
    const resetFiltersSpy = jest.spyOn(taskStore, 'resetFilters');
    taskStore.setTasks([new Task()]);
    wrapper.vm.applyFilters();

    expect(wrapper.vm.nextPageUrl).toBe('/task-url');
    expect(wrapper.vm.itemsToAnimate).toBe(1);
    expect(resetFiltersSpy).toBeCalled();
  });

  it('it redirects the user to care input details page', () => {
    let wrapper = mountWrapper();
    wrapper.vm.goToCareInput(defaultTaskObject);
    expect(window.location.assign).toHaveBeenCalled();
  });

  describe('Infinite scroll', () => {
    it('it completes the loading when there is no next page', () => {
      let wrapper = mountWrapper();
      wrapper.vm.nextPageUrl = null;
      wrapper.vm.getTasksWithAjax = jest.fn();
      wrapper.vm.infiniteHandler(fakeLoadingState);
      expect(fakeLoadingState.done).toHaveBeenCalledWith('empty');
      expect(wrapper.vm.getTasksWithAjax).toHaveBeenCalledTimes(0);
    });

    it('it loads the next page of tasks when there are more tasks to be loaded', async () => {
      let wrapper = mountWrapper();
      wrapper.vm.nextPageUrl = '/next-page';
      wrapper.vm.getAjaxOptions = jest.fn();
      wrapper.vm.getAjaxOptions.mockReturnValue({});
      wrapper.vm.getTasksWithAjax = jest.fn();
      wrapper.vm.getTasksWithAjax.mockResolvedValue({
        data: {
          next_page_url: '/new-next-page',
          current_page: 1,
          last_page: 2,
        },
      });

      wrapper.vm.infiniteHandler(fakeLoadingState);
      await wrapper.vm.$nextTick();

      expect(wrapper.vm.nextPageUrl).toBe('/new-next-page');
      expect(wrapper.vm.getAjaxOptions).toHaveBeenCalled();
      expect(fakeLoadingState.done).toHaveBeenCalledWith('ok');
    });

    it('it finishes the loading when the last tasks are loaded', async () => {
      let wrapper = mountWrapper();
      wrapper.vm.nextPageUrl = '/next-page';
      wrapper.vm.getAjaxOptions = jest.fn();
      wrapper.vm.getAjaxOptions.mockReturnValue({});
      wrapper.vm.getTasksWithAjax = jest.fn();
      wrapper.vm.getTasksWithAjax.mockResolvedValue({
        data: {
          next_page_url: '/new-next-page',
          current_page: 2,
          last_page: 2,
        },
      });

      wrapper.vm.infiniteHandler(fakeLoadingState);
      await wrapper.vm.$nextTick();

      expect(wrapper.vm.nextPageUrl).toBe('/new-next-page');
      expect(wrapper.vm.getAjaxOptions).toHaveBeenCalled();
      expect(fakeLoadingState.done).toHaveBeenCalledWith('empty');
    });

    it('it increments the items loaded after the transition', () => {
      let wrapper = mountWrapper();
      wrapper.vm.resetInfiniteScroll = jest.fn();
      wrapper.vm.itemsToAnimate = 3;

      wrapper.vm.checkLeaveList();
      expect(wrapper.vm.itemsDone).toBe(1);
      expect(wrapper.vm.resetInfiniteScroll).toHaveBeenCalledTimes(0);
    });

    it('it resets the infinite scroll when all tasks are loaded', () => {
      let wrapper = mountWrapper();
      wrapper.vm.resetInfiniteScroll = jest.fn();
      wrapper.vm.itemsDone = 2;
      wrapper.vm.itemsToAnimate = 3;

      wrapper.vm.checkLeaveList();
      expect(wrapper.vm.itemsDone).toBe(3);
      expect(wrapper.vm.resetInfiniteScroll).toHaveBeenCalled();
    });
  });
});

function mountWrapper(data = {}) {
  const pinia = new createTestingPinia({ stubActions: false });
  const mounted = mount(TaskList, {
    props: lodashDefaults(data, {
      baseUrl: 'test',
      tasksUrl: '/task-url',
      careInputBaseUrl: '/care-input-url/careInputId',
      user: user,
      useFilters: true,
    }),
    global: {
      plugins: [pinia, createTestingVuetify()],
      stubs: {
        InfiniteLoading: { template: '<div><slot></slot></div>' },
      },
    },
  });

  taskStore = useTaskStore(pinia);
  taskStore.$reset();
  taskStore.setTasks([new Task()]);

  return mounted;
}
