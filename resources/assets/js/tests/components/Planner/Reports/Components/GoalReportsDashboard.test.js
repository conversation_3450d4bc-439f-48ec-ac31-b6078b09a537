import { enableAutoUnmount, shallowMount } from '@vue/test-utils';
import GoalReportsDashboard from '../../../../../components/Planner/Reports/components/GoalReportsDashboard.vue';
import TkLoader from '../../../../../components/Planner/common/TkLoader.vue';
import SemiCircle from '../../../../../components/Planner/Reports/components/SemiCircle.vue';
import AnimatedProgressBar from '../../../../../components/Planner/Reports/components/AnimatedProgressBar.vue';
import createTestingVuetify from '../../../../helpers/CreateTestingVuetify.js';
import axios from 'axios';
import moxios from 'moxios';
import moxiosWait from '../../../../helpers/MoxiosWait.js';

jest.mock('../../../../../components/Planner/common/TkLoader.vue', () => ({
  name: 'TkLoader',
  template: '<div><slot></slot></div>',
}));
jest.mock('../../../../../components/Planner/Reports/components/SemiCircle.vue');
jest.mock('../../../../../components/Planner/Reports/components/AnimatedProgressBar.vue');

const mountWrapper = (propsData = {}) => {
  return shallowMount(GoalReportsDashboard, {
    propsData: {
      schoolWithGroups: {
        school: { id: 1, name: 'Test School' },
        groups: [
          {
            id: 1,
            uid: 'group1',
            name: 'Group 1',
            selected: true,
            noSummary: false,
            achievedAreas: [],
            unachievedAreas: '',
          },
          {
            id: 2,
            uid: 'group2',
            name: 'Group 2',
            selected: false,
            noSummary: false,
            achievedAreas: [],
            unachievedAreas: '',
          },
        ],
      },
      schoolyear: { startYear: 2021 },
      reportViewMode: 'list',
      triggerUpdate: false,
      loading: false,
      detailBaseUrl: '/detail',
      goalType: 'LEERLOKAAL',
      ...propsData,
    },
    stubs: {
      TkLoader,
      SemiCircle,
      AnimatedProgressBar,
    },
    global: {
      plugins: [createTestingVuetify()],
    },
  });
};

enableAutoUnmount(afterEach);

describe('GoalReportsDashboard.vue', () => {
  let wrapper;

  beforeEach(() => moxios.install(axios));

  afterEach(() => moxios.uninstall(axios));

  it('renders correctly', () => {
    wrapper = mountWrapper();
    expect(wrapper.exists()).toBe(true);
  });

  it('displays the correct number of groups', () => {
    wrapper = mountWrapper();
    const groups = wrapper.findAll('.panel-default');
    expect(groups.length).toBe(1); // Only one group is selected
  });

  it('displays the correct school and group name', () => {
    wrapper = mountWrapper();
    const title = wrapper.find('.panel-title');
    expect(title.text()).toBe('Test School - Group 1');
  });

  it('changes view mode correctly', async () => {
    wrapper = mountWrapper({ reportViewMode: 'grid' });
    expect(wrapper.find('.v-col-12').exists()).toBe(true);

    await wrapper.setProps({ reportViewMode: 'list' });
    expect(wrapper.find('.v-col-sm-6').exists()).toBe(true);
  });

  it('handles no summary correctly', async () => {
    wrapper = mountWrapper({
      schoolWithGroups: {
        school: { id: 1, name: 'Test School' },
        groups: [
          {
            id: 1,
            uid: 'group1',
            name: 'Group 1',
            selected: true,
            noSummary: true,
            achievedAreas: [],
            unachievedAreas: '',
          },
        ],
      },
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.text()).toContain('No summary available for this group.');
  });

  it('displays achieved areas correctly', (done) => {
    const mockAchievedAreas = [{ id: 'area1', name: 'Math', goalsAchieved: 5, goalsTotal: 10 }];

    moxios.stubRequest(/.*\/progress/, {
      status: 200,
      response: [{ id: 1, learningAreas: mockAchievedAreas }],
    });

    wrapper = mountWrapper({
      schoolWithGroups: {
        school: { id: 1, name: 'Test School' },
        groups: [
          {
            id: 1,
            uid: 'group1',
            name: 'Group 1',
            selected: true,
            noSummary: false,
            achievedAreas: mockAchievedAreas,
            unachievedAreas: '',
          },
        ],
      },
    });

    moxios.wait(() => {
      wrapper.vm.$nextTick(() => {
        const areaElement = wrapper.find('.records-clickableItem');
        expect(areaElement.exists()).toBe(true);
        expect(areaElement.text()).toContain('Math');
        expect(areaElement.text()).toContain('5 (50%)');
        done();
      });
    });
  });

  it('navigates to the correct detail page when an area is clicked', async () => {
    wrapper = mountWrapper({
      schoolWithGroups: {
        school: { id: 1, name: 'Test School' },
        groups: [
          {
            id: 1,
            uid: 'group1',
            name: 'Group 1',
            selected: true,
            noSummary: false,
            achievedAreas: [{ id: 'area1', name: 'Math', goalsAchieved: 5, goalsTotal: 10 }],
            unachievedAreas: '',
          },
        ],
      },
    });

    await wrapper.vm.$nextTick();
    await moxiosWait();

    const areaElement = wrapper.find('.records-clickableItem');
    await areaElement.trigger('click');

    expect(window.location.assign).toHaveBeenCalledWith(
      expect.stringMatching('/detail\\?learningAreaId=area1&curriculumType=LEERLOKAAL$')
    );
  });

  it('navigates to the correct detail page when the attitudes link clicked', async () => {
    const mockUnAchievedAreas = [
      { id: 'mathId', name: 'Math', goalsAchieved: 0 },
      { id: 'attitudeId', name: 'Attitudes', goalsAchieved: 0 },
    ];

    moxios.stubRequest(/.*\/progress/, {
      status: 200,
      response: [{ id: 1, learningAreas: mockUnAchievedAreas }],
    });

    wrapper = mountWrapper({
      schoolWithGroups: {
        school: { id: 1, name: 'Test School' },
        groups: [
          {
            id: 1,
            uid: 'group1',
            name: 'Group 1',
            selected: true,
          },
        ],
      },
    });

    await wrapper.vm.$nextTick();
    await moxiosWait();

    const divs = wrapper.findAll('div.v-col-12');
    expect(divs.length).toBe(2);
    expect(divs[0].text()).toBe(
      'No goals achieved for: Math. Add goals for these learning areas to records in your calendar.'
    );
    expect(divs[1].text()).toBe(
      'Show all planned goals and attitudes for this group. Attitudes apply at all ages and serve as a basic attitude for all goals'
    );
    expect(divs[1].find('a').attributes().href).toBe('/detail?curriculumType=LEERLOKAAL');
  });
});
