import { mount } from '@vue/test-utils';
import lodashDefaults from 'lodash/defaults';
import PlrCalendarItemTimeProperties from '../../../../components/Planner/Calendar/components/PlrCalendarItemTimeProperties.vue';
import createTestingVuetify from '../../../helpers/CreateTestingVuetify.js';
import dayjs from 'dayjs';
import PlrCalendarItemTimePropertiesItem from '../../../../tests/mocks/planner/PlrCalendarItemTimePropertiesItem';

import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
dayjs.extend(isSameOrAfter);

const mockIsDateInArray = jest.fn();

jest.mock('../../../../composables/useDateFormat.js', () => ({
  useDateFormat: () => ({
    isDateInArray: mockIsDateInArray,
  }),
}));

describe('PlrCalendarItemTimeProperties.vue', () => {
  let wrapper;
  let item;
  let mockNotify;

  const mockSchoolyear = {
    end: dayjs('2024-06-30'),
  };

  const defaultProps = {
    item: new PlrCalendarItemTimePropertiesItem(),
    schoolyear: mockSchoolyear,
    minimumPlanningDate: new Date('2024-01-01'),
    maximumPlanningDate: new Date('2024-12-31'),
    plannerVisibleDates: ['2024-12-20', '2024-12-21', '2024-12-22'],
    plannerVisibleDatesFormat: 'YYYY-MM-DD',
  };

  beforeEach(() => {
    mockIsDateInArray.mockReset();

    mockNotify = jest.fn();

    item = new PlrCalendarItemTimePropertiesItem();

    wrapper = mountWrapper();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  function mountWrapper(propsData = {}) {
    return mount(PlrCalendarItemTimeProperties, {
      props: lodashDefaults(propsData, defaultProps),
      global: {
        mocks: {
          $notify: mockNotify,
        },
        stubs: {
          TkFormGroup: true,
          DatePicker: true,
          TkTimeInput: true,
          TkDuration: true,
          'v-checkbox': true,
          'v-select': true,
          'v-list-item': true,
        },
        plugins: [createTestingVuetify()],
      },
    });
  }

  it('renders the component correctly', () => {
    expect(wrapper.vm).toBeTruthy();
  });

  it('keeps item visible and does not notify when date is within plannerVisibleDates', () => {
    const newDate = '2024-12-21';
    mockIsDateInArray.mockReturnValue(true);

    wrapper.vm.saveItemDate(newDate);

    expect(wrapper.props().item.begin.format('YYYY-MM-DD')).toBe(newDate);
    expect(wrapper.props().item.show).toBe(true);
    expect(mockIsDateInArray).toHaveBeenCalledWith(
      dayjs(`${newDate} ${wrapper.props().item.begin.format('HH:mm')}`),
      defaultProps.plannerVisibleDates,
      defaultProps.plannerVisibleDatesFormat
    );
    expect(mockNotify).not.toHaveBeenCalled();
    expect(wrapper.props().item.save).toHaveBeenCalled();
    expect(wrapper.props().item.timeUpdated.fire).toHaveBeenCalled();
  });

  it('hides item and notifies when date is outside plannerVisibleDates', () => {
    const newDate = '2024-12-25';
    mockIsDateInArray.mockReturnValue(false);

    wrapper.vm.saveItemDate(newDate);

    expect(wrapper.props().item.begin.format('YYYY-MM-DD')).toBe(newDate);
    expect(wrapper.props().item.show).toBe(false);
    expect(mockIsDateInArray).toHaveBeenCalledWith(
      dayjs(`${newDate} ${wrapper.props().item.begin.format('HH:mm')}`),
      defaultProps.plannerVisibleDates,
      defaultProps.plannerVisibleDatesFormat
    );
    expect(mockNotify).toHaveBeenCalledWith({
      color: 'warning',
      text: i18n.t('labels.modules.planner.collections.calendar.item.moved-out-of-calendar-range'),
    });
    expect(wrapper.props().item.save).toHaveBeenCalled();
    expect(wrapper.props().item.timeUpdated.fire).toHaveBeenCalled();
  });
});
