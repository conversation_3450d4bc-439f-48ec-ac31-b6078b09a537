import TaskPriorityEnum from '../../models/enums/TaskPriority';
import TaskStatusEnum from '../../models/enums/TaskStatus';
import Task from '../mocks/Task';
import lodashCloneDeep from 'lodash/cloneDeep';
import moxios from 'moxios';
import { useTaskStore } from '../../stores/taskStore';
import { createPinia, setActivePinia } from 'pinia';

const defaultTaskObject = {
  title: '',
  description: '',
  follow_up: '',
  priority: TaskPriorityEnum.NORMAL.value,
  status: TaskStatusEnum.OPEN.value,
  attachments: [],
  deadline: null,
  care_input_id: null,
  users: [],
};
const baseUrl = 'baseUrl';

setActivePinia(createPinia());
const store = useTaskStore();
store.$notify = jest.fn();

describe('taskStore', () => {
  beforeEach(() => {
    store.$reset();
  });

  test('defaults', () => {
    expect(store.tasks).toStrictEqual([]);
    expect(store.baseUrl).toBe('');
    expect(store.showTaskDrawer).toBeFalsy();
    expect(defaultTaskObject).toStrictEqual(store.defaultTask);
  });

  describe('getters', () => {
    it('returns the defaultTask', () => {
      expect(defaultTaskObject).toStrictEqual(store.getDefaultTask);
    });
  });

  describe('actions', () => {
    beforeEach(() => {
      moxios.install(axios);
    });

    afterEach(() => {
      moxios.uninstall(axios);
      jest.clearAllMocks();
    });

    test('setBaseUrl', () => {
      store.setBaseUrl(baseUrl);
      expect(store.baseUrl).toBe(baseUrl);
    });

    test('setCareInputId', () => {
      const careInputId = 123;
      store.setCareInputId(careInputId);
      expect(store.care_input_id).toBe(careInputId);
      expect(store.defaultTask.care_input_id).toBe(careInputId);
    });

    test('setUserData', () => {
      const user = { test: 'hello' };
      store.setUserData(user);
      expect(store.defaultTask.users).toStrictEqual([user]);
    });

    test('setTasks', () => {
      const tasks = [new Task(), new Task()];
      store.setTasks(tasks);
      expect(store.tasks).toStrictEqual(tasks);
      expect(store.tasks.length).toBe(2);
    });

    test('addTasks', () => {
      store.setTasks([new Task()]);

      const tasks = [new Task(), new Task()];

      store.addTasks(tasks);
      expect(store.tasks).toEqual(expect.arrayContaining(tasks));
      expect(store.tasks.length).toBe(3);
    });

    test('addTask', () => {
      const selectedTask = new Task();
      const newTask = new Task({ title: selectedTask.title });

      store.setTasks([new Task()]);

      store.selectedTask = selectedTask;

      store.addTask(newTask);

      expect(store.tasks[0].title).toBe(selectedTask.title);
      expect(store.tasks[0].description).toBe(newTask.description);
      expect(store.showTaskDrawer).toBeFalsy();
    });

    test('replaceTaskContent', () => {
      const defaultTask = new Task();
      const newTask = new Task({ uid: defaultTask.uid });
      store.setTasks([defaultTask]);

      store.replaceTaskContent(newTask);

      expect(store.tasks[0].uid).toBe(defaultTask.uid);
      expect(store.tasks[0].title).toBe(newTask.title);
      expect(store.tasks[0].description).toBe(newTask.description);
    });

    test('editTask', () => {
      const defaultTask = new Task();
      store.setTasks([defaultTask]);

      store.editTask(0);

      expect(store.selectedTask.uid).toBe(defaultTask.uid);
      expect(store.taskDrawerStatus).toBe('edit');
    });

    test('createTaskInStore', () => {
      const defaultTask = new Task();
      store.setTasks([defaultTask]);

      store.createTaskInStore();

      expect(store.showTaskDrawer).toBeTruthy();
      expect(store.selectedTask).toStrictEqual(defaultTaskObject);
      expect(store.taskDrawerStatus).toBe('create');
    });

    test('showTask', () => {
      const defaultTask = new Task();
      store.setTasks([defaultTask]);

      store.showTask(0);

      expect(store.showTaskDrawer).toBeTruthy();
      expect(store.selectedTask).toStrictEqual(defaultTask);
    });

    test('deleteTaskInStore', () => {
      const defaultTask = new Task();
      const tasks = [defaultTask, new Task(), new Task()];

      store.setTasks(lodashCloneDeep(tasks));

      expect(store.tasks.length).toBe(3);

      store.deleteTaskInStore(0);

      expect(store.tasks.length).toBe(2);
      expect(store.tasks[0]).toStrictEqual(tasks[1]);

      store.deleteTaskInStore(1);

      expect(store.tasks.length).toBe(1);
      expect(store.tasks[0]).toStrictEqual(tasks[1]);
    });

    test('selectTask', () => {
      const defaultTask = new Task();
      store.setTasks([defaultTask]);

      store.selectTask(0);

      expect(store.beforeEditing).toEqual(defaultTask);
      expect(store.selectedTask).toStrictEqual(defaultTask);
      expect(store.showTaskDrawer).toBeTruthy();
    });

    test('cancelTask', () => {
      const defaultTask = new Task();
      const editedTask = lodashCloneDeep(defaultTask);
      editedTask.title = 'edited';

      store.beforeEditing = lodashCloneDeep(defaultTask);
      store.selectedTask = editedTask;

      store.cancelTask();

      expect(store.selectedTask).toStrictEqual(defaultTask);
      expect(store.showTaskDrawer).toBeFalsy();
    });

    test('showDrawer', () => {
      expect(store.showTaskDrawer).toBeFalsy();
      store.showDrawer(true);
      expect(store.showTaskDrawer).toBeTruthy();
      store.showDrawer(false);
      expect(store.showTaskDrawer).toBeFalsy();
    });

    test('patchTaskInStore', () => {
      const defaultTask = new Task();

      store.setTasks([defaultTask]);

      store.patchTaskInStore(0);

      expect(store.taskDrawerStatus).toBe('patch');
      expect(store.selectedTask).toStrictEqual(defaultTask);
      expect(store.showTaskDrawer).toBeTruthy();
    });

    test('clearTasks', () => {
      const defaultTask = new Task();

      store.setTasks([defaultTask]);

      expect(store.tasks.length).toBe(1);
      store.clearTasks();
      expect(store.tasks.length).toBe(0);
    });

    test('resetInfiniteScroll', () => {
      const previousInfinityIdentifier = lodashCloneDeep(store.infinityIdentifier);
      store.resetInfiniteScroll();
      expect(store.infinityIdentifier).toBe(previousInfinityIdentifier + 1);
    });

    test('getTasksWithAjax', async () => {
      const request = { url: 'baseUrl' };
      const response = [new Task(), new Task()];
      moxios.stubRequest(request.url, {
        status: 200,
        response: { data: response },
      });

      await store.getTasksWithAjax(request);
      expect(moxios.requests.mostRecent().url).toBe(request.url);
      expect(store.tasks).toStrictEqual(response);
    });

    describe('createTask', () => {
      test('success flow', async () => {
        store.baseUrl = baseUrl;
        const response = new Task();
        store.selectedTask = response;
        moxios.stubRequest(baseUrl, {
          status: 200,
          response: response,
        });

        await store.createTask();
        expect(moxios.requests.mostRecent().url).toBe(baseUrl);
        expect(store.tasks[0]).toEqual(response);
      });

      test('fail flow', async () => {
        store.baseUrl = baseUrl;
        const response = new Task();
        store.selectedTask = response;
        moxios.stubRequest(baseUrl, {
          status: 500,
          response: { message: 'hello' },
        });

        await store.createTask();
        expect(moxios.requests.mostRecent().url).toBe(baseUrl);
        expect(store.tasks.length).toBe(0);
      });
    });

    describe('updateTask', () => {
      test('success flow', async () => {
        const defaultTask = new Task();
        const newTask = new Task({ uid: defaultTask.uid });
        store.tasks = [defaultTask];
        store.baseUrl = baseUrl;

        store.selectedTask = newTask;
        moxios.stubRequest(baseUrl + '/' + newTask.uid, {
          status: 200,
          response: newTask,
        });

        await store.updateTask();
        expect(moxios.requests.mostRecent().url).toBe(baseUrl + '/' + newTask.uid);
        expect(store.tasks[0]).toEqual(newTask);
      });

      test('fail flow', async () => {
        const defaultTask = new Task();
        const newTask = new Task({ uid: defaultTask.uid });
        store.tasks = [defaultTask];
        store.baseUrl = baseUrl;

        store.selectedTask = newTask;
        moxios.stubRequest(baseUrl + '/' + newTask.uid, {
          status: 500,
          response: { message: 'hello' },
        });

        await store.updateTask();
        expect(moxios.requests.mostRecent().url).toBe(baseUrl + '/' + newTask.uid);
        expect(store.tasks[0]).toEqual(defaultTask);
      });
    });

    describe('patchTask', () => {
      test('success flow', async () => {
        const defaultTask = new Task();
        const newTask = new Task({ uid: defaultTask.uid });
        store.tasks = [defaultTask];
        store.baseUrl = baseUrl;

        store.selectedTask = newTask;
        moxios.stubRequest(baseUrl + '/' + newTask.uid, {
          status: 200,
          response: newTask,
        });

        await store.patchTask();
        expect(moxios.requests.mostRecent().url).toBe(baseUrl + '/' + newTask.uid);
        expect(store.tasks[0]).toEqual(newTask);
      });

      test('fail flow', async () => {
        const defaultTask = new Task();
        const newTask = new Task({ uid: defaultTask.uid });
        store.tasks = [defaultTask];
        store.baseUrl = baseUrl;

        store.selectedTask = newTask;
        moxios.stubRequest(baseUrl + '/' + newTask.uid, {
          status: 500,
          response: { message: 'hello' },
        });

        await store.patchTask();
        expect(moxios.requests.mostRecent().url).toBe(baseUrl + '/' + newTask.uid);
        expect(store.tasks[0]).toEqual(defaultTask);
      });
    });

    describe('deleteTask', () => {
      test('success flow', async () => {
        const defaultTask = new Task();
        store.tasks = [defaultTask];
        store.baseUrl = baseUrl;

        moxios.stubRequest(baseUrl + '/' + defaultTask.uid, {
          status: 200,
        });

        await store.deleteTask({ task: defaultTask, index: 0 });
        expect(moxios.requests.mostRecent().url).toBe(baseUrl + '/' + defaultTask.uid);
        expect(store.tasks.length).toBe(0);
      });

      test('fail flow', async () => {
        const defaultTask = new Task();
        store.tasks = [defaultTask];
        store.baseUrl = baseUrl;

        moxios.stubRequest(baseUrl + '/' + defaultTask.uid, {
          status: 500,
        });

        await store.deleteTask({ task: defaultTask, index: 0 });
        expect(moxios.requests.mostRecent().url).toBe(baseUrl + '/' + defaultTask.uid);
        expect(store.tasks.length).toBe(1);
        expect(store.tasks[0]).toEqual(defaultTask);
      });
    });

    test('resetFilters', async () => {
      store.tasks = [new Task()];

      await store.resetFilters();
      expect(store.tasks.length).toBe(0);
    });

    test('resetInfiniteScroll', () => {
      const previousInfinityIdentifier = lodashCloneDeep(store.infinityIdentifier);
      store.resetInfiniteScroll();
      expect(store.infinityIdentifier).toBe(previousInfinityIdentifier + 1);
    });
  });
});
