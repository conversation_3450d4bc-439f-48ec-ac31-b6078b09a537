import { createP<PERSON>, setActive<PERSON>inia } from 'pinia';
import { useTenantConfig } from '../../stores/tenantConfig';

setActivePinia(createPinia());
const store = useTenantConfig();

describe('tenantConfig store', () => {
  beforeEach(() => {
    store.$reset();
  });

  test('defaults', () => {
    expect(store.awsConfig).toEqual('');
    expect(store.locale).toEqual(null);
    expect(store.permissions).toEqual({});
    expect(store.features).toEqual({});
  });

  describe('actions', () => {
    test('setAwsConfig sets new config correctly', () => {
      const mockEntry = {
        s3Url: 'https://edubase-sol2.s3-eu-west-1.amazonaws.com',
        s3Prefix: 'sol2-acc-app',
        cloudfrontUrl: 'https://tms-cdn-acc.bingel.be',
        maxUploadSize: 262144000,
        useCdn: false,
      };

      store.setAwsConfig(mockEntry);

      expect(store.awsConfig).toEqual(mockEntry);
    });

    test('setLocale sets new locale correctly', () => {
      const mockEntry = 'nl-be';

      store.setLocale(mockEntry);

      expect(store.locale).toEqual(mockEntry);
    });

    test('setPermissions sets permission object correctly', () => {
      const mockEntry = {
        has_access_to_all_groups: true,
        has_access_to_care: true,
        has_access_to_evaluation: true,
        has_access_to_planner: true,
        has_access_to_preschool: false,
        has_access_to_settings: true,
      };

      store.setPermissions(mockEntry);

      expect(store.permissions).toEqual(mockEntry);
    });

    test('setFeatures sets features object correctly', () => {
      const mockEntry = {
        active_feature: true,
      };

      store.setFeatures(mockEntry);

      expect(store.features).toEqual(mockEntry);
    });

    test('setSecurityExtensionWhitelist sets whitelist object correctly', () => {
      const mockEntry = {
        allowed_extensions: {
          'care-import-file': ['csv'],
          default: [],
        },
        max_upload_sizes: {
          'care-file': 20971520,
          default: 0,
        },
      };

      store.setSecurityExtensionWhitelist(mockEntry);

      expect(store.securityExtensionWhitelist).toEqual(mockEntry);
    });

    test('setInternalUrls sets internalUrls object correctly', () => {
      const mockEntry = {
        apiPlannerPrefix: '/sol/planner/rest/v2',
      };

      store.setInternalUrls(mockEntry);

      expect(store.internalUrls).toEqual(mockEntry);
    });
  });
});
