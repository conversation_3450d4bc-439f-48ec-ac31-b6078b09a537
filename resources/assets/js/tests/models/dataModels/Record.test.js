import Record from '../../../models/dataModels/Record';
import MockRecord from '../../mocks/planner/Record';
import Collection from '../../mocks/planner/Collection';
import axios from 'axios';

jest.mock('axios');

const collection = new Collection();

describe('Record', () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  it('sets the record data', () => {
    const mockRecord = new MockRecord();
    mockRecord.collectionId = null;
    mockRecord.localId = 1;
    mockRecord.parent = null;
    expect(new Record(null, mockRecord, false)).toEqual(mockRecord);
  });

  it('updates the record', async () => {
    const mockRecord = new MockRecord({ collection: collection.uid });
    const record = new Record(null, mockRecord, false);
    const localId = record.localId;

    axios.put.mockResolvedValueOnce({ data: { record: { uid: 'updated' } } });
    const savedRecord = await record.save({ lesson_steps: 'new steps' }, true);
    expect(axios.get).not.toHaveBeenCalled();
    expect(axios.put).toHaveBeenCalledWith(
      '/collections/' + collection.uid + '/records/' + mockRecord.id,
      getExpectedFormDataForMockRecord(mockRecord, { lesson_steps: 'new steps' })
    );
    mockRecord.uid = 'updated';
    mockRecord.collectionId = collection.uid;
    mockRecord.localId = localId;
    mockRecord.parent = null;
    expect(savedRecord).toEqual(mockRecord);
  });

  it('updates the record with only the available data', async () => {
    const record = new Record(
      null,
      { id: 'record-uid', collection: 'collection-uid', name: 'New Name' },
      false
    );
    const localId = record.localId;

    axios.put.mockResolvedValueOnce({ data: { record: { id: 'updated' }, overwrittenFields: [] } });
    const savedRecord = await record.save({ lesson_steps: 'new steps' }, true);
    expect(axios.get).not.toHaveBeenCalled();
    expect(axios.put).toHaveBeenCalledWith('/collections/collection-uid/records/record-uid', {
      name: 'New Name',
      lesson_steps: 'new steps',
    });
    const expectedRecord = new Record(null, {
      id: 'updated',
      collection: 'collection-uid',
      parent: null,
      name: 'New Name',
      overwrittenFields: [],
    });
    expectedRecord.localId = localId;
    expect(savedRecord).toEqual(expectedRecord);
  });

  it('updates the record data in a calendar item row', async () => {
    const mockRecord = new MockRecord({ collection: null, uid: null, calendarItemRowId: 'rowUid' });
    const record = new Record(null, mockRecord, false);
    const localId = record.localId;

    axios.put.mockResolvedValueOnce({ data: { record: { uid: 'updated' } } });
    const savedRecord = await record.save({ lesson_steps: 'new steps' }, true);
    expect(axios.get).not.toHaveBeenCalled();
    expect(axios.put).toHaveBeenCalledWith(
      '/collections/calendaritemrows/rowUid/record',
      getExpectedFormDataForMockRecord(mockRecord, { lesson_steps: 'new steps' })
    );
    mockRecord.uid = 'updated';
    mockRecord.collectionId = null;
    mockRecord.localId = localId;
    mockRecord.parent = null;
    expect(savedRecord).toEqual(mockRecord);
  });

  it('updates the record to collection from a calendar item row', async () => {
    const mockRecord = new MockRecord({ collection: null, uid: null, calendarItemRowId: 'rowUid' });
    const record = new Record(null, mockRecord, false);
    const localId = record.localId;
    const collectionId = 'uidFromTheCollection';
    const recordId = 'uidFromTheRecord';

    axios.put.mockResolvedValueOnce({ data: { record: { name: 'someName' } } });
    const savedRecord = await record.save(
      { lesson_steps: 'new steps' },
      {
        collectionId,
        recordId,
      }
    );
    expect(axios.get).not.toHaveBeenCalled();
    expect(axios.put).toHaveBeenCalledWith(
      '/collections/' + collectionId + '/records/' + recordId,
      getExpectedFormDataForMockRecord(mockRecord, { lesson_steps: 'new steps' })
    );
    mockRecord.name = 'someName';
    mockRecord.collectionId = null;
    mockRecord.localId = localId;
    mockRecord.parent = null;
    expect(savedRecord).toEqual(mockRecord);
  });
});

function getExpectedFormDataForMockRecord(mockRecord, data = {}) {
  return {
    name: mockRecord.name,
    purpose: mockRecord.purpose,
    start_situation: mockRecord.start_situation,
    duration_minutes: mockRecord.duration_minutes,
    manual: mockRecord.manual,
    curriculumGoals: mockRecord.curriculumGoals,
    teacher_goals: mockRecord.teacher_goals,
    materialList: mockRecord.materialList,
    lesson_steps: mockRecord.lesson_steps,
    tips: mockRecord.tips,
    work_methods: mockRecord.work_methods,
    differentiation: mockRecord.differentiation,
    experienceSituations: mockRecord.experienceSituations,
    instruction_movie_url_1: mockRecord.instruction_movie_url_1,
    instruction_movie_url_2: mockRecord.instruction_movie_url_2,
    instruction_movie_url_3: mockRecord.instruction_movie_url_3,
    is_duration_applicable: true,
    boardbook_url: mockRecord.boardbook_url,
    ...data,
  };
}
