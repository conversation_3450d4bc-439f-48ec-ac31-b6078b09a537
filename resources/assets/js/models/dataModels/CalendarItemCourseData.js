import axios from 'axios';
import lodash from 'lodash';
import CalendarItemCourseDataRow from './CalendarItemCourseDataRow';

export default class CalendarItemCourseData {
  constructor(calendarItem, data) {
    this.calendarItem = calendarItem;
    this.updateProperties(data);
    this.rows = (data &&
      data.rows &&
      data.rows.length > 0 &&
      data.rows.map((rowData) => {
        return new CalendarItemCourseDataRow(this, calendarItem, rowData);
      })) || [new CalendarItemCourseDataRow(this, calendarItem, {})];
  }

  updateProperties(data) {
    this.subjectId = data?.subject || data?.subjectId;
    this.rows = data?.rows || this.rows || [];
  }

  addBackendTypeDataTo(data) {
    data.subject = this.subjectId;
  }

  addRow(data) {
    const row = new CalendarItemCourseDataRow(this, this.calendarItem, data);
    this.rows.push(row);
    return row;
  }

  removeRow(localId) {
    this.rows = this.rows.filter((row) => {
      return row.localId !== localId;
    });
    if (this.rows.length === 0) {
      this.rows.push(new CalendarItemCourseDataRow(this, this.calendarItem, {}));
    }
  }

  removeEmptyRows() {
    lodash.each(lodash.map(this.rows), (element) => {
      if (!element.title) {
        if (!element.id) {
          this.removeRow(element.localId);
        }
        element.resetTitleToLastSavedTitle();
      }
    });
  }

  async updateRecord(oldRecordId, newRecord) {
    return axios.all(
      lodash(this.rows)
        .filter({ id: newRecord.calendarItemRowId })
        .map(async (row) => {
          row.recordId = newRecord.id;
          row.collectionId = newRecord.collection;

          await row.updateRecord(newRecord).then(() => this.calendarItem.save());

          return this.calendarItem.typeData.rows.filter(
            (row) => row.originalId === newRecord.calendarItemRowId
          )[0];
        })
        .value()
    );
  }

  hasContent() {
    return lodash.some(this.rows, 'title');
  }

  getRowsUrl() {
    return this.calendarItem.getUrl() + '/rows';
  }
}
