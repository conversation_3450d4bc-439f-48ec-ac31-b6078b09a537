import lodash from 'lodash';
import ColorMode from './ColorMode';

const VIEW_MODE = {
  MY_CALENDAR: {
    name: 'MY_CALENDAR',
    autoColorMode: ColorMode.COURSE,
  },
  MY_COLLEAGUES: {
    name: 'MY_COLLEAGUES',
    autoColorMode: ColorMode.CALENDAR,
  },
  MY_GROUPS: {
    name: 'MY_GROUPS',
    autoColorMode: ColorMode.CALENDAR,
  },
};

VIEW_MODE.ALL = lodash.values(VIEW_MODE);

VIEW_MODE.get = function (name) {
  return VIEW_MODE[name] || VIEW_MODE.MY_CALENDAR;
};

export default VIEW_MODE;
