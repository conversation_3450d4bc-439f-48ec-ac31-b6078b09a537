import axios from 'axios';
import lodash from 'lodash';

export default class coreService {
  static getResponseData(response) {
    return response.data;
  }

  static getResponseId(response) {
    return lodash(response.headers['content-location']).split('/').last();
  }

  static extractResponseIdInto(entity) {
    const self = this;
    return function (response) {
      entity.id = self.getResponseId(response);
      return entity;
    };
  }

  static extractProperty(property) {
    return function (entity) {
      return entity[property];
    };
  }

  static transformAll(transformation) {
    return function (data) {
      return lodash.map(data, transformation);
    };
  }

  static enrichAll(enrichment) {
    return function (data) {
      return axios.all(lodash.map(data, enrichment));
    };
  }

  static terminalEventHandler() {
    const fn = arguments[0];
    const args = lodash.tail(arguments);
    return function (event) {
      fn.apply(event, args.concat(event));
      event.preventDefault();
      if (event.stopPropagation) {
        event.stopPropagation();
      }
    };
  }
}
