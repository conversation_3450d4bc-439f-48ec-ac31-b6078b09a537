const elementSelector = 'td:not(.no-hover), th:not(.no-hover)';

let hoverClass = '';
let originalElement = null;

function setElementClass(event, addClass) {
  const columnIndex = event.target.cellIndex + 1;
  const tableElement = originalElement.getElementsByTagName('table')[0];
  const columnElements = tableElement.querySelectorAll(
    `td:not(.no-hover):nth-child(${columnIndex}), th:not(.no-hover):nth-child(${columnIndex})`
  );
  for (let i = 0; i < columnElements.length; i++) {
    if (addClass) {
      columnElements[i].classList.add(hoverClass);
    } else {
      columnElements[i].classList.remove(hoverClass);
    }
  }
}

function handleMouseEnter(event) {
  setElementClass(event, true);
}

function handleMouseLeave(event) {
  setElementClass(event, false);
}

function bindEventListeners() {
  const tdElements = originalElement.querySelectorAll(elementSelector);
  for (let i = 0; i < tdElements.length; i++) {
    tdElements[i].addEventListener('mouseenter', handleMouseEnter);
    tdElements[i].addEventListener('mouseleave', handleMouseLeave);
  }
}

function removeEventListeners() {
  const tdElements = originalElement.querySelectorAll(elementSelector);
  for (let i = 0; i < tdElements.length; i++) {
    tdElements[i].removeEventListener('mouseenter', handleMouseEnter);
    tdElements[i].removeEventListener('mouseleave', handleMouseLeave);
  }
}

function resetEventListeners() {
  removeEventListeners();
  bindEventListeners();
}

export default {
  name: 'table-vertical-hover',
  beforeMount(element, binding) {
    const vm = binding.instance;
    hoverClass = binding.value;
    originalElement = element;

    vm.$nextTick(() => {
      bindEventListeners();
    });

    originalElement.addEventListener('recalculate', resetEventListeners);
  },
  beforeUnmount: function () {
    removeEventListeners();
  },
  retrigger() {
    resetEventListeners();
  },
};
