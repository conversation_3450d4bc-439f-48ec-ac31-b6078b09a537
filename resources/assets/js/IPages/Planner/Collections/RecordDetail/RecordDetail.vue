<template>
  <Form ref="validationObserver" :initial-values="formData" @vue:mounted="initValidationObserver">
    <LeavePageWarning :showMessage="isDirty" />
    <Layout>
      <template v-slot:navbar-top-left>
        <div class="d-flex align-center">
          <v-btn icon="fa:fa fa-chevron-left" class="mr-4" @click="back" size="small"> </v-btn>
          <template v-if="!loading">
            <EllipsisTooltip>
              {{ context.title }}
            </EllipsisTooltip>
            <ShareStatusChipGeneral class="ml-2" />
          </template>
        </div>
      </template>

      <template v-slot:navbar-top-right>
        <v-select
          v-if="
            !loading &&
            !!collectionModel &&
            collectionModel.chapters[0] &&
            collectionModel.chapters[0].records.length
          "
          :items="collectionModel.chapters[0].records"
          :model-value="recordModel.id"
          @update:model-value="changeRecord"
          item-value="id"
          item-title="name"
          variant="solo"
          density="compact"
          class="mr-2"
          style="min-width: 0"
          :menu-props="{ offsetY: true }"
          hide-details
        />
        <RecordDetailSaveOptions
          :saveCalendarRecordToCollectionUrl="saveCalendarRecordToCollectionUrl"
          :canSaveOnCollection="canSaveOnCollection"
          :recordId="recordId"
          :collectionId="collectionId"
        />
      </template>

      <v-fade-transition>
        <v-container v-if="loading" :fluid="true">
          <div class="text-center">
            <v-progress-circular
              color="primary"
              :width="5"
              size="50"
              :indeterminate="true"
              style="margin: auto"
            ></v-progress-circular>
          </div>
        </v-container>

        <v-container v-else :fluid="true">
          <RecordDetailCalendarItemList />
          <RecordDetailForm />
        </v-container>
      </v-fade-transition>
    </Layout>
  </Form>
</template>

<script>
import Layout from '../../../Common/Layout.vue';
import LeavePageWarning from '../../../../components/LeavePageWarning.vue';
import { mapActions, mapState } from 'pinia';
import RecordDetailSaveOptions from './partials/RecordDetailSaveOptions.vue';
import RecordDetailCalendarItemList from './partials/RecordDetailCalendarItemList.vue';
import RecordDetailForm from './partials/RecordDetailForm.vue';
import EllipsisTooltip from '../../../../components/EllipsisTooltip.vue';
import ShareStatusChipGeneral from '../../../../components/ShareStatusChipGeneral.vue';
import { Form } from 'vee-validate';
import { useCollectionRecordStore } from '../../../../stores/collectionRecordStore';
import { useTenantConfig } from '../../../../stores/tenantConfig';
import { capitalize } from '../../../../helpers/stringHelpers.js';

export default {
  name: 'RecordDetail',

  components: {
    Form,
    ShareStatusChipGeneral,
    EllipsisTooltip,
    RecordDetailForm,
    RecordDetailCalendarItemList,
    RecordDetailSaveOptions,
    LeavePageWarning,
    Layout,
  },

  props: {
    record: { required: true, type: Object },
    networks: { required: true, type: Array },
    careTypeOptions: { required: true, type: Array },
    goals: { required: true, type: Array },
    originalGoals: { required: false, type: Array, default: () => [] },
    backUrl: { required: false, type: String },
    collection: { required: false, type: Object },
    calendarItem: { required: false, type: Object },
    canEdit: { required: true, type: Boolean },
    calendarItemRowUid: { required: false, type: String },
    shareStatus: { required: false, type: String },
    overwrittenFields: { required: false, type: Array, default: () => [] },
    originalRecord: { required: false, type: Object },
    linkedCalendarItems: { required: false, type: Array },
    saveCalendarRecordToCollectionUrl: { required: false, type: String },
    canSaveOnCollection: { required: false, type: Boolean },
    recordId: { type: String, required: false },
    collectionId: { type: String, required: false },
    zillVersion: { required: true, type: [String, null] },
  },

  watch: {
    computedTitle: {
      immediate: true,
      handler() {
        this.setPageTitle(this.computedTitle);
      },
    },
    record: function () {
      this.setRecordModel(this.record);
      if (this.calendarItemRowUid) {
        this.setCalendarItemRowUid(this.calendarItemRowUid);
      }
    },
  },

  computed: {
    ...mapState(useCollectionRecordStore, [
      'context',
      'loading',
      'collectionModel',
      'recordModel',
      'validationObserver',
      'formData',
    ]),

    isDirty() {
      return this.validationObserver?.getMeta()?.dirty;
    },

    computedTitle() {
      let title = this.$t('labels.modules.planner.collections.detail.record');

      if (this.recordModel) {
        title += ' - ' + this.recordModel.name;
      }

      return this.capitalize(title);
    },
  },

  async mounted() {
    if (this.collection) {
      this.setCollectionModel(this.collection);
    }

    if (this.calendarItem) {
      this.setCalendarItemModel({ ...this.calendarItem, shareStatus: this.shareStatus });
    }

    this.setCanEditItem(this.canEdit);
    this.setOverwrittenFields(this.overwrittenFields);
    this.setOriginalRecord(this.originalRecord);
    this.setRecordModel(this.record);
    this.setNetworks(this.networks);
    this.setZillVersion(this.zillVersion);
    this.setCareTypeOptions(this.careTypeOptions);
    this.setContext();
    this.setGoals(this.goals);
    this.setOriginalGoals(this.originalGoals);

    if (this.calendarItemRowUid) {
      this.setCalendarItemRowUid(this.calendarItemRowUid);
    }
    this.setLinkedCalendarItems(this.linkedCalendarItems);
    this.setLoading(false);
  },

  methods: {
    ...mapActions(useTenantConfig, ['setPageTitle']),

    ...mapActions(useCollectionRecordStore, [
      'setLoading',
      'setCollectionModel',
      'setRecordModel',
      'setCalendarItemModel',
      'setCanEditItem',
      'setOverwrittenFields',
      'setOriginalRecord',
      'setNetworks',
      'setContext',
      'setCareTypeOptions',
      'setValidationObserver',
      'setGoals',
      'setOriginalGoals',
      'setCalendarItemRowUid',
      'setLinkedCalendarItems',
      'setZillVersion',
    ]),

    capitalize,

    back() {
      if (this.backUrl) {
        window.location.assign(this.backUrl);
      } else {
        window.history.back();
      }
    },

    changeRecord(record) {
      const newUrl = window.location.pathname.split('/').slice(0, -1).join('/') + '/' + record;
      window.location.assign(newUrl);
    },

    initValidationObserver() {
      this.setValidationObserver(this.$refs.validationObserver);
    },
  },
};
</script>
