<template>
  <RecordDetailFormGroup
    v-if="canEditItem || formData[name]"
    :label="label"
    :name="name"
    :form-data="formData"
  >
    <p v-if="!canEditItem" class="control-label" v-html="formData[name]"></p>
    <v-card v-else>
      <VEditor v-model="formData[name]" :small="true" :height-max="500"> </VEditor>
    </v-card>
    <slot></slot>
    <OriginalRecordDataHolder :name="name" :title="label" :isRichText="true" />
  </RecordDetailFormGroup>
</template>

<script>
import RecordDetailFormGroup from '../RecordDetailFormGroup.vue';
import VEditor from '../../../../../../components/VEditor.vue';
import { mapState } from 'pinia';
import OriginalRecordDataHolder from './OriginalRecordDataHolder.vue';
import { useCollectionRecordStore } from '../../../../../../stores/collectionRecordStore';

export default {
  name: 'RecordEditorField',

  components: {
    OriginalRecordDataHolder,
    VEditor,
    RecordDetailFormGroup,
  },

  props: {
    label: { required: true, type: String },
    name: { required: true, type: String },
  },

  computed: {
    ...mapState(useCollectionRecordStore, ['canEditItem', 'formData']),
  },
};
</script>
