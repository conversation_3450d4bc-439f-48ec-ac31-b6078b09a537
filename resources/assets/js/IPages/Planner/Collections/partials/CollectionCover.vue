<template>
  <v-col
    class="d-flex flex-column collectionCover"
    @mouseenter="hoverState = true"
    @mouseleave="hoverState = false"
    @focusin="hoverState = true"
    @focusout="handleFocusOut"
  >
    <v-card
      class="flex d-flex flex-column"
      :class="cardIsDisabled && 'disabled-card'"
      :id="'card_' + collection.id"
      :ripple="!cardIsDisabled"
      @click.stop.prevent="visitCollection"
      @keyup.enter="visitCollection"
      @keyup.space="visitCollection"
      tabindex="0"
    >
      <v-img
        :src="cover"
        :cover="true"
        :lazySrc="collection.fallbackCover"
        class="collectionImage"
        :class="isDeactivated ? 'disabled' : ''"
        aspectRatio="1.2"
        :key="collection.id + '_image'"
      >
        <template v-slot:placeholder>
          <v-row class="fill-height ma-0" align="center" justify="center">
            <v-progress-circular :indeterminate="true" color="grey-lighten-5"></v-progress-circular>
          </v-row>
        </template>

        <div class="collectionOverlay">
          <template v-if="showFavourite(hoverState) && !isDeactivated">
            <v-hover v-slot="{ isHovering, props }">
              <v-btn
                class="favButton mt-2 ml-2 hoverElement"
                color="white"
                :class="isHovering || collection.favourited ? 'text-orange' : 'text-black'"
                :icon="isHovering || collection.favourited ? 'fa:fas fa-star' : 'fa:far fa-star'"
                size="x-small"
                :absolute="true"
                @click.stop.prevent="toggleFavourite"
                @keyup.enter.capture.stop.prevent
                @keyup.space.capture.stop.prevent="toggleFavourite"
                v-bind="props"
              >
              </v-btn>
            </v-hover>
          </template>

          <CollectionCoverOptions
            v-if="hoverState && showCollectionOptions"
            :collection="collection"
            :collectionType="collectionType"
            :key="collection.id + '_options'"
            :card-element="'#card_' + collection.id"
          ></CollectionCoverOptions>
          <CollectionCoverPublisherNotifications
            v-if="!showCollectionOptions"
            :collection="collection"
            :collectionType="collectionType"
            :key="collection.id + '_publisher_notifications'"
            :hover="hoverState"
          >
          </CollectionCoverPublisherNotifications>
        </div>
      </v-img>

      <CollectionCoverBody
        :collection="collection"
        :collectionType="collectionType"
        :hover="hoverState"
      />
    </v-card>
  </v-col>
</template>

<script>
import Collection from '../../../../models/dataModels/Collection';
import CollectionCoverOptions from './CollectionCoverOptions.vue';
import CollectionCoverPublisherNotifications from './CollectionCoverPublisherNotifications.vue';
import CollectionCoverBody from './CollectionCoverBody.vue';
import CollectionType from '../dataModels/CollectionType';

export default {
  name: 'CollectionCover',

  components: {
    CollectionCoverOptions,
    CollectionCoverPublisherNotifications,
    CollectionCoverBody,
  },

  data() {
    return {
      hoverState: false,
    };
  },

  props: {
    collection: { type: Collection, required: true },
    collectionType: { type: Object, required: true },
  },

  computed: {
    cover() {
      return this.collection.cover || this.collection.fallbackCover;
    },

    showCollectionOptions() {
      return this.collectionType.name !== CollectionType.LIBRARY.name;
    },

    isDeactivated() {
      return this.collection.isDeactivated;
    },

    cardIsDisabled() {
      return this.collectionType.name === CollectionType.LIBRARY.name || this.isDeactivated;
    },
  },

  methods: {
    visitCollection() {
      if (this.cardIsDisabled) {
        return false;
      }
      window.location.assign('/collections/collection/' + this.collection.id);
    },

    toggleFavourite() {
      this.collection.toggleFavourite();
    },

    showFavourite(hover) {
      return (
        this.collectionType.name !== CollectionType.LIBRARY.name &&
        (hover || this.collection.favourited)
      );
    },

    handleFocusOut(event) {
      if (!event.currentTarget.contains(event.relatedTarget)) {
        this.hoverState = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.collectionCover {
  position: relative;
  transition: transform 0.2s;

  .collectionOverlay {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: 10;
  }

  &:hover,
  &:focus,
  &:focus-within {
    transform: scale(1.05);
  }
}

.collectionImage {
  flex: 0 0 auto;

  :deep(img) {
    object-position: top center;
  }
}

.disabled :deep(.v-image__image) {
  opacity: 0.4;
}

@media (min-width: 1904px) {
  .v-col-xl-2_4 {
    flex: 0 0 20%;
    max-width: 20%;
  }
}

.disabled-card {
  cursor: inherit;
  user-select: none;
}

.disabled-card.v-card--link:focus:before {
  opacity: 0;
}
</style>
