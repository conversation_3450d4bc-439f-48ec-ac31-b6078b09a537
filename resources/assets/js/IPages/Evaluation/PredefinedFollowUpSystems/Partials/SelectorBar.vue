<template>
  <div v-bind="$attrs">
    <Tabs
      :tabs="subTypes"
      v-model="selectedSubType.name"
      name-value="name"
      key-value="name"
      link-value="link"
      :mobile-breakpoint="20"
    ></Tabs>
  </div>
</template>

<script>
import Tabs from '../../../../components/Tabs';

export default {
  components: {
    Tabs,
  },
  props: {
    selectedSubType: Object,
    subTypes: Array,
  },
};
</script>
