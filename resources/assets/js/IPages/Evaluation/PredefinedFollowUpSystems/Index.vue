<template>
  <FollowUpSystemWrapper>
    <template v-slot:toolbar>
      <div v-if="subTypes.length > 1">
        <v-spacer></v-spacer>
        <SelectorBar :sub-types="subTypes" :selected-sub-type="subType"></SelectorBar>
      </div>
    </template>

    <v-container :fluid="true" class="min-width pa-0" :key="followUpSystem.uid">
      <v-row v-for="(warning, warningIndex) in warnings" :key="warningIndex">
        <v-col cols="12">
          <v-alert type="error" variant="outlined">
            {{ warning }}
          </v-alert>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12">
          <v-card>
            <FollowUpSystemTable
              :is-editable="editable"
              :follow-up-system="followUpSystem"
              :is-vclb="isVclb"
              :percentile-mapping="percentileMapping"
              :percentile-mapping-nn="percentileMappingNn"
              :percentile-aware="percentileAware"
              :zone-mapping="zoneMapping"
              :zone-name="zoneName"
              :test-moments="testMoments"
              :table-data="pupils"
              :divide-percentile-by-hundred="dividePercentileByHundred"
              :score-base-url="baseUrl"
              :comment-base-url="commentBaseUrl"
            ></FollowUpSystemTable>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </FollowUpSystemWrapper>
</template>

<script>
import FollowUpSystemTable from '../../../pages/follow-up-systems/components/FollowUpSystemTable.vue';
import Layout from '../../Common/Layout.vue';
import FollowUpSystemWrapper from '../FollowUpSystems/FollowUpSystemWrapper';
import SelectorBar from './Partials/SelectorBar';
import { mapActions } from 'pinia';
import { useTenantConfig } from '../../../stores/tenantConfig';

export default {
  components: {
    FollowUpSystemWrapper,
    FollowUpSystemTable,
    SelectorBar,
  },

  layout: (h, page) => h(Layout, () => [page]),

  props: {
    warnings: Array,
    editable: Boolean,
    followUpSystem: Object,
    isVclb: Boolean,
    percentileMapping: Object,
    percentileMappingNn: Object,
    percentileAware: Boolean,
    zoneMapping: Object,
    zoneName: String,
    testMoments: Array,
    dividePercentileByHundred: Boolean,
    pupils: Array,
    baseUrl: String,
    commentBaseUrl: String,
    subTypes: Array,
    subType: Object,
  },

  mounted() {
    this.setPageTitle(this.$t('titles.evaluation') + ' - ' + this.followUpSystem.name);
  },

  methods: {
    ...mapActions(useTenantConfig, ['setPageTitle']),
  },
};
</script>
