<template>
  <EvaluationDialog
    :loading="loading"
    :row-label="isPupilView ? $t('labels.goal') : $t('labels.pupils')"
    :bulk-comment-label="
      isPupilView
        ? $t('labels.bulk-comments.feedback-all-goals')
        : $t('labels.bulk-comments.feedback-all-pupils')
    "
    :rows="items"
    :show-history-column="showCommentsColumn || showEvaluationColumn"
    :show-quotation-column="showEvaluationColumn"
    :show-comments-column="showCommentsColumn"
    :quotation-system="quotationSystem"
    :quotation-history="followUpSystemGoalQuotations"
    :comment-validation-rules="commentValidationRules"
    :read-only="periodIsExpired"
    @update-item-quotation="changeItemQuotation"
    @update-item-comment="changeItemComment"
    @update-bulk-item-quotation="changeBulkItemQuotation"
    @update-bulk-item-comment="changeBulkItemComment"
    ref="evaluationDialog"
  >
    <template v-if="targetAudiences && targetAudiences.length" v-slot:toolbar-prepend>
      <TargetAudienceSelect
        :target-audiences="targetAudiences"
        :model-value="targetAudience"
        @update:model-value="$emit('update:targetAudience', $event)"
      />
    </template>
    <template v-slot:toolbar-title>
      <div class="d-flex align-center">
        <v-btn
          :style="{
            opacity: previousSibling ? 1 : 0,
            cursor: previousSibling ? 'pointer' : 'default',
          }"
          icon="chevron_left"
          size="small"
          @click.stop="previousSibling && goToPreviousSibling()"
        >
        </v-btn>
        <v-toolbar-title class="pl-0 ml-0 text-center">
          <span v-if="isPupilView">{{ title }}</span>
          <span v-else v-html="title"></span>
        </v-toolbar-title>
        <v-btn
          :style="{
            opacity: nextSibling ? 1 : 0,
            cursor: nextSibling ? 'pointer' : 'default',
          }"
          icon="chevron_right"
          size="small"
          @click.stop="nextSibling && goToNextSibling()"
        >
        </v-btn>
      </div>
    </template>
  </EvaluationDialog>
</template>

<script>
import axios from 'axios';
import EvaluationDialog from './EvaluationDialog.vue';
import TargetAudienceSelect from './TargetAudienceSelect.vue';

export default {
  name: 'FollowUpSystemQuotationDialog',

  components: { TargetAudienceSelect, EvaluationDialog },

  props: {
    fetchBaseUrl: { required: true, type: String },
    getFetchUrl: { required: true, type: Function },
    commentBaseUrl: { required: true, type: String },
    quotationBaseUrl: { required: true, type: String },
    quotationSyncUrl: { required: true, type: String },
    commentSyncUrl: { required: false, type: String },
    quotationSystem: { required: true, type: Object },
    commentValidationRules: { required: true, type: Object },
    reportPeriod: { required: true, type: Object },
    periodIsExpired: { required: true, type: Boolean },
    targetAudiences: { required: false, type: Array, default: () => [] },
    targetAudience: Object,
  },

  data() {
    return {
      loading: false,
      goals: [],
      selectedInputMoment: null,
      siblings: [],
      pupils: [],
      followUpSystemGoalQuotations: {},
      isPupilView: false,
    };
  },

  computed: {
    selectedItem() {
      return this.isPupilView ? this.pupils[0] : this.goals[0];
    },
    items() {
      return this.isPupilView ? this.goals : this.pupils;
    },
    title() {
      if (!this.selectedItem) {
        return null;
      }
      if (this.isPupilView) {
        return (
          this.selectedItem.fullname +
          (this.selectedItem.parent ? ' - ' + this.selectedItem.parent.description : '') +
          ' - ' +
          this.selectedInputMoment.name
        );
      }
      return this.selectedItem.description + ' - ' + this.selectedInputMoment.name || null;
    },
    previousSibling() {
      return this.siblings[this.selectedItem.index - 1] || null;
    },
    nextSibling() {
      return this.siblings[this.selectedItem.index + 1] || null;
    },
    showEvaluationColumn() {
      return this.isPupilView
        ? this.items.some((item) => item.is_evaluable)
        : this.selectedItem && this.selectedItem.is_evaluable;
    },
    showCommentsColumn() {
      return this.isPupilView
        ? this.items.some((item) => item.is_commentable)
        : this.selectedItem && this.selectedItem.is_commentable;
    },
  },

  methods: {
    openDialog(goals, pupils, inputMoment, siblings, isPupilView) {
      this.loading = true;
      this.goals = goals;
      this.pupils = pupils;
      this.selectedInputMoment = inputMoment;
      this.siblings = siblings;
      this.isPupilView = isPupilView;
      this.$refs.evaluationDialog.openDialog();
      this.fetchDialogData();
    },
    updateGoals(goals) {
      this.goals = goals;
      this.fetchDialogData();
    },
    fetchDialogData() {
      if (!this.selectedItem || !this.selectedInputMoment) return;
      this.loading = true;
      const url = this.getFetchUrl(this.fetchBaseUrl, this.selectedItem, this.selectedInputMoment);
      const req = this.isPupilView
        ? axios.get(url)
        : axios.post(url, { pupils: this.pupils.map((p) => p.uid) });

      req
        .then((response) => this.setDialogData(response))
        .finally(() => {
          this.loading = false;
        });
    },
    setDialogData(response) {
      const existingFollowUpSystemGoalQuotations = response.data.quotations || {};
      const existingFollowUpSystemGoalComments = response.data.comments || {};
      const history = response.data.history || {};

      this.followUpSystemGoalQuotations = Array.isArray(history) ? {} : history;
      this.items.forEach((item) => {
        const existingQuotation = existingFollowUpSystemGoalQuotations[item.uid] || null;
        const existingComment = existingFollowUpSystemGoalComments[item.uid] || null;

        item.description = this.isPupilView ? item.description : item.fullname;
        item.isRichText = this.isPupilView;

        item.quotation_uid = existingQuotation ? existingQuotation.quotation.uid : null;
        item.comment = existingComment ? existingComment.comment : null;

        const row = this.isPupilView ? item : this.selectedItem;
        item.showHistoryColumn = row.is_evaluable || row.is_commentable;
        item.showQuotationColumn = row.is_evaluable;
        item.showCommentsColumn = row.is_commentable;
        item.disabled = this.isDisabled(item);
      });
      this.loading = false;
    },
    changeItemQuotation({ item, quotation }) {
      const url = this.quotationBaseUrl
        .replace('#pupilUid#', this.getPupil(item).uid)
        .replace('#followUpSystemGoalUid#', this.getFollowUpSystemGoal(item).uid)
        .replace('#followUpSystemInputMoment#', this.selectedInputMoment.uid);

      return axios.post(url, { quotation: quotation.uid }).then(() => this.emitHasSelections());
    },
    changeItemComment({ item, comment }) {
      const url = this.commentBaseUrl
        .replace('#pupilUid#', this.getPupil(item).uid)
        .replace('#followUpSystemGoalUid#', this.getFollowUpSystemGoal(item).uid)
        .replace('#followUpSystemInputMoment#', this.selectedInputMoment.uid);

      return axios
        .post(url, {
          comment: comment,
          report_period: this.reportPeriod,
        })
        .then(() => this.emitHasComments());
    },
    changeBulkItemQuotation({ items, quotation }) {
      const url = this.quotationSyncUrl.replace(
        '#followUpSystemInputMoment#',
        this.selectedInputMoment.uid
      );
      const itemUids = items.map((item) => item.uid);
      const params = {
        quotation: quotation.uid,
        report_period: this.reportPeriod,
        pupils: this.isPupilView ? [this.pupils[0].uid] : itemUids,
        follow_up_system_goals: this.isPupilView ? itemUids : [this.selectedItem.uid],
      };

      return axios.post(url, params).then(() => this.emitHasSelections());
    },
    changeBulkItemComment({ items, comment }) {
      const url = this.commentSyncUrl.replace(
        '#followUpSystemInputMoment#',
        this.selectedInputMoment.uid
      );
      const itemUids = items.map((item) => item.uid);
      const params = {
        comment: comment,
        report_period: this.reportPeriod,
        pupils: this.isPupilView ? [this.pupils[0].uid] : itemUids,
        follow_up_system_goals: this.isPupilView ? itemUids : [this.selectedItem.uid],
      };

      return axios.post(url, params).then(() => this.emitHasComments());
    },
    isDisabled(item) {
      if (this.isPupilView) {
        return item.parent ? item.parent.is_archived : item.is_archived || false;
      }

      return item.disabled;
    },
    getPupil(item) {
      return this.isPupilView ? this.pupils[0] : item;
    },
    getFollowUpSystemGoal(item) {
      return this.isPupilView ? item : this.selectedItem;
    },
    goToNextSibling() {
      return this.goToSibling(this.nextSibling);
    },
    goToPreviousSibling() {
      return this.goToSibling(this.previousSibling);
    },
    goToSibling(sibling) {
      return this.openDialog(
        this.isPupilView ? this.goals : [sibling],
        this.isPupilView ? [sibling] : this.pupils,
        this.selectedInputMoment,
        this.siblings,
        this.isPupilView
      );
    },
    emitHasSelections() {
      this.$emit('hasSelections', {
        itemUid: this.selectedItem.uid,
        inputMomentUid: this.selectedInputMoment.uid,
        hasSelections: this.items.some((item) => item.quotation_uid && !item.disabled),
      });
    },
    emitHasComments() {
      this.$emit('hasComments', {
        itemUid: this.selectedItem.uid,
        inputMomentUid: this.selectedInputMoment.uid,
        hasComments: this.items.some((item) => item.comment && !item.disabled),
      });
    },
  },
};
</script>
