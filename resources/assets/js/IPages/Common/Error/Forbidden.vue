<template>
  <Error :status-code="statusCode" :custom-title="customTitle">
    <template v-slot:description>
      <p>
        {{ $t('errors.403.body.paragraph1') }}
      </p>
      <p>
        {{ $t('errors.403.body.paragraph2.prefix') }}
        <a
          :href="$t('errors.403.body.paragraph2.link')"
          target="_blank"
          rel="noreferrer noopener"
          style="white-space: nowrap"
        >
          {{ $t('errors.403.body.paragraph2.link_description') }}
        </a>
        {{ $t('errors.403.body.paragraph2.postfix') }}
      </p>
    </template>
  </Error>
</template>

<script>
import Layout from '../Layout.vue';
import Error from './Error.vue';

export default {
  layout: (h, page) => h(Layout, () => [page]),
  components: {
    Error,
  },
  props: {
    statusCode: Number,
    customTitle: String,
  },
};
</script>
