<template>
  <ul style="list-style: none; padding-left: 0">
    <TreeViewItem
      v-for="item in treeData"
      :key="item.uid"
      class="item"
      :model="item"
      :selected-uid="selectedUid"
      :get-children="getChildren"
      :go-to-element="goToElement"
    >
    </TreeViewItem>
  </ul>
</template>

<script>
import TreeViewItem from './TreeViewItem';

export default {
  name: 'TreeView',
  components: {
    TreeViewItem,
  },
  props: {
    treeData: {
      required: true,
      type: Array,
    },
    selectedUid: {
      required: true,
    },
    getChildren: {
      required: true,
    },
    goToElement: {
      required: true,
    },
  },
};
</script>
