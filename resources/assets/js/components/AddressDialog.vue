<template>
  <v-dialog v-model="dialogOpen" max-width="300px">
    <template v-slot:activator="{ props }">
      <v-btn icon="home" v-bind="props"></v-btn>
    </template>
    <v-card>
      <v-card-title style="word-break: keep-all">
        {{ userData.name }} - {{ $t('labels.address.address') }}
      </v-card-title>
      <v-card-text>
        <p>
          {{ userData.address.street }} {{ userData.address.number }} {{ userData.address.bus
          }}<br />
          {{ userData.address.postalcode }} {{ userData.address.city }}<br />
          {{ userData.address.country }}
        </p>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="primary" variant="text" @click="dialogOpen = false">
          {{ $t('labels.close') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'AddressDialog',

  props: {
    userData: { required: true, type: Object },
  },

  data() {
    return {
      dialogOpen: false,
    };
  },
};
</script>
