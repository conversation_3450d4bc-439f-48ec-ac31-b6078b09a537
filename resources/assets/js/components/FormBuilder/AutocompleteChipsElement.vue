<template>
  <Field
    :slim="true"
    :name="element.model"
    :rules="validations"
    v-slot="{ errors, handleChange, handleBlur }"
    ref="validationProvider"
    v-model="parentModel[element.model]"
    @vue:mounted="initValidationProvider"
  >
    <AutocompleteChips
      :label="element.label || ''"
      v-model="parentModel[element.model]"
      @change="(event) => sendUpdate(event, handleChange)"
      @blur="handleBlur"
      :disabled="element.disabled"
      :placeholder="element.placeholder"
      :options="element.options"
      :item-value="element.itemValue"
      :item-text="element.itemText"
      v-bind="{ ...$attrs }"
      :select-classes="selectClasses"
      :error-messages="errors"
      :remap-value="element.remapValue || null"
    >
      <template v-if="hasSlot('chips')" v-slot:chips="data">
        <slot
          name="chips"
          :selected-value="data.selectedValue"
          :full-chips="data.fullChips"
          :remove-element="data.removeElement"
          :item-text="data.itemText"
          :index="data.index"
          :key="data.key"
        ></slot>
      </template>
      <template v-if="hasSlot('item')" v-slot:item="{ props, item }">
        <slot name="item" :item="item" :props="props"></slot>
      </template>
    </AutocompleteChips>
  </Field>
</template>

<script>
import AutocompleteChips from '../AutocompleteChips';
import FormBuilderElement from './FormBuilderElement';
import { Field } from 'vee-validate';

export default {
  name: 'AutocompleteChipsElement',

  components: {
    Field,
    AutocompleteChips,
  },

  mixins: [FormBuilderElement],

  props: {
    selectClasses: { required: false, default: '' },
  },

  methods: {
    hasSlot(name = 'default') {
      return !!this.$slots[name] || !!this.$slots[name];
    },
  },
};
</script>
