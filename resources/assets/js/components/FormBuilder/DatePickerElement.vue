<template>
  <Field
    :slim="true"
    :name="element.model"
    :rules="validations"
    v-slot="{ handleChange, errors }"
    :detectInput="false"
    ref="validationProvider"
    v-model="parentModel[element.model]"
  >
    <DatePicker
      v-model="parentModel[element.model]"
      :min="element.min"
      :max="element.max"
      @update:model-value="(event) => sendUpdate(event, handleChange)"
      v-bind="{ ...$attrs }"
      :error-messages="errors"
      :disabled="element.disabled"
      :label="element.label"
      :placeholder="element.placeholder"
      :class="inputClasses"
    />
  </Field>
</template>

<script>
import FormBuilderElement from './FormBuilderElement';
import { Field } from 'vee-validate';
import DatePicker from '../DatePicker.vue';
import { useDateFormat } from '../../composables/useDateFormat.js';

export default {
  name: 'DatePickerElement',
  components: { DatePicker, Field },
  mixins: [FormBuilderElement],
  props: ['inputClasses'],
  setup() {
    const { getFormattedDate } = useDateFormat();

    return { getFormattedDate };
  },
  data() {
    return {
      menuOpen: false,
      datePickerDate: null,
      displayDate: null,
    };
  },
  async mounted() {
    if (this.parentModel[this.element.model]) {
      this.$validator.reset({
        value: this.getFormattedDate(this.parentModel[this.element.model], 'YYYY-MM-DD'),
      });
    }
  },
};
</script>
