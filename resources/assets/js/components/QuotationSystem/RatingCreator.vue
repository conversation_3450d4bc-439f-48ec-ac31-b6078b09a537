<template>
  <div>
    <v-row>
      <v-col cols="12">
        <ListCrudComponent
          v-model:items="modelData"
          :empty-element="getEmptyElement()"
          :minimum="minimum"
          :maximum="maximum"
          @list-crud:remove="updateQuotationErrors"
          @update:items="updateQuotations"
        >
          <template v-slot:header>
            <v-container :fluid="true" class="py-0 px-0 mx-0">
              <v-row>
                <v-col cols="3">
                  <v-label>{{ $t('labels.label') }}</v-label>
                </v-col>
                <v-col cols="2">
                  <v-label>{{ $t('labels.quotation-creator.selection-color') }}</v-label>
                </v-col>
                <v-col cols="2">
                  <v-label>{{ $t('labels.quotation-creator.selection-icon') }}</v-label>
                </v-col>
                <v-col cols="2">
                  <v-label>{{ $t('labels.quotation-creator.background-color') }}</v-label>
                </v-col>
                <v-col cols="2">
                  <v-label>{{ $t('labels.quotation-creator.background-icon') }}</v-label>
                </v-col>
                <v-col cols="1"> </v-col>
              </v-row>
            </v-container>
          </template>

          <template v-slot="{ element }">
            <v-col cols="3">
              <InputElement
                :element="{
                  model: 'label',
                  name: getQuotationKey(element) + '-label',
                  label: $i18n.t('labels.label'),
                }"
                variant="solo"
                density="compact"
                validations="required|max:15"
                :parent-model="element"
                color="primary"
                @input="parentChange"
                :parent-errors="parentErrors"
                :single-line="true"
                flat
              >
              </InputElement>
            </v-col>
            <v-col cols="2" class="d-flex">
              <ColorPicker
                v-model:color="element.icon_color"
                full-width
                @change="handleChange"
              ></ColorPicker>
            </v-col>
            <v-col cols="2">
              <IconSelector
                v-model:selected-icon="element.icon"
                :show-amount="false"
                :show-color="false"
                :color="element.icon_color"
                :amount="1"
                :inline="true"
                :close-on-content-click="true"
                :input-name="getQuotationKey(element) + '-icon'"
                :validations="'required'"
                :parent-errors="parentErrors"
                :parent-change="parentChange"
                @change="handleChange"
                :icons="icons"
              ></IconSelector>
            </v-col>
            <v-col cols="2" class="d-flex">
              <ColorPicker
                v-model:color="element.empty_icon_color"
                full-width
                @change="handleChange"
              ></ColorPicker>
            </v-col>
            <v-col cols="2">
              <IconSelector
                v-model:selected-icon="element.empty_icon"
                :show-amount="false"
                :show-color="false"
                :color="element.empty_icon_color"
                :amount="1"
                :inline="true"
                :close-on-content-click="true"
                :input-name="getQuotationKey(element) + '-empty_icon'"
                :validations="'required'"
                :parent-errors="parentErrors"
                :parent-change="parentChange"
                @change="handleChange"
                :icons="icons"
              ></IconSelector>
            </v-col>
          </template>
        </ListCrudComponent>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12" class="pb-0">
        <h3 class="h-3">{{ $t('labels.preview') }}</h3>
      </v-col>
      <v-col cols="12 pt-0">
        <div class="d-flex align-center">
          <span class="mr-4">{{ ratingTypes[1].text }}</span>
          <span style="vertical-align: sub">
            <v-switch
              v-model="selectedRatingType"
              :true-value="ratingTypes[0]"
              :false-value="ratingTypes[1]"
              @update:model-value="updateRatingType"
              class="d-inline-block mt-1"
              hide-details
              :value-comparator="checkRatingTypeSwitchValue"
            ></v-switch>
          </span>
          <span class="ml-4">{{ ratingTypes[0].text }}</span>
        </div>
      </v-col>
    </v-row>
    <v-row class="pb-5">
      <v-col cols="12">
        <TmsRating
          v-if="!hasErrors"
          :modelValue="modelData"
          :rating-type="selectedRatingType"
        ></TmsRating>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import ColorPicker from '../ColorPicker';
import { events } from '../../events';
import IconSelector from '../IconSelector';
import InputElement from '../FormBuilder/InputElement';
import ListCrudComponent from '../../pages/follow-up-systems/ListCrudComponent';
import TmsRating from './TmsRating';
import PredefinedRatingSystems from './PredefinedRatingSystems';

export default {
  name: 'RatingCreator',

  components: {
    TmsRating,
    IconSelector,
    InputElement,
    ColorPicker,
    ListCrudComponent,
  },

  props: {
    modelValue: { required: true, type: Array, default: () => [] },
    minimum: { required: false, type: Number, default: 1 },
    maximum: { required: false, type: Number, maximum: 5 },
    parentErrors: { required: true },
    parentChange: { required: true },
    icons: { required: true },
    ratingType: { required: true, type: Object },
    ratingTypes: { required: true, type: Array },
  },

  data() {
    return {
      modelData: this.modelValue,
      predefinedRatings: PredefinedRatingSystems,
      selectedRatingType: this.ratingType,
    };
  },

  computed: {
    hasErrors() {
      return Object.keys(this.parentErrors).some((key) => {
        return this.parentErrors[key].length > 0;
      });
    },
  },

  methods: {
    getQuotationKey(quotationEntry) {
      return 'quotation' + quotationEntry.internalId;
    },
    handleChange() {
      this.$emit('change');
    },
    updateQuotationErrors() {
      if (this.hasErrors) {
        events.$emit('validateForm');
      }
    },
    updateQuotations() {
      this.$emit('update:quotations', this.modelData);
    },
    updateRatingType(ratingType) {
      this.selectedRatingType = ratingType;
      this.$emit('update:ratingType', this.selectedRatingType);
    },
    getEmptyElement() {
      return {
        label: 'rating_' + (this.modelData.length + 1),
        color: 'blue',
        icon: null,
        icon_count: this.modelData.length,
        icon_color: 'blue',
        empty_icon: null,
        empty_icon_color: 'blue-lighten-4',
        empty_icon_count: 0,
      };
    },
    checkRatingTypeSwitchValue(firstValue, secondValue) {
      return firstValue.value === secondValue.value;
    },
  },
};
</script>
