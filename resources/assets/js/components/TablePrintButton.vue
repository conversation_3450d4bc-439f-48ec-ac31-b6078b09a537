<template>
  <div class="inline-block">
    <v-btn
      v-if="fileReady"
      color="primary"
      icon="fa:fas fa-download"
      size="small"
      @click="openFile"
    ></v-btn>
    <v-btn
      v-else
      icon="print"
      size="small"
      variant="flat"
      @click="printTable"
      :loading="loading"
    ></v-btn>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'TablePrintButton',

  props: {
    title: { required: true, type: String },
    items: { required: true, type: Array },
    headers: { required: true, type: Array },
    headerGroups: { required: true, type: Array },
    secondaryHeaderGroups: { required: true, type: Array },
    url: { required: true, type: String },
    titleData: { required: true, type: String },
  },

  data() {
    return {
      loading: false,
      fileReady: false,
    };
  },

  methods: {
    printTable() {
      this.loading = true;
      axios
        .post(this.url, {
          title: this.title,
          titleData: this.titleData,
          items: this.items,
          headers: this.headers,
          headerGroups: this.headerGroups,
          secondaryHeaderGroups: this.secondaryHeaderGroups,
        })
        .then((response) => {
          this.loading = false;
          this.fileUrl = response.data;
          this.fileReady = true;
          this.openFile();
        });
    },
    openFile() {
      window.open(this.fileUrl);
    },
  },
};
</script>
