<template>
  <div>
    <Field
      :modelValue="selectedIcon"
      :slim="true"
      :name="inputName"
      :label="dataVvAs"
      :rules="validations"
      v-slot="{ errors }"
      ref="validationProvider"
      @vue:mounted="initValidationProvider"
    >
      <input type="hidden" :value="selectedIcon" />
      <v-container :fluid="true" class="mx-0">
        <v-row>
          <v-col class="pb-0 pt-1 flex-grow-0" v-if="!inline">
            <v-tooltip
              location="bottom"
              :disabled="selectedIconValue === null || selectedIconValue?.label === null"
            >
              <template v-slot:activator="{ props }">
                <div v-bind="props" v-if="selectedIconValue">
                  <v-icon
                    v-for="n in amountValue"
                    style="width: 25px; text-align: center"
                    :key="'icon' + n"
                    :color="colorValue"
                    :icon="selectedIconValue ? getIconHandle(selectedIconValue.handle) : ''"
                  />
                </div>
              </template>
              <span>{{ selectedIconValue ? selectedIconValue.label : '' }}</span>
            </v-tooltip>
          </v-col>
          <v-col class="py-0" :class="{ 'pa-0': inline }">
            <v-menu
              v-model="menuOpen"
              location="bottom"
              :close-on-content-click="closeOnContentClick"
            >
              <template v-slot:activator="{ props }">
                <v-btn v-if="inline" v-bind="props" :block="true" append-icon="keyboard_arrow_down">
                  <v-icon
                    v-for="n in amountValue"
                    style="width: 25px; text-align: center"
                    :key="'icon' + n"
                    :start="true"
                    :color="colorValue"
                    :icon="selectedIconValue ? getIconHandle(selectedIconValue.handle) : ''"
                  />
                </v-btn>
                <v-btn
                  v-else
                  v-bind="props"
                  color="primary"
                  class="ml-0"
                  :block="true"
                  :variant="variant"
                >
                  {{ $t('labels.icon-selector.select-icon') }}
                </v-btn>
              </template>
              <v-card>
                <v-card-text class="pa-3" v-if="menuOpen">
                  <v-row align="center">
                    <v-col cols="12" class="pb-0">
                      <v-text-field
                        v-model="search"
                        :label="$t('labels.search')"
                        :placeholder="$t('labels.search')"
                        variant="solo"
                        :autofocus="true"
                        @click.stop.prevent
                        @update:model-value="filterIcons"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12">
                      <v-virtual-scroll
                        :items="getItemRows(filteredIcons)"
                        height="200"
                        item-height="36"
                      >
                        <template v-slot:default="{ item }">
                          <v-btn
                            variant="text"
                            v-for="(icon, index) in item"
                            :key="index"
                            :class="isIconSelected(icon) ? 'bg-primary' : ''"
                            @click="setIcon(icon)"
                          >
                            <v-icon :icon="getIconHandle(icon.handle)" />
                          </v-btn>
                        </template>
                      </v-virtual-scroll>
                    </v-col>
                  </v-row>
                  <v-row justify="end">
                    <v-col cols="3" v-if="showAmount">
                      <v-select
                        class="mt-0"
                        hide-details
                        :items="[1, 2, 3, 4, 5]"
                        v-model="amountValue"
                      >
                      </v-select>
                    </v-col>
                    <v-col cols="3" v-if="showColor">
                      <ColorPicker v-model:color="colorValue"></ColorPicker>
                    </v-col>
                    <v-col cols="6" class="d-flex justify-end">
                      <v-btn class="mb-0" color="primary" @click="menuOpen = false">{{
                        $t('labels.close')
                      }}</v-btn>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-menu>
          </v-col>
          <v-col v-if="errors.length" cols="12" class="px-0 py-0">
            <div class="v-text-field__details">
              <div class="v-messages theme--light text-error">
                <div class="v-messages__wrapper">
                  {{ errors[0] || '' }}
                </div>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </Field>
  </div>
</template>

<script>
import { Field } from 'vee-validate';
import ColorPicker from './ColorPicker';
import lodash from 'lodash';
import { events } from '../events';
import { mapActions, mapState } from 'pinia';
import { useFormStore } from '../stores/FormStore';
import getIconHandle from '../helpers/getIconHandle.js';

export default {
  name: 'IconSelector',

  components: { Field, ColorPicker },

  props: {
    icons: { required: true, type: Array },
    selectedIcon: { required: true },
    color: { required: false, default: 'shades black' },
    amount: { required: false, default: 1 },
    showColor: { required: false, default: true },
    showAmount: { required: false, default: true },
    inputName: { type: String, required: false, default: 'icon' },
    closeOnContentClick: { type: Boolean, required: false, default: false },
    validations: { required: false, default: '' },
    parentErrors: { required: true },
    parentChange: { required: true },
    dataVvAs: {
      required: false,
      default: function () {
        return 'icon';
      },
    },
    inline: { required: false, type: Boolean, default: false },
    variant: { required: false, type: String, default: 'elevated' },
  },
  data() {
    return {
      filteredIcons: this.icons,
      menuOpen: false,
      selectedIconValue: this.selectedIcon,
      amountValue: this.amount,
      colorValue: this.color,
      search: '',
    };
  },

  computed: {
    ...mapState(useFormStore, ['validator', 'errors', 'fields']),
    ...mapState(useFormStore, { $parentValidator: 'validator' }),
    $validator() {
      return this.$refs.validationProvider;
    },
  },

  watch: {
    selectedIcon() {
      if (!this.selectedIcon) {
        this.setDefaultIcon();
        return;
      }
      this.selectedIconValue = this.selectedIcon;
    },
    colorValue() {
      this.$emit('update:color', this.colorValue);
      this.$emit('change');
    },
    color() {
      this.colorValue = this.color;
    },
    amountValue() {
      this.$emit('update:amount', this.amountValue);
      this.$emit('change');
    },
    amount() {
      this.amountValue = this.amount;
    },
  },

  mounted() {
    if (!this.selectedIcon) {
      this.setDefaultIcon();
    }
    if (!this.amount) {
      this.amountValue = 1;
    }
    if (!this.color) {
      this.colorValue = 'shades-black';
    }
    events.$on('validate', this.validateAll);
  },

  beforeUnmount() {
    this.search = '';
    events.$off('validate', this.validateAll);
  },

  methods: {
    ...mapActions(useFormStore, ['addField']),
    getIconHandle,
    filterIcons(search) {
      this.search = search;

      this.filteredIcons =
        search.length <= 0
          ? this.icons
          : lodash.filter(this.icons, (icon) => {
              return (
                icon &&
                icon.label !== null &&
                lodash.includes(icon.label.toLowerCase(), search.toLowerCase())
              );
            });
    },
    setIcon(icon) {
      this.selectedIconValue = this.isIconSelected(icon) ? null : icon;

      this.$emit('update:selectedIcon', this.selectedIconValue);
      this.$emit('change');
      this.$nextTick(function () {
        this.validateAll();
      });
    },
    setDefaultIcon() {
      this.selectedIconValue = {
        handle: 'fal fa-times',
        label: this.$i18n.t('labels.icon-selector.no-icon'),
      };
    },
    isIconSelected(icon) {
      if (this.selectedIconValue) {
        return this.selectedIconValue.handle === icon.handle;
      }
      return false;
    },
    validateAll() {
      // If the parent validator has no uid, it means it is not a Vue component.
      if (!this.$validator || !this.$parentValidator) {
        return null;
      }

      this.$parentValidator.validate();
    },
    getItemRows(items) {
      return lodash.chunk(items, 6);
    },
    initValidationProvider() {
      this.addField(this.inputName, this.$refs.validationProvider);
    },
  },
};
</script>

<style scoped lang="scss">
.v-card-text {
  width: 450px;
}

.v-menu__content {
  background: white;
}

:deep(.v-btn.v-btn--block) {
  .v-btn__append {
    position: absolute;
    right: 12px;
  }
}
</style>
