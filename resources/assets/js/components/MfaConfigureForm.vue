<template>
  <LogoutButton
    :label="$t('labels.mfa.configure.logout-button')"
    :before-logout="configureMfa"
  ></LogoutButton>
</template>

<script>
import axios from 'axios';
import LogoutButton from './LogoutButton';

export default {
  name: 'MfaConfigureForm',
  components: { LogoutButton },
  props: {
    configureUrl: {
      required: true,
      type: String,
    },
  },
  methods: {
    configureMfa: function () {
      return axios.post(this.configureUrl);
    },
  },
};
</script>
