<template>
  <span>
    {{ user.getFullName() }}
    <span v-for="(contextLevel, index) in calendarContextTree" :key="'context_' + index">
      &nbsp;&nbsp;&nbsp;&nbsp;
      <em
        v-if="
          contextLevel.contextType === 'MY_CONTEXT' || contextLevel.contextType === 'SYSTEM_CONTEXT'
        "
        >{{
          $t(
            'MODULE.CALENDARS.CALENDAR.CONTEXT.' +
              contextLevel.contextType +
              '.' +
              contextLevel.contextType
          )
        }}</em
      >
      <em v-else>{{ $t('MODULE.CALENDARS.CALENDAR.CONTEXT.' + contextLevel.contextType) }}</em>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <span
        v-for="(schoolLevel, schoolIndex) in contextLevel.calendarsBySchool"
        :key="'context_' + index + '_school_' + schoolIndex"
      >
        <span
          v-for="(calendar, calendarIndex) in getVisibleCalendars(schoolLevel.calendars)"
          :key="'context_' + index + '_school_' + schoolIndex + '_calendar_' + calendarIndex"
          style="white-space: nowrap"
        >
          {{ calendar.name
          }}{{ !index === getVisibleCalendars(schoolLevel.calendars).length - 1 ? ', ' : '' }}
        </span>
      </span>
    </span>
    <br />
    <span
      ><strong
        >{{ $t('MODULE.REPORTS.DASHBOARD.SCHOOLYEAR') }}: {{ schoolyear.description }}</strong
      ></span
    >
  </span>
</template>

<script>
import Schoolyear from '../../../../models/dataModels/Schoolyear';

export default {
  name: 'PlrCalendarPrintHeader',
  props: {
    user: {
      required: true,
      type: Object,
    },
    calendarContextTree: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      schoolyear: {},
    };
  },
  mounted() {
    Schoolyear.getCurrent().then((schoolyear) => {
      this.schoolyear = schoolyear;
    });
  },
  methods: {
    getVisibleCalendars(calendars) {
      return calendars.filter((calendar) => {
        return calendar.show === true;
      });
    },
  },
};
</script>
