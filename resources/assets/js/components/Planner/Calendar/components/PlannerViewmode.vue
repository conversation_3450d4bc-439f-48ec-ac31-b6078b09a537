<template>
  <v-btn-toggle
    v-model="selectedViewMode"
    density="compact"
    variant="outlined"
    divided
    selected-class="bg-primary text-white"
  >
    <v-btn
      v-for="(mode, index) in filteredViewModes"
      @click="changeViewMode(mode)"
      size="small"
      density="compact"
      variant="outlined"
      class="toolbarButton"
      :key="mode.name"
      :value="mode.name"
      :id="'calendarView' + index"
    >
      {{ $t('MODULE.CALENDARS.CALENDAR.PLANNER_MODE.' + mode.name) }}
    </v-btn>
  </v-btn-toggle>
</template>

<script>
import PlannerViewMode from '../../../../models/dataModels/PlannerViewMode';

export default {
  name: 'PlannerViewmode',
  props: {
    viewMode: {
      required: true,
      type: Object,
    },
    groupsAvailable: {
      required: true,
      type: Boolean,
    },
  },
  data() {
    return {
      viewModes: PlannerViewMode.ALL,
      stateParams: null,
      selectedViewMode: null,
    };
  },
  watch: {
    viewMode: {
      immediate: true,
      deep: true,
      handler() {
        this.selectedViewMode = this.viewMode.name;
      },
    },
  },
  mounted() {
    this.updateStateParams();
  },
  computed: {
    filteredViewModes() {
      return this.viewModes.filter((mode) => {
        return mode.name !== 'MY_GROUPS' || this.groupsAvailable;
      });
    },
  },
  methods: {
    changeViewMode(mode) {
      this.updateStateParams();
      this.stateParams.set('plannerMode', mode.name);
      history.replaceState(null, null, '?' + this.stateParams.toString());

      this.$emit('update:viewMode', mode);
      this.$emit('update');
    },
    updateStateParams() {
      this.stateParams = new URLSearchParams(window.location.search);
    },
  },
};
</script>

<style lang="scss" scoped>
.v-btn-group--density-compact {
  height: 28px;
}

:deep(.v-btn--active:hover::before),
:deep(.v-btn--active::before) {
  opacity: 0;
}
</style>
