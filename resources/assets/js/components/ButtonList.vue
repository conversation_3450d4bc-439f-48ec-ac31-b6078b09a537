<template>
  <div class="button-list">
    <ListTitle v-if="this.title">{{ title }}</ListTitle>
    <v-card>
      <v-list class="pt-0 pb-0" :lines="hasSubtitle ? 'two' : 'one'">
        <slot></slot>
      </v-list>
    </v-card>
  </div>
</template>

<script>
import ListTitle from './ListTitle';

export default {
  name: 'ButtonList',

  components: { ListTitle },

  props: {
    title: {
      type: String,
      required: false,
    },
    hasSubtitle: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
};
</script>

<style lang="scss">
.button-list {
  .v-list-item-text {
    display: block;
    text-overflow: ellipsis;
  }
}
</style>
