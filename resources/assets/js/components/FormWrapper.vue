<template>
  <Form
    v-slot="{ handleSubmit, failed, errors }"
    ref="validationObserver"
    tag="div"
    style="width: 100%"
    @vue:mounted="initValidationProvider"
  >
    <v-form @submit.prevent>
      <LeavePageWarning :show-message="showLeaveWarning"></LeavePageWarning>
      <slot
        :parent-model="modelData"
        :parent-fields="formFields"
        :parent-errors="errors"
        :parent-change="parseFrontendErrors"
        :parent-file-change="fileChange"
        :sending="sending"
        :linked-fields="linkedFields"
      ></slot>
      <slot
        name="action-buttons"
        v-if="renderButtons"
        :validate-form="() => handleSubmit(validateAndSubmitForm)"
        :cancel-event="cancelEvent"
        :validationFailed="failed"
        :errors="errors"
      >
        <v-card-actions class="ml-3 flex-row-reverse">
          <v-btn
            color="primary"
            type="submit"
            :disabled="failed"
            @click.prevent="handleSubmit(validateAndSubmitForm)"
          >
            {{ submitText || $t('labels.save') }}
          </v-btn>
          <v-btn
            color="primary"
            class="mx-2"
            variant="text"
            v-if="showCancel"
            @click.prevent="cancelEvent"
          >
            {{ cancelText || $t('labels.cancel') }}
          </v-btn>
        </v-card-actions>
      </slot>
    </v-form>
  </Form>
</template>

<script>
import FormBuilder from '../mixins/FormBuilder';
import LeavePageWarning from './LeavePageWarning';
import lodashIsEqual from 'lodash/isEqual';
import lodashIntersection from 'lodash/intersection';
import { Form } from 'vee-validate';
import { mapActions, mapState } from 'pinia';
import { useFormStore } from '../stores/FormStore.js';

export default {
  name: 'FormWrapper',

  components: { Form, LeavePageWarning },

  mixins: [FormBuilder],

  data() {
    return {
      formFields: {},
      originalValues: null,
    };
  },

  props: {
    renderButtons: { required: false, default: true, type: Boolean },
    submitText: { type: String, default: null },
    cancelText: { type: String, default: null },
    showCancelButton: { type: Boolean, default: false },
  },

  computed: {
    ...mapState(useFormStore, { formErrors: 'errors' }),
    showLeaveWarning() {
      return (
        this.preventPageChange &&
        this.mappedFormFields.some((item) => item.dirty && item.valueChanged)
      );
    },
    showCancel() {
      return this.showCancelButton || this.cancelUrl;
    },
    mappedFormFields() {
      if (!this.originalValues) {
        return [];
      }

      return Object.keys(this.formFields).map((key) => {
        return {
          key: key,
          dirty: this.formFields[key].meta.dirty,
          valueChanged: this.isValueDifferent(this.modelData[key], this.originalValues[key]),
        };
      });
    },
  },

  watch: {
    formErrors() {
      this.updateErrors(this.formErrors);
    },
  },

  async mounted() {
    await this.$nextTick();
    this.originalValues = JSON.parse(JSON.stringify(this.modelData));
  },

  methods: {
    ...mapActions(useFormStore, ['setValidator']),
    isValueDifferent(value1, value2) {
      if (value1 === undefined || value2 === undefined || value1 === null || value2 === null) {
        return true;
      }
      // make sure we don't receive observables
      value1 = JSON.parse(JSON.stringify(value1));
      value2 = JSON.parse(JSON.stringify(value2));

      if (!value1 && !value2) {
        return false;
      }

      if (typeof value1 === typeof value2 && typeof value1 === 'object') {
        if (Array.isArray(value1)) {
          return !lodashIsEqual(value1, value2);
        }

        const duplicates = lodashIntersection(Object.keys(value1), Object.keys(value2));
        return !duplicates.some((key) => value1[key] === value2[key]);
      }

      if (typeof value1 === typeof value2 && typeof value1 === 'string') {
        value1 = value1.trim();
        value2 = value2.trim();
      }

      return JSON.stringify(value1) !== JSON.stringify(value2);
    },
    initValidationProvider() {
      this.setValidator(this.$refs.validationObserver);
    },
  },
};
</script>
