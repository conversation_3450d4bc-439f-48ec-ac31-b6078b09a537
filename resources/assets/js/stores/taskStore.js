import { defineStore } from 'pinia';
import TaskPriorityEnum from '../models/enums/TaskPriority';
import TaskStatusEnum from '../models/enums/TaskStatus';
import { i18n } from '../plugins/vue-i18n';
import axios from 'axios';
import { useDateFormat } from '../composables/useDateFormat.js';

const getErrorNotification = function (error) {
  const defaultMessage = { color: 'error' };
  if (
    !error ||
    !error.response ||
    !error.response.data ||
    !error.response.data.errors ||
    error.response.data.errors.length === 0
  ) {
    return {
      ...defaultMessage,
      text: i18n.t('errors.default.title'),
    };
  }

  const errors = Object.values(error.response.data.errors);

  if (errors.length === 1) {
    return {
      ...defaultMessage,
      text: capitalizeString(errors[0][0]),
    };
  }

  return {
    ...defaultMessage,
    list: errors.map((error) => error[0]),
  };
};

const capitalizeString = function (string) {
  return string.charAt(0).toUpperCase() + string.substring(1);
};

export const useTaskStore = defineStore('taskStore', {
  state() {
    return {
      tasks: [],
      baseUrl: '',
      showTaskDrawer: false,
      taskDrawerStatusOptions: {
        SHOW: 'show',
        EDIT: 'edit',
        CREATE: 'create',
        PATCH: 'patch',
      },
      priorityOptions: TaskPriorityEnum,
      priorityIcons: {
        1: {
          icon: 'warning',
          color: 'error',
        },
        2: {
          icon: 'priority_high',
          color: 'warning',
        },
        3: {
          icon: 'info',
          color: 'green',
        },
      },
      statusOptions: TaskStatusEnum,
      statusIcons: {
        1: {
          icon: 'timer',
        },
        2: {
          icon: 'update',
        },
        3: {
          icon: 'done',
        },
      },
      taskDrawerStatus: 'show',
      selectedTask: null,
      defaultTask: {
        title: '',
        description: '',
        follow_up: '',
        priority: TaskPriorityEnum.NORMAL.value,
        status: TaskStatusEnum.OPEN.value,
        attachments: [],
        deadline: null,
        care_input_id: null,
        users: [],
      },
      care_input_id: null,
      infinityIdentifier: +Date.now(),
    };
  },
  actions: {
    getTasksWithAjax(request) {
      return axios.get(request.url, request.options).then((response) => {
        this.addTasks(response.data.data);
        return response;
      });
    },
    createTask() {
      return axios.post(this.baseUrl, this.getDataForRequest(this.selectedTask)).then(
        (response) => {
          this.addTask(response.data);
        },
        (error) => {
          this.$notify(getErrorNotification(error));
        }
      );
    },
    updateTask() {
      return axios
        .post(this.baseUrl + '/' + this.selectedTask.uid, this.getDataForRequest(this.selectedTask))
        .then(
          (response) => {
            this.replaceTaskContent(response.data);
            this.showDrawer(false);
          },
          (error) => {
            this.$notify(getErrorNotification(error));
          }
        );
    },
    patchTask() {
      return axios
        .patch(
          this.baseUrl + '/' + this.selectedTask.uid,
          this.getDataForRequest(this.selectedTask)
        )
        .then(
          (response) => {
            this.replaceTaskContent(response.data);
            this.showDrawer(false);
          },
          (error) => {
            this.$notify(getErrorNotification(error));
          }
        );
    },
    deleteTask({ task, index }) {
      return axios.delete(this.baseUrl + '/' + task.uid).then(
        () => {
          this.deleteTaskInStore(index);
        },
        (error) => {
          this.$notify(getErrorNotification(error));
        }
      );
    },
    resetFilters() {
      this.clearTasks();
    },
    setBaseUrl(baseUrl) {
      this.baseUrl = baseUrl;
    },
    setCareInputId(careInputId) {
      this.care_input_id = careInputId;
      this.defaultTask.care_input_id = careInputId;
    },
    setUserData(user) {
      this.defaultTask.users = [user];
    },
    setTasks(tasks) {
      this.tasks = tasks;
    },
    addTasks(tasks) {
      this.tasks = this.tasks.concat(tasks);
    },
    addTask(task) {
      this.tasks.unshift(Object.assign({}, this.selectedTask, task));
      this.showDrawer(false);
    },
    replaceTaskContent(data) {
      const taskIndex = this.tasks.findIndex((task) => task.uid === data.uid);
      Object.assign(this.tasks[taskIndex], data);
    },
    editTask(taskIndex) {
      this.taskDrawerStatus = this.taskDrawerStatusOptions.EDIT;
      this.selectTask(taskIndex);
    },
    createTaskInStore() {
      this.taskDrawerStatus = this.taskDrawerStatusOptions.CREATE;
      this.selectedTask = JSON.parse(JSON.stringify(this.defaultTask));
      this.showDrawer(true);
    },
    showTask(taskIndex) {
      this.taskDrawerStatus = this.taskDrawerStatusOptions.SHOW;
      this.selectTask(taskIndex);
    },
    selectTask(taskIndex) {
      this.beforeEditing = Object.assign({}, this.tasks[taskIndex]);
      this.selectedTask = this.tasks[taskIndex];
      this.showDrawer(true);
    },
    cancelTask() {
      Object.assign(this.selectedTask, this.beforeEditing);
      this.showDrawer(false);
    },
    showDrawer(showDrawer) {
      this.showTaskDrawer = showDrawer;
    },
    patchTaskInStore(taskIndex) {
      this.taskDrawerStatus = this.taskDrawerStatusOptions.PATCH;
      this.selectTask(taskIndex);
    },
    clearTasks() {
      this.tasks = [];
    },
    resetInfiniteScroll() {
      this.infinityIdentifier += 1;
    },
    deleteTaskInStore(taskIndex) {
      this.tasks.splice(taskIndex, 1);
    },
    getDataForRequest(taskData) {
      const { getFormattedDate } = useDateFormat();
      return {
        ...taskData,
        deadline: getFormattedDate(taskData.deadline),
      };
    },
  },
  getters: {
    getDefaultTask() {
      return JSON.parse(JSON.stringify(this.defaultTask));
    },
  },
  persist: false,
});
